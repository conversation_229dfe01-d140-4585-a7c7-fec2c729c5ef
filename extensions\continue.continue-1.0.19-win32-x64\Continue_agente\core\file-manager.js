/**
 * File Manager - Gerenciador de Arquivos e Pastas
 * Sistema completo para manipulação de arquivos no sistema
 */

const vscode = require('vscode');
const fs = require('fs').promises;
const path = require('path');

class FileManager {
    constructor() {
        this.isInitialized = false;
        this.backupSystem = new Map();
        this.operationHistory = [];
    }

    async initialize() {
        if (this.isInitialized) return;

        console.log('🔧 Inicializando Gerenciador de Arquivos...');
        
        this.setupBackupSystem();
        
        this.isInitialized = true;
        console.log('✅ Gerenciador de Arquivos inicializado');
    }

    setupBackupSystem() {
        // Sistema de backup automático antes de edições
        this.backupSystem.set('maxBackups', 10);
        this.backupSystem.set('backupDir', path.join(__dirname, '..', 'backups'));
        this.ensureBackupDirectory();
    }

    async ensureBackupDirectory() {
        try {
            const backupDir = this.backupSystem.get('backupDir');
            await fs.mkdir(backupDir, { recursive: true });
        } catch (error) {
            console.error('Erro ao criar diretório de backup:', error);
        }
    }

    // Operações básicas de arquivo
    async readFile(filePath) {
        try {
            const absolutePath = this.resolveFilePath(filePath);
            const content = await fs.readFile(absolutePath, 'utf8');
            
            this.logOperation('read', filePath, { success: true });
            
            return {
                success: true,
                content,
                path: absolutePath,
                size: content.length,
                lines: content.split('\n').length
            };
        } catch (error) {
            this.logOperation('read', filePath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: filePath
            };
        }
    }

    async writeFile(filePath, content, options = {}) {
        try {
            const absolutePath = this.resolveFilePath(filePath);
            
            // Criar backup se arquivo existe
            if (await this.fileExists(absolutePath)) {
                await this.createBackup(absolutePath);
            }

            // Criar diretório se não existe
            const dir = path.dirname(absolutePath);
            await fs.mkdir(dir, { recursive: true });

            // Escrever arquivo
            await fs.writeFile(absolutePath, content, 'utf8');
            
            this.logOperation('write', filePath, { 
                success: true, 
                size: content.length,
                backup: await this.fileExists(absolutePath)
            });

            return {
                success: true,
                path: absolutePath,
                size: content.length,
                lines: content.split('\n').length
            };
        } catch (error) {
            this.logOperation('write', filePath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: filePath
            };
        }
    }

    async appendFile(filePath, content) {
        try {
            const absolutePath = this.resolveFilePath(filePath);
            
            // Criar backup se arquivo existe
            if (await this.fileExists(absolutePath)) {
                await this.createBackup(absolutePath);
            }

            await fs.appendFile(absolutePath, content, 'utf8');
            
            this.logOperation('append', filePath, { success: true, size: content.length });

            return {
                success: true,
                path: absolutePath,
                appendedSize: content.length
            };
        } catch (error) {
            this.logOperation('append', filePath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: filePath
            };
        }
    }

    async deleteFile(filePath) {
        try {
            const absolutePath = this.resolveFilePath(filePath);
            
            // Criar backup antes de deletar
            await this.createBackup(absolutePath);
            
            await fs.unlink(absolutePath);
            
            this.logOperation('delete', filePath, { success: true });

            return {
                success: true,
                path: absolutePath,
                backed_up: true
            };
        } catch (error) {
            this.logOperation('delete', filePath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: filePath
            };
        }
    }

    async copyFile(sourcePath, destPath) {
        try {
            const absoluteSource = this.resolveFilePath(sourcePath);
            const absoluteDest = this.resolveFilePath(destPath);
            
            // Criar diretório de destino se não existe
            const destDir = path.dirname(absoluteDest);
            await fs.mkdir(destDir, { recursive: true });

            await fs.copyFile(absoluteSource, absoluteDest);
            
            this.logOperation('copy', sourcePath, { 
                success: true, 
                destination: destPath 
            });

            return {
                success: true,
                source: absoluteSource,
                destination: absoluteDest
            };
        } catch (error) {
            this.logOperation('copy', sourcePath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                source: sourcePath,
                destination: destPath
            };
        }
    }

    async moveFile(sourcePath, destPath) {
        try {
            const absoluteSource = this.resolveFilePath(sourcePath);
            const absoluteDest = this.resolveFilePath(destPath);
            
            // Criar backup do arquivo original
            await this.createBackup(absoluteSource);
            
            // Criar diretório de destino se não existe
            const destDir = path.dirname(absoluteDest);
            await fs.mkdir(destDir, { recursive: true });

            await fs.rename(absoluteSource, absoluteDest);
            
            this.logOperation('move', sourcePath, { 
                success: true, 
                destination: destPath 
            });

            return {
                success: true,
                source: absoluteSource,
                destination: absoluteDest
            };
        } catch (error) {
            this.logOperation('move', sourcePath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                source: sourcePath,
                destination: destPath
            };
        }
    }

    // Operações de diretório
    async createDirectory(dirPath) {
        try {
            const absolutePath = this.resolveFilePath(dirPath);
            await fs.mkdir(absolutePath, { recursive: true });
            
            this.logOperation('mkdir', dirPath, { success: true });

            return {
                success: true,
                path: absolutePath
            };
        } catch (error) {
            this.logOperation('mkdir', dirPath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: dirPath
            };
        }
    }

    async deleteDirectory(dirPath, recursive = false) {
        try {
            const absolutePath = this.resolveFilePath(dirPath);
            
            // Criar backup do diretório se contém arquivos importantes
            if (recursive) {
                await this.createDirectoryBackup(absolutePath);
            }
            
            await fs.rmdir(absolutePath, { recursive });
            
            this.logOperation('rmdir', dirPath, { success: true, recursive });

            return {
                success: true,
                path: absolutePath,
                recursive
            };
        } catch (error) {
            this.logOperation('rmdir', dirPath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: dirPath
            };
        }
    }

    async listDirectory(dirPath, options = {}) {
        try {
            const absolutePath = this.resolveFilePath(dirPath);
            const entries = await fs.readdir(absolutePath, { withFileTypes: true });
            
            const files = [];
            const directories = [];
            
            for (const entry of entries) {
                const fullPath = path.join(absolutePath, entry.name);
                const stats = await fs.stat(fullPath);
                
                const item = {
                    name: entry.name,
                    path: fullPath,
                    relativePath: path.relative(this.getWorkspaceRoot(), fullPath),
                    size: stats.size,
                    modified: stats.mtime,
                    created: stats.birthtime
                };

                if (entry.isDirectory()) {
                    directories.push(item);
                } else {
                    files.push(item);
                }
            }

            this.logOperation('list', dirPath, { success: true, count: entries.length });

            return {
                success: true,
                path: absolutePath,
                files,
                directories,
                total: entries.length
            };
        } catch (error) {
            this.logOperation('list', dirPath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: dirPath
            };
        }
    }

    // Operações avançadas de edição
    async replaceInFile(filePath, searchPattern, replacement, options = {}) {
        try {
            const fileResult = await this.readFile(filePath);
            if (!fileResult.success) {
                return fileResult;
            }

            let content = fileResult.content;
            let replacements = 0;

            if (options.regex) {
                const regex = new RegExp(searchPattern, options.flags || 'g');
                const matches = content.match(regex);
                replacements = matches ? matches.length : 0;
                content = content.replace(regex, replacement);
            } else {
                const originalLength = content.length;
                content = content.split(searchPattern).join(replacement);
                replacements = (originalLength - content.length) / (searchPattern.length - replacement.length);
            }

            const writeResult = await this.writeFile(filePath, content);
            
            this.logOperation('replace', filePath, { 
                success: writeResult.success, 
                replacements,
                pattern: searchPattern
            });

            return {
                ...writeResult,
                replacements,
                pattern: searchPattern,
                replacement
            };
        } catch (error) {
            this.logOperation('replace', filePath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: filePath
            };
        }
    }

    async insertAtLine(filePath, lineNumber, content) {
        try {
            const fileResult = await this.readFile(filePath);
            if (!fileResult.success) {
                return fileResult;
            }

            const lines = fileResult.content.split('\n');
            
            // Inserir na linha especificada (1-based)
            lines.splice(lineNumber - 1, 0, content);
            
            const newContent = lines.join('\n');
            const writeResult = await this.writeFile(filePath, newContent);
            
            this.logOperation('insert', filePath, { 
                success: writeResult.success, 
                line: lineNumber,
                content: content.substring(0, 50) + '...'
            });

            return {
                ...writeResult,
                insertedAt: lineNumber,
                insertedContent: content
            };
        } catch (error) {
            this.logOperation('insert', filePath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: filePath
            };
        }
    }

    async replaceLines(filePath, startLine, endLine, newContent) {
        try {
            const fileResult = await this.readFile(filePath);
            if (!fileResult.success) {
                return fileResult;
            }

            const lines = fileResult.content.split('\n');
            
            // Substituir linhas (1-based)
            const newLines = newContent.split('\n');
            lines.splice(startLine - 1, endLine - startLine + 1, ...newLines);
            
            const content = lines.join('\n');
            const writeResult = await this.writeFile(filePath, content);
            
            this.logOperation('replaceLines', filePath, { 
                success: writeResult.success, 
                startLine,
                endLine,
                newLines: newLines.length
            });

            return {
                ...writeResult,
                replacedLines: { start: startLine, end: endLine },
                newLines: newLines.length
            };
        } catch (error) {
            this.logOperation('replaceLines', filePath, { success: false, error: error.message });
            return {
                success: false,
                error: error.message,
                path: filePath
            };
        }
    }

    // Sistema de backup
    async createBackup(filePath) {
        try {
            if (!await this.fileExists(filePath)) {
                return { success: false, reason: 'File does not exist' };
            }

            const backupDir = this.backupSystem.get('backupDir');
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const fileName = path.basename(filePath);
            const backupName = `${fileName}.${timestamp}.backup`;
            const backupPath = path.join(backupDir, backupName);

            await fs.copyFile(filePath, backupPath);

            // Limpar backups antigos
            await this.cleanOldBackups(fileName);

            return {
                success: true,
                backupPath,
                originalPath: filePath
            };
        } catch (error) {
            console.error('Erro ao criar backup:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async createDirectoryBackup(dirPath) {
        try {
            const backupDir = this.backupSystem.get('backupDir');
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const dirName = path.basename(dirPath);
            const backupName = `${dirName}.${timestamp}.backup`;
            const backupPath = path.join(backupDir, backupName);

            await this.copyDirectory(dirPath, backupPath);

            return {
                success: true,
                backupPath,
                originalPath: dirPath
            };
        } catch (error) {
            console.error('Erro ao criar backup do diretório:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async copyDirectory(source, destination) {
        await fs.mkdir(destination, { recursive: true });
        const entries = await fs.readdir(source, { withFileTypes: true });

        for (const entry of entries) {
            const srcPath = path.join(source, entry.name);
            const destPath = path.join(destination, entry.name);

            if (entry.isDirectory()) {
                await this.copyDirectory(srcPath, destPath);
            } else {
                await fs.copyFile(srcPath, destPath);
            }
        }
    }

    async cleanOldBackups(fileName) {
        try {
            const backupDir = this.backupSystem.get('backupDir');
            const maxBackups = this.backupSystem.get('maxBackups');
            
            const entries = await fs.readdir(backupDir);
            const backups = entries
                .filter(entry => entry.startsWith(fileName) && entry.endsWith('.backup'))
                .map(entry => ({
                    name: entry,
                    path: path.join(backupDir, entry),
                    stats: null
                }));

            // Obter estatísticas dos arquivos
            for (const backup of backups) {
                backup.stats = await fs.stat(backup.path);
            }

            // Ordenar por data de modificação (mais recente primeiro)
            backups.sort((a, b) => b.stats.mtime - a.stats.mtime);

            // Remover backups antigos
            if (backups.length > maxBackups) {
                const toDelete = backups.slice(maxBackups);
                for (const backup of toDelete) {
                    await fs.unlink(backup.path);
                }
            }
        } catch (error) {
            console.error('Erro ao limpar backups antigos:', error);
        }
    }

    // Utilitários
    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    async getFileStats(filePath) {
        try {
            const absolutePath = this.resolveFilePath(filePath);
            const stats = await fs.stat(absolutePath);
            
            return {
                success: true,
                path: absolutePath,
                size: stats.size,
                modified: stats.mtime,
                created: stats.birthtime,
                isFile: stats.isFile(),
                isDirectory: stats.isDirectory()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                path: filePath
            };
        }
    }

    resolveFilePath(filePath) {
        if (path.isAbsolute(filePath)) {
            return filePath;
        }
        
        const workspaceRoot = this.getWorkspaceRoot();
        return path.resolve(workspaceRoot, filePath);
    }

    getWorkspaceRoot() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return workspaceFolders[0].uri.fsPath;
        }
        return process.cwd();
    }

    logOperation(operation, filePath, details) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            operation,
            filePath,
            details
        };

        this.operationHistory.push(logEntry);
        
        // Manter apenas os últimos 100 registros
        if (this.operationHistory.length > 100) {
            this.operationHistory.shift();
        }

        console.log(`FileManager: ${operation} ${filePath}`, details);
    }

    getOperationHistory() {
        return this.operationHistory.slice(); // Retornar cópia
    }

    clearHistory() {
        this.operationHistory = [];
    }

    // Operações em lote
    async batchOperation(operations) {
        const results = [];
        
        for (const operation of operations) {
            try {
                let result;
                
                switch (operation.type) {
                    case 'read':
                        result = await this.readFile(operation.path);
                        break;
                    case 'write':
                        result = await this.writeFile(operation.path, operation.content);
                        break;
                    case 'delete':
                        result = await this.deleteFile(operation.path);
                        break;
                    case 'copy':
                        result = await this.copyFile(operation.source, operation.destination);
                        break;
                    case 'move':
                        result = await this.moveFile(operation.source, operation.destination);
                        break;
                    default:
                        result = { success: false, error: `Unknown operation: ${operation.type}` };
                }
                
                results.push({
                    operation,
                    result
                });
            } catch (error) {
                results.push({
                    operation,
                    result: { success: false, error: error.message }
                });
            }
        }

        return {
            success: true,
            operations: operations.length,
            results,
            successful: results.filter(r => r.result.success).length,
            failed: results.filter(r => !r.result.success).length
        };
    }
}

module.exports = FileManager;
