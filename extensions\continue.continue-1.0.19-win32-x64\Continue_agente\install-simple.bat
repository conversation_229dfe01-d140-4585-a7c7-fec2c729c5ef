@echo off
echo 🚀 Instalando Continue Agent...
echo.

REM Verificar se Node.js está instalado
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js não encontrado
    echo 📦 Baixando e instalando Node.js...
    
    REM Baixar Node.js
    powershell -Command "Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile 'nodejs-installer.msi'"
    
    if exist nodejs-installer.msi (
        echo 📦 Instalando Node.js...
        msiexec /i nodejs-installer.msi /quiet /norestart
        
        REM Aguardar instalação
        timeout /t 30 /nobreak >nul
        
        REM Limpar arquivo temporário
        del nodejs-installer.msi
        
        echo ✅ Node.js instalado
    ) else (
        echo ❌ Erro ao baixar Node.js
        echo 💡 Instale manualmente: https://nodejs.org
        pause
        exit /b 1
    )
) else (
    echo ✅ Node.js já está instalado
    node --version
)

echo.
echo 🔧 Integrando Continue Agent...

REM Navegar para o diretório do agente
cd /d "%~dp0"

REM Executar integração
node integrate-agent.js

if %errorlevel% equ 0 (
    echo.
    echo 🎉 CONTINUE AGENT INSTALADO COM SUCESSO!
    echo.
    echo 📋 Próximos passos:
    echo    1. Reinicie o VSCode completamente
    echo    2. Abra o Continue ^(Ctrl+Shift+L^)
    echo    3. Verifique se o botão 'Agent' está ativo
    echo    4. Use comandos como /edit, /web-search, etc.
    echo.
    echo 🤖 O Continue Agent está pronto para uso!
) else (
    echo ❌ Erro na integração do agente
    echo 💡 Verifique se o LM Studio está rodando na porta 1234
)

echo.
echo 📊 Para verificar o status: node integrate-agent.js --status
echo.
pause
