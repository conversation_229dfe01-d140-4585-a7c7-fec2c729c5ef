/**
 * Task Manager - Gerenciador de Tarefas Avançado
 * Sistema de planejamento e acompanhamento de tarefas
 */

const { v4: uuidv4 } = require('crypto');

class TaskManager {
    constructor() {
        this.tasks = new Map();
        this.taskHierarchy = new Map();
        this.currentSession = null;
        this.isInitialized = false;
    }

    async initialize() {
        if (this.isInitialized) return;

        console.log('🔧 Inicializando Gerenciador de Tarefas...');
        
        this.createRootTask();
        
        this.isInitialized = true;
        console.log('✅ Gerenciador de Tarefas inicializado');
    }

    createRootTask() {
        const rootTask = {
            id: 'root-task',
            name: 'Continue_agente Session',
            description: 'Sessão principal do Continue_agente',
            state: 'IN_PROGRESS',
            parent: null,
            children: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.tasks.set('root-task', rootTask);
        this.currentSession = 'root-task';
    }

    generateTaskId() {
        // Gerar UUID simples para compatibilidade
        return 'task-' + Math.random().toString(36).substr(2, 16);
    }

    async viewTaskList() {
        const markdown = this.generateTaskListMarkdown();
        return {
            type: 'task_list',
            markdown,
            totalTasks: this.tasks.size,
            completedTasks: this.getTasksByState('COMPLETE').length,
            inProgressTasks: this.getTasksByState('IN_PROGRESS').length
        };
    }

    generateTaskListMarkdown() {
        const rootTask = this.tasks.get('root-task');
        if (!rootTask) return '# Nenhuma tarefa encontrada';

        let markdown = `# ${rootTask.name}\n\n`;
        markdown += this.renderTaskHierarchy(rootTask, 0);
        
        return markdown;
    }

    renderTaskHierarchy(task, level) {
        const indent = '  '.repeat(level);
        const stateSymbol = this.getStateSymbol(task.state);
        
        let markdown = `${indent}- ${stateSymbol} UUID:${task.id} NAME:${task.name}`;
        if (task.description) {
            markdown += ` DESCRIPTION:${task.description}`;
        }
        markdown += '\n';

        // Renderizar filhos
        if (task.children && task.children.length > 0) {
            for (const childId of task.children) {
                const childTask = this.tasks.get(childId);
                if (childTask) {
                    markdown += this.renderTaskHierarchy(childTask, level + 1);
                }
            }
        }

        return markdown;
    }

    getStateSymbol(state) {
        const symbols = {
            'NOT_STARTED': '[ ]',
            'IN_PROGRESS': '[/]',
            'COMPLETE': '[x]',
            'CANCELLED': '[-]'
        };
        return symbols[state] || '[ ]';
    }

    async addTasks(tasksData) {
        const createdTasks = [];
        
        for (const taskData of tasksData) {
            const task = {
                id: this.generateTaskId(),
                name: taskData.name,
                description: taskData.description,
                state: taskData.state || 'NOT_STARTED',
                parent: taskData.parent_task_id || 'root-task',
                children: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // Adicionar à estrutura
            this.tasks.set(task.id, task);
            
            // Atualizar hierarquia
            const parentTask = this.tasks.get(task.parent);
            if (parentTask) {
                if (!parentTask.children) {
                    parentTask.children = [];
                }
                
                if (taskData.after_task_id) {
                    // Inserir após tarefa específica
                    const afterIndex = parentTask.children.indexOf(taskData.after_task_id);
                    if (afterIndex !== -1) {
                        parentTask.children.splice(afterIndex + 1, 0, task.id);
                    } else {
                        parentTask.children.push(task.id);
                    }
                } else {
                    parentTask.children.push(task.id);
                }
                
                parentTask.updatedAt = new Date().toISOString();
            }

            createdTasks.push(task);
        }

        return {
            type: 'tasks_created',
            created: createdTasks.length,
            tasks: createdTasks
        };
    }

    async updateTasks(tasksData) {
        const updatedTasks = [];
        
        for (const taskUpdate of tasksData) {
            const task = this.tasks.get(taskUpdate.task_id);
            if (!task) {
                console.warn(`Tarefa não encontrada: ${taskUpdate.task_id}`);
                continue;
            }

            // Atualizar propriedades
            if (taskUpdate.name !== undefined) {
                task.name = taskUpdate.name;
            }
            if (taskUpdate.description !== undefined) {
                task.description = taskUpdate.description;
            }
            if (taskUpdate.state !== undefined) {
                task.state = taskUpdate.state;
            }

            task.updatedAt = new Date().toISOString();
            updatedTasks.push(task);
        }

        return {
            type: 'tasks_updated',
            updated: updatedTasks.length,
            tasks: updatedTasks
        };
    }

    async reorganizeTaskList(markdown) {
        try {
            // Parse do markdown para reconstruir a estrutura
            const newStructure = this.parseTaskListMarkdown(markdown);
            
            // Limpar estrutura atual (exceto root)
            const rootTask = this.tasks.get('root-task');
            this.tasks.clear();
            this.tasks.set('root-task', rootTask);
            
            // Reconstruir estrutura
            this.buildTaskStructure(newStructure);
            
            return {
                type: 'tasks_reorganized',
                totalTasks: this.tasks.size
            };
            
        } catch (error) {
            console.error('Erro ao reorganizar tarefas:', error);
            throw new Error('Falha ao reorganizar lista de tarefas');
        }
    }

    parseTaskListMarkdown(markdown) {
        const lines = markdown.split('\n');
        const structure = [];
        const stack = [];
        
        for (const line of lines) {
            if (!line.trim() || line.startsWith('#')) continue;
            
            const match = line.match(/^(\s*)-\s*(\[.\])\s*UUID:([^\s]+)\s*NAME:([^D]*?)(?:\s*DESCRIPTION:(.*))?$/);
            if (!match) continue;
            
            const [, indent, stateSymbol, uuid, name, description] = match;
            const level = Math.floor(indent.length / 2);
            const state = this.parseStateSymbol(stateSymbol);
            
            const taskData = {
                id: uuid === 'NEW_UUID' ? this.generateTaskId() : uuid,
                name: name.trim(),
                description: description ? description.trim() : '',
                state,
                level,
                children: []
            };
            
            // Ajustar stack para o nível atual
            while (stack.length > level) {
                stack.pop();
            }
            
            // Adicionar como filho do pai atual
            if (stack.length > 0) {
                const parent = stack[stack.length - 1];
                parent.children.push(taskData);
                taskData.parent = parent.id;
            } else {
                taskData.parent = 'root-task';
            }
            
            stack.push(taskData);
            structure.push(taskData);
        }
        
        return structure;
    }

    parseStateSymbol(symbol) {
        const stateMap = {
            '[ ]': 'NOT_STARTED',
            '[/]': 'IN_PROGRESS',
            '[x]': 'COMPLETE',
            '[-]': 'CANCELLED'
        };
        return stateMap[symbol] || 'NOT_STARTED';
    }

    buildTaskStructure(structure) {
        const rootTask = this.tasks.get('root-task');
        rootTask.children = [];
        
        for (const taskData of structure) {
            const task = {
                id: taskData.id,
                name: taskData.name,
                description: taskData.description,
                state: taskData.state,
                parent: taskData.parent || 'root-task',
                children: taskData.children.map(child => child.id),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            this.tasks.set(task.id, task);
            
            // Atualizar hierarquia do pai
            if (task.parent === 'root-task') {
                rootTask.children.push(task.id);
            }
        }
    }

    getTasksByState(state) {
        return Array.from(this.tasks.values()).filter(task => task.state === state);
    }

    getTaskById(taskId) {
        return this.tasks.get(taskId);
    }

    getTaskChildren(taskId) {
        const task = this.tasks.get(taskId);
        if (!task || !task.children) return [];
        
        return task.children.map(childId => this.tasks.get(childId)).filter(Boolean);
    }

    getTaskPath(taskId) {
        const path = [];
        let currentTask = this.tasks.get(taskId);
        
        while (currentTask && currentTask.id !== 'root-task') {
            path.unshift(currentTask);
            currentTask = this.tasks.get(currentTask.parent);
        }
        
        return path;
    }

    calculateProgress() {
        const allTasks = Array.from(this.tasks.values()).filter(task => task.id !== 'root-task');
        const completed = allTasks.filter(task => task.state === 'COMPLETE').length;
        const total = allTasks.length;
        
        return {
            completed,
            total,
            percentage: total > 0 ? Math.round((completed / total) * 100) : 0,
            inProgress: allTasks.filter(task => task.state === 'IN_PROGRESS').length,
            notStarted: allTasks.filter(task => task.state === 'NOT_STARTED').length,
            cancelled: allTasks.filter(task => task.state === 'CANCELLED').length
        };
    }

    getTaskStatistics() {
        const progress = this.calculateProgress();
        const rootTask = this.tasks.get('root-task');
        
        return {
            sessionName: rootTask ? rootTask.name : 'Sessão Desconhecida',
            progress,
            totalTasks: this.tasks.size - 1, // Excluir root task
            createdAt: rootTask ? rootTask.createdAt : null,
            lastUpdated: rootTask ? rootTask.updatedAt : null
        };
    }

    exportTasks() {
        return {
            version: '1.0',
            exportedAt: new Date().toISOString(),
            tasks: Array.from(this.tasks.entries()).map(([id, task]) => ({
                id,
                ...task
            }))
        };
    }

    importTasks(data) {
        if (!data.tasks || !Array.isArray(data.tasks)) {
            throw new Error('Formato de dados inválido para importação');
        }

        this.tasks.clear();
        
        for (const taskData of data.tasks) {
            const { id, ...task } = taskData;
            this.tasks.set(id, task);
        }

        // Verificar se existe root task
        if (!this.tasks.has('root-task')) {
            this.createRootTask();
        }
    }

    clearAllTasks() {
        this.tasks.clear();
        this.createRootTask();
    }

    // Métodos de conveniência para integração com VSCode
    async createQuickTask(name, description = '') {
        const taskData = [{
            name,
            description,
            state: 'NOT_STARTED'
        }];
        
        return await this.addTasks(taskData);
    }

    async markTaskComplete(taskId) {
        return await this.updateTasks([{
            task_id: taskId,
            state: 'COMPLETE'
        }]);
    }

    async markTaskInProgress(taskId) {
        return await this.updateTasks([{
            task_id: taskId,
            state: 'IN_PROGRESS'
        }]);
    }

    async getNextTask() {
        const notStartedTasks = this.getTasksByState('NOT_STARTED');
        return notStartedTasks.length > 0 ? notStartedTasks[0] : null;
    }

    async getCurrentTasks() {
        return this.getTasksByState('IN_PROGRESS');
    }
}

module.exports = TaskManager;
