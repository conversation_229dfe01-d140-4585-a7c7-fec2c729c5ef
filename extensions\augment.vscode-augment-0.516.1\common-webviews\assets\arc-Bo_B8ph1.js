import{L as dt,M as lt,N as f,O as Tt,P as $,Q as T,R as rt,S as ft,T as N,V as xt,W,X as o,Y as gt,Z as At,$ as Rt}from"./AugmentMessage-C8cOeLWa.js";function Pt(i){return i.innerRadius}function Qt(i){return i.outerRadius}function Lt(i){return i.startAngle}function Mt(i){return i.endAngle}function Nt(i){return i&&i.padAngle}function D(i,O,b,j,P,Q,_){var e=i-b,F=O-j,c=(_?Q:-Q)/W(e*e+F*F),n=c*F,A=-c*e,u=i+n,a=O+A,y=b+n,l=j+A,Z=(u+y)/2,s=(a+l)/2,t=y-u,r=l-a,m=t*t+r*r,h=P-Q,x=u*l-y*a,v=(r<0?-1:1)*W(Rt(0,h*h*m-x*x)),L=(x*r-t*v)/m,M=(-x*t-r*v)/m,k=(x*r+t*v)/m,B=(-x*t+r*v)/m,d=L-Z,g=M-s,p=k-Z,C=B-s;return d*d+g*g>p*p+C*C&&(L=k,M=B),{cx:L,cy:M,x01:-n,y01:-A,x11:L*(P/h-1),y11:M*(P/h-1)}}function jt(){var i=Pt,O=Qt,b=N(0),j=null,P=Lt,Q=Mt,_=Nt,e=null,F=dt(c);function c(){var n,A,u=+i.apply(this,arguments),a=+O.apply(this,arguments),y=P.apply(this,arguments)-lt,l=Q.apply(this,arguments)-lt,Z=ft(l-y),s=l>y;if(e||(e=n=F()),a<u&&(A=a,a=u,u=A),a>f)if(Z>Tt-f)e.moveTo(a*$(y),a*T(y)),e.arc(0,0,a,y,l,!s),u>f&&(e.moveTo(u*$(l),u*T(l)),e.arc(0,0,u,l,y,s));else{var t,r,m=y,h=l,x=y,v=l,L=Z,M=Z,k=_.apply(this,arguments)/2,B=k>f&&(j?+j.apply(this,arguments):W(u*u+a*a)),d=rt(ft(a-u)/2,+b.apply(this,arguments)),g=d,p=d;if(B>f){var C=gt(B/u*T(k)),E=gt(B/a*T(k));(L-=2*C)>f?(x+=C*=s?1:-1,v-=C):(L=0,x=v=(y+l)/2),(M-=2*E)>f?(m+=E*=s?1:-1,h-=E):(M=0,m=h=(y+l)/2)}var K=a*$(m),V=a*T(m),I=u*$(v),J=u*T(v);if(d>f){var R,X=a*$(h),Y=a*T(h),q=u*$(x),w=u*T(x);if(Z<xt)if(R=function(tt,nt,pt,mt,at,ct,ht,vt){var st=pt-tt,it=mt-nt,ot=ht-at,yt=vt-ct,G=yt*st-ot*it;if(!(G*G<f))return[tt+(G=(ot*(nt-ct)-yt*(tt-at))/G)*st,nt+G*it]}(K,V,q,w,X,Y,I,J)){var z=K-R[0],H=V-R[1],U=X-R[0],S=Y-R[1],et=1/T(At((z*U+H*S)/(W(z*z+H*H)*W(U*U+S*S)))/2),ut=W(R[0]*R[0]+R[1]*R[1]);g=rt(d,(u-ut)/(et-1)),p=rt(d,(a-ut)/(et+1))}else g=p=0}M>f?p>f?(t=D(q,w,K,V,a,p,s),r=D(X,Y,I,J,a,p,s),e.moveTo(t.cx+t.x01,t.cy+t.y01),p<d?e.arc(t.cx,t.cy,p,o(t.y01,t.x01),o(r.y01,r.x01),!s):(e.arc(t.cx,t.cy,p,o(t.y01,t.x01),o(t.y11,t.x11),!s),e.arc(0,0,a,o(t.cy+t.y11,t.cx+t.x11),o(r.cy+r.y11,r.cx+r.x11),!s),e.arc(r.cx,r.cy,p,o(r.y11,r.x11),o(r.y01,r.x01),!s))):(e.moveTo(K,V),e.arc(0,0,a,m,h,!s)):e.moveTo(K,V),u>f&&L>f?g>f?(t=D(I,J,X,Y,u,-g,s),r=D(K,V,q,w,u,-g,s),e.lineTo(t.cx+t.x01,t.cy+t.y01),g<d?e.arc(t.cx,t.cy,g,o(t.y01,t.x01),o(r.y01,r.x01),!s):(e.arc(t.cx,t.cy,g,o(t.y01,t.x01),o(t.y11,t.x11),!s),e.arc(0,0,u,o(t.cy+t.y11,t.cx+t.x11),o(r.cy+r.y11,r.cx+r.x11),s),e.arc(r.cx,r.cy,g,o(r.y11,r.x11),o(r.y01,r.x01),!s))):e.arc(0,0,u,v,x,s):e.lineTo(I,J)}else e.moveTo(0,0);if(e.closePath(),n)return e=null,n+""||null}return c.centroid=function(){var n=(+i.apply(this,arguments)+ +O.apply(this,arguments))/2,A=(+P.apply(this,arguments)+ +Q.apply(this,arguments))/2-xt/2;return[$(A)*n,T(A)*n]},c.innerRadius=function(n){return arguments.length?(i=typeof n=="function"?n:N(+n),c):i},c.outerRadius=function(n){return arguments.length?(O=typeof n=="function"?n:N(+n),c):O},c.cornerRadius=function(n){return arguments.length?(b=typeof n=="function"?n:N(+n),c):b},c.padRadius=function(n){return arguments.length?(j=n==null?null:typeof n=="function"?n:N(+n),c):j},c.startAngle=function(n){return arguments.length?(P=typeof n=="function"?n:N(+n),c):P},c.endAngle=function(n){return arguments.length?(Q=typeof n=="function"?n:N(+n),c):Q},c.padAngle=function(n){return arguments.length?(_=typeof n=="function"?n:N(+n),c):_},c.context=function(n){return arguments.length?(e=n??null,c):e},c}export{jt as d};
