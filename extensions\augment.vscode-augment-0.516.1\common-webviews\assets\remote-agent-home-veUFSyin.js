import{S as j,i as V,s as X,a as $e,n as Q,d as m,b as fe,g as Ge,u as je,v as Ve,w as Xe,x as Ye,f as Je,H as Ke,j as pe,W as D,D as _,t as u,q as g,c as f,E as x,e as q,F as R,G as y,V as I,h as S,af as Le,X as B,Y as T,N,o as F,p as P,$ as K,y as ae,al as Ue,R as We,an as Qe,z as we,ah as Ze}from"./SpinnerAugment-VfHtkDdv.js";import"./design-system-init-BQpWKoxZ.js";/* empty css                                */import{e as O,u as Y,o as J,I as de,h as et}from"./IconButtonAugment-BlRCK7lJ.js";import{M as tt}from"./message-broker-DxXjuHCW.js";import{S as ge,a as nt,T as Be,b as st,c as rt,d as at,v as lt,e as ot}from"./StatusIndicator-C8wcdhmo.js";import{R as C,s as it,a as ce}from"./index-B528snJk.js";import{T as se,a as re,C as ct}from"./CardAugment-CMpdst0l.js";import{C as $t}from"./CalloutAugment-jvmj3vIU.js";import{E as dt}from"./exclamation-triangle-BgK0UWCq.js";import{c as gt,s as ut,R as le}from"./remote-agents-client-zf3VV9pT.js";import{A as mt}from"./augment-logo-DHqqkJ4i.js";import"./async-messaging-Cm1y2LK7.js";import"./index-PzkfeRvH.js";import"./types-CGlLNakm.js";function ft(o){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},o[0]],a={};for(let r=0;r<t.length;r+=1)a=$e(a,t[r]);return{c(){e=Je("svg"),n=new Ke(!0),this.h()},l(r){e=Ve(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var l=Xe(e);n=Ye(l,!0),l.forEach(m),this.h()},h(){n.a=null,fe(e,a)},m(r,l){je(r,e,l),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',e)},p(r,[l]){fe(e,a=Ge(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&l&&r[0]]))},i:Q,o:Q,d(r){r&&m(e)}}}function pt(o,e,n){return o.$$set=t=>{n(0,e=$e($e({},e),pe(t)))},[e=pe(e)]}class wt extends j{constructor(e){super(),V(this,e,pt,ft,X,{})}}function vt(o){let e,n=o[0]?"Running in the cloud":"Running locally";return{c(){e=T(n)},m(t,a){f(t,e,a)},p(t,a){1&a&&n!==(n=t[0]?"Running in the cloud":"Running locally")&&B(e,n)},d(t){t&&m(e)}}}function ht(o){let e;return{c(){e=T("Unknown time")},m(n,t){f(n,e,t)},p:Q,d(n){n&&m(e)}}}function _t(o){let e;return{c(){e=T(o[3])},m(n,t){f(n,e,t)},p(n,t){8&t&&B(e,n[3])},d(n){n&&m(e)}}}function xt(o){let e,n,t,a=o[1]===C.agentRunning?"Last updated":"Started";function r($,s){return $[2]?_t:ht}let l=r(o),i=l(o);return{c(){e=T(a),n=I(),i.c(),t=N()},m($,s){f($,e,s),f($,n,s),i.m($,s),f($,t,s)},p($,s){2&s&&a!==(a=$[1]===C.agentRunning?"Last updated":"Started")&&B(e,a),l===(l=r($))&&i?i.p($,s):(i.d(1),i=l($),i&&(i.c(),i.m(t.parentNode,t)))},d($){$&&(m(e),m(n),m(t)),i.d($)}}}function yt(o){let e,n,t,a,r,l;return n=new D({props:{size:1,color:"secondary",class:"location-text",$$slots:{default:[vt]},$$scope:{ctx:o}}}),r=new D({props:{size:1,color:"secondary",class:"time-text",$$slots:{default:[xt]},$$scope:{ctx:o}}}),{c(){e=R("div"),y(n.$$.fragment),t=I(),a=R("div"),y(r.$$.fragment),S(a,"class","time-container"),S(e,"class","agent-card-footer svelte-1qwlkoj")},m(i,$){f(i,e,$),x(n,e,null),q(e,t),q(e,a),x(r,a,null),l=!0},p(i,[$]){const s={};33&$&&(s.$$scope={dirty:$,ctx:i}),n.$set(s);const c={};46&$&&(c.$$scope={dirty:$,ctx:i}),r.$set(c)},i(i){l||(g(n.$$.fragment,i),g(r.$$.fragment,i),l=!0)},o(i){u(n.$$.fragment,i),u(r.$$.fragment,i),l=!1},d(i){i&&m(e),_(n),_(r)}}}function kt(o,e,n){let{isRemote:t=!1}=e,{status:a}=e,{timestamp:r}=e,l=gt(r);const i=ut(r,$=>{n(3,l=$)});return Le(()=>{i()}),o.$$set=$=>{"isRemote"in $&&n(0,t=$.isRemote),"status"in $&&n(1,a=$.status),"timestamp"in $&&n(2,r=$.timestamp)},[t,a,r,l]}class St extends j{constructor(e){super(),V(this,e,kt,yt,X,{isRemote:0,status:1,timestamp:2})}}function At(o){let e;return{c(){e=T(o[0])},m(n,t){f(n,e,t)},p(n,t){1&t&&B(e,n[0])},d(n){n&&m(e)}}}function bt(o){let e,n,t;return n=new D({props:{size:1,color:"secondary",$$slots:{default:[At]},$$scope:{ctx:o}}}),{c(){e=R("div"),y(n.$$.fragment),S(e,"class","task-text-container svelte-1tatwxk")},m(a,r){f(a,e,r),x(n,e,null),t=!0},p(a,r){const l={};9&r&&(l.$$scope={dirty:r,ctx:a}),n.$set(l)},i(a){t||(g(n.$$.fragment,a),t=!0)},o(a){u(n.$$.fragment,a),t=!1},d(a){a&&m(e),_(n)}}}function ve(o){let e,n,t;return n=new D({props:{size:1,color:o[1]==="error"?"error":"neutral",$$slots:{default:[Rt]},$$scope:{ctx:o}}}),{c(){e=R("div"),y(n.$$.fragment),S(e,"class","task-status-indicator svelte-1tatwxk")},m(a,r){f(a,e,r),x(n,e,null),t=!0},p(a,r){const l={};2&r&&(l.color=a[1]==="error"?"error":"neutral"),10&r&&(l.$$scope={dirty:r,ctx:a}),n.$set(l)},i(a){t||(g(n.$$.fragment,a),t=!0)},o(a){u(n.$$.fragment,a),t=!1},d(a){a&&m(e),_(n)}}}function Rt(o){let e,n=o[1]==="error"?"!":o[1]==="warning"?"⚠":"";return{c(){e=T(n)},m(t,a){f(t,e,a)},p(t,a){2&a&&n!==(n=t[1]==="error"?"!":t[1]==="warning"?"⚠":"")&&B(e,n)},d(t){t&&m(e)}}}function It(o){let e,n,t,a,r,l,i;r=new se({props:{content:o[0],triggerOn:[re.Hover],maxWidth:"400px",$$slots:{default:[bt]},$$scope:{ctx:o}}});let $=(o[1]==="error"||o[1]==="warning")&&ve(o);return{c(){e=R("div"),n=R("div"),a=I(),y(r.$$.fragment),l=I(),$&&$.c(),S(n,"class",t="bullet-point "+o[2]+" svelte-1tatwxk"),S(e,"class","task-item svelte-1tatwxk")},m(s,c){f(s,e,c),q(e,n),q(e,a),x(r,e,null),q(e,l),$&&$.m(e,null),i=!0},p(s,[c]){(!i||4&c&&t!==(t="bullet-point "+s[2]+" svelte-1tatwxk"))&&S(n,"class",t);const d={};1&c&&(d.content=s[0]),9&c&&(d.$$scope={dirty:c,ctx:s}),r.$set(d),s[1]==="error"||s[1]==="warning"?$?($.p(s,c),2&c&&g($,1)):($=ve(s),$.c(),g($,1),$.m(e,null)):$&&(F(),u($,1,1,()=>{$=null}),P())},i(s){i||(g(r.$$.fragment,s),g($),i=!0)},o(s){u(r.$$.fragment,s),u($),i=!1},d(s){s&&m(e),_(r),$&&$.d()}}}function zt(o,e,n){let t,{text:a}=e,{status:r="info"}=e;return o.$$set=l=>{"text"in l&&n(0,a=l.text),"status"in l&&n(1,r=l.status)},o.$$.update=()=>{2&o.$$.dirty&&n(2,t=function(l){switch(l){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(r))},[a,r,t]}class Ft extends j{constructor(e){super(),V(this,e,zt,It,X,{text:0,status:1})}}function he(o,e,n){const t=o.slice();return t[24]=e[n],t[26]=n,t}function _e(o){let e,n,t;return n=new $t({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[qt],default:[Pt]},$$scope:{ctx:o}}}),{c(){e=R("div"),y(n.$$.fragment),S(e,"class","deletion-error svelte-1bxdvw4")},m(a,r){f(a,e,r),x(n,e,null),t=!0},p(a,r){const l={};134217744&r&&(l.$$scope={dirty:r,ctx:a}),n.$set(l)},i(a){t||(g(n.$$.fragment,a),t=!0)},o(a){u(n.$$.fragment,a),t=!1},d(a){a&&m(e),_(n)}}}function Pt(o){let e,n,t,a,r;return{c(){e=T(o[4]),n=I(),t=R("button"),t.textContent="×",S(t,"class","error-dismiss svelte-1bxdvw4"),S(t,"aria-label","Dismiss error")},m(l,i){f(l,e,i),f(l,n,i),f(l,t,i),a||(r=We(t,"click",o[11]),a=!0)},p(l,i){16&i&&B(e,l[4])},d(l){l&&(m(e),m(n),m(t)),a=!1,r()}}}function qt(o){let e,n;return e=new dt({props:{slot:"icon"}}),{c(){y(e.$$.fragment)},m(t,a){x(e,t,a),n=!0},p:Q,i(t){n||(g(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Ht(o){let e,n;return e=new D({props:{size:2,weight:"medium",class:"session-text",$$slots:{default:[Tt]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,a){x(e,t,a),n=!0},p(t,a){const r={};134217729&a&&(r.$$scope={dirty:a,ctx:t}),e.$set(r)},i(t){n||(g(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Ot(o){let e,n,t,a,r,l;return t=new Be({}),r=new D({props:{size:2,weight:"medium",$$slots:{default:[Mt]},$$scope:{ctx:o}}}),{c(){e=R("div"),n=R("div"),y(t.$$.fragment),a=I(),y(r.$$.fragment),S(n,"class","setup-script-badge svelte-1bxdvw4"),S(e,"class","setup-script-title-container svelte-1bxdvw4")},m(i,$){f(i,e,$),q(e,n),x(t,n,null),q(e,a),x(r,e,null),l=!0},p(i,$){const s={};134217728&$&&(s.$$scope={dirty:$,ctx:i}),r.$set(s)},i(i){l||(g(t.$$.fragment,i),g(r.$$.fragment,i),l=!0)},o(i){u(t.$$.fragment,i),u(r.$$.fragment,i),l=!1},d(i){i&&m(e),_(t),_(r)}}}function Tt(o){let e,n=o[0].session_summary+"";return{c(){e=T(n)},m(t,a){f(t,e,a)},p(t,a){1&a&&n!==(n=t[0].session_summary+"")&&B(e,n)},d(t){t&&m(e)}}}function Mt(o){let e;return{c(){e=R("span"),e.textContent="Generate a setup script",S(e,"class","setup-script-title svelte-1bxdvw4")},m(n,t){f(n,e,t)},p:Q,d(n){n&&m(e)}}}function xe(o){let e,n,t=[],a=new Map,r=O(o[8].slice(0,3));const l=i=>i[26];for(let i=0;i<r.length;i+=1){let $=he(o,r,i),s=l($);a.set(s,t[i]=ye(s,$))}return{c(){e=R("div");for(let i=0;i<t.length;i+=1)t[i].c();S(e,"class","tasks-list svelte-1bxdvw4")},m(i,$){f(i,e,$);for(let s=0;s<t.length;s+=1)t[s]&&t[s].m(e,null);n=!0},p(i,$){256&$&&(r=O(i[8].slice(0,3)),F(),t=Y(t,$,l,1,i,r,a,e,J,ye,null,he),P())},i(i){if(!n){for(let $=0;$<r.length;$+=1)g(t[$]);n=!0}},o(i){for(let $=0;$<t.length;$+=1)u(t[$]);n=!1},d(i){i&&m(e);for(let $=0;$<t.length;$+=1)t[$].d()}}}function ye(o,e){let n,t,a;return t=new Ft({props:{text:e[24],status:"success"}}),{key:o,first:null,c(){n=N(),y(t.$$.fragment),this.first=n},m(r,l){f(r,n,l),x(t,r,l),a=!0},p(r,l){e=r;const i={};256&l&&(i.text=e[24]),t.$set(i)},i(r){a||(g(t.$$.fragment,r),a=!0)},o(r){u(t.$$.fragment,r),a=!1},d(r){r&&m(n),_(t,r)}}}function Et(o){let e,n;return e=new rt({}),{c(){y(e.$$.fragment)},m(t,a){x(e,t,a),n=!0},i(t){n||(g(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Nt(o){let e,n;return e=new wt({}),{c(){y(e.$$.fragment)},m(t,a){x(e,t,a),n=!0},i(t){n||(g(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Ct(o){let e,n,t,a;const r=[Nt,Et],l=[];function i($,s){return $[7]?0:1}return e=i(o),n=l[e]=r[e](o),{c(){n.c(),t=N()},m($,s){l[e].m($,s),f($,t,s),a=!0},p($,s){let c=e;e=i($),e!==c&&(F(),u(l[c],1,1,()=>{l[c]=null}),P(),n=l[e],n||(n=l[e]=r[e]($),n.c()),g(n,1),n.m(t.parentNode,t))},i($){a||(g(n),a=!0)},o($){u(n),a=!1},d($){$&&m(t),l[e].d($)}}}function Dt(o){let e,n;return e=new de({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Ct]},$$scope:{ctx:o}}}),e.$on("click",o[16]),{c(){y(e.$$.fragment)},m(t,a){x(e,t,a),n=!0},p(t,a){const r={};134217856&a&&(r.$$scope={dirty:a,ctx:t}),e.$set(r)},i(t){n||(g(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Lt(o){let e,n;return e=new Be({}),{c(){y(e.$$.fragment)},m(t,a){x(e,t,a),n=!0},i(t){n||(g(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Ut(o){let e,n;return e=new de({props:{disabled:!o[5],variant:"ghost",color:"neutral",size:1,title:o[5]?"SSH to agent":"SSH to agent (agent must be running or idle)",$$slots:{default:[Lt]},$$scope:{ctx:o}}}),e.$on("click",o[17]),{c(){y(e.$$.fragment)},m(t,a){x(e,t,a),n=!0},p(t,a){const r={};32&a&&(r.disabled=!t[5]),32&a&&(r.title=t[5]?"SSH to agent":"SSH to agent (agent must be running or idle)"),134217728&a&&(r.$$scope={dirty:a,ctx:t}),e.$set(r)},i(t){n||(g(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Wt(o){let e,n;return e=new st({}),{c(){y(e.$$.fragment)},m(t,a){x(e,t,a),n=!0},i(t){n||(g(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Bt(o){let e,n;return e=new de({props:{variant:"ghost",color:"neutral",size:1,disabled:o[3],title:o[3]?"Deleting agent...":"Delete agent",$$slots:{default:[Wt]},$$scope:{ctx:o}}}),e.$on("click",o[18]),{c(){y(e.$$.fragment)},m(t,a){x(e,t,a),n=!0},p(t,a){const r={};8&a&&(r.disabled=t[3]),8&a&&(r.title=t[3]?"Deleting agent...":"Delete agent"),134217728&a&&(r.$$scope={dirty:a,ctx:t}),e.$set(r)},i(t){n||(g(e.$$.fragment,t),n=!0)},o(t){u(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function Gt(o){let e,n,t,a,r,l,i,$,s,c,d,h,w,v,k,G,M,L,E,U;const b=[Ot,Ht],H=[];function W(p,A){return p[0].is_setup_script_agent?0:1}t=W(o),a=H[t]=b[t](o),$=new nt({props:{status:o[0].status,workspaceStatus:o[0].workspace_status,isExpanded:!0,hasUpdates:o[0].has_updates}});let z=o[8].length>0&&xe(o);return w=new se({props:{content:o[7]?"Unpin agent":"Pin agent",triggerOn:[re.Hover],side:"top",$$slots:{default:[Dt]},$$scope:{ctx:o}}}),k=new se({props:{content:"SSH to agent",triggerOn:[re.Hover],side:"top",$$slots:{default:[Ut]},$$scope:{ctx:o}}}),M=new se({props:{content:"Delete agent",triggerOn:[re.Hover],side:"top",$$slots:{default:[Bt]},$$scope:{ctx:o}}}),E=new St({props:{isRemote:o[6],status:o[0].status,timestamp:o[0].updated_at||o[0].started_at}}),{c(){e=R("div"),n=R("div"),a.c(),l=I(),i=R("div"),y($.$$.fragment),s=I(),c=R("div"),z&&z.c(),d=I(),h=R("div"),y(w.$$.fragment),v=I(),y(k.$$.fragment),G=I(),y(M.$$.fragment),L=I(),y(E.$$.fragment),S(n,"class","session-summary-container svelte-1bxdvw4"),S(n,"title",r=o[0].is_setup_script_agent?"Generate a setup script":o[0].session_summary),S(i,"class","card-info"),S(e,"class","card-header svelte-1bxdvw4"),S(c,"class","card-content svelte-1bxdvw4"),S(h,"class","card-actions svelte-1bxdvw4")},m(p,A){f(p,e,A),q(e,n),H[t].m(n,null),q(e,l),q(e,i),x($,i,null),f(p,s,A),f(p,c,A),z&&z.m(c,null),f(p,d,A),f(p,h,A),x(w,h,null),q(h,v),x(k,h,null),q(h,G),x(M,h,null),f(p,L,A),x(E,p,A),U=!0},p(p,A){let oe=t;t=W(p),t===oe?H[t].p(p,A):(F(),u(H[oe],1,1,()=>{H[oe]=null}),P(),a=H[t],a?a.p(p,A):(a=H[t]=b[t](p),a.c()),g(a,1),a.m(n,null)),(!U||1&A&&r!==(r=p[0].is_setup_script_agent?"Generate a setup script":p[0].session_summary))&&S(n,"title",r);const te={};1&A&&(te.status=p[0].status),1&A&&(te.workspaceStatus=p[0].workspace_status),1&A&&(te.hasUpdates=p[0].has_updates),$.$set(te),p[8].length>0?z?(z.p(p,A),256&A&&g(z,1)):(z=xe(p),z.c(),g(z,1),z.m(c,null)):z&&(F(),u(z,1,1,()=>{z=null}),P());const ie={};128&A&&(ie.content=p[7]?"Unpin agent":"Pin agent"),134217857&A&&(ie.$$scope={dirty:A,ctx:p}),w.$set(ie);const ue={};134217760&A&&(ue.$$scope={dirty:A,ctx:p}),k.$set(ue);const me={};134217737&A&&(me.$$scope={dirty:A,ctx:p}),M.$set(me);const ne={};64&A&&(ne.isRemote=p[6]),1&A&&(ne.status=p[0].status),1&A&&(ne.timestamp=p[0].updated_at||p[0].started_at),E.$set(ne)},i(p){U||(g(a),g($.$$.fragment,p),g(z),g(w.$$.fragment,p),g(k.$$.fragment,p),g(M.$$.fragment,p),g(E.$$.fragment,p),U=!0)},o(p){u(a),u($.$$.fragment,p),u(z),u(w.$$.fragment,p),u(k.$$.fragment,p),u(M.$$.fragment,p),u(E.$$.fragment,p),U=!1},d(p){p&&(m(e),m(s),m(c),m(d),m(h),m(L)),H[t].d(),_($),z&&z.d(),_(w),_(k),_(M),_(E,p)}}}function jt(o){let e,n,t,a,r=o[4]&&_e(o);return t=new ct({props:{variant:"surface",size:2,interactive:!0,class:"agent-card",$$slots:{default:[Gt]},$$scope:{ctx:o}}}),t.$on("click",o[19]),t.$on("keydown",o[20]),{c(){e=R("div"),r&&r.c(),n=I(),y(t.$$.fragment),S(e,"class","card-wrapper svelte-1bxdvw4"),K(e,"selected-card",o[1]),K(e,"setup-script-card",o[0].is_setup_script_agent),K(e,"deleting",o[3])},m(l,i){f(l,e,i),r&&r.m(e,null),q(e,n),x(t,e,null),a=!0},p(l,[i]){l[4]?r?(r.p(l,i),16&i&&g(r,1)):(r=_e(l),r.c(),g(r,1),r.m(e,n)):r&&(F(),u(r,1,1,()=>{r=null}),P());const $={};134218217&i&&($.$$scope={dirty:i,ctx:l}),t.$set($),(!a||2&i)&&K(e,"selected-card",l[1]),(!a||1&i)&&K(e,"setup-script-card",l[0].is_setup_script_agent),(!a||8&i)&&K(e,"deleting",l[3])},i(l){a||(g(r),g(t.$$.fragment,l),a=!0)},o(l){u(r),u(t.$$.fragment,l),a=!1},d(l){l&&m(e),r&&r.d(),_(t)}}}function Vt(o,e,n){let t,a,r,l,i,$,{agent:s}=e,{selected:c=!1}=e,{onSelect:d}=e;const h=ae(le.key),w=ae(ge);Ue(o,w,b=>n(15,$=b));let v=!1,k=null,G=null;async function M(b){var W,z;L(),n(3,v=!0);const H=((W=$.state)==null?void 0:W.agentOverviews)||[];try{if(!await h.deleteRemoteAgent(b))throw new Error("Failed to delete agent");if(w.update(p=>{if(p)return{...p,agentOverviews:p.agentOverviews.filter(A=>A.remote_agent_id!==b)}}),(((z=$.state)==null?void 0:z.pinnedAgents)||{})[b])try{await h.deletePinnedAgentFromStore(b);const p=await h.getPinnedAgentsFromStore();w.update(A=>{if(A)return{...A,pinnedAgents:p}})}catch(p){console.error("Failed to remove pinned status:",p)}}catch(p){console.error("Failed to delete agent:",p),w.update(A=>{if(A)return{...A,agentOverviews:H}}),n(4,k=p instanceof Error?p.message:"Failed to delete agent"),G=setTimeout(()=>{L()},5e3)}finally{n(3,v=!1)}}function L(){n(4,k=null),G&&(clearTimeout(G),G=null)}async function E(b){try{r?await h.deletePinnedAgentFromStore(b):await h.savePinnedAgentToStore(b,!0);const H=await h.getPinnedAgentsFromStore();w.update(W=>{if(W)return{...W,pinnedAgents:H}})}catch(H){console.error("Failed to toggle pinned status:",H)}}function U(){l&&(async b=>{await h.sshToRemoteAgent(b.remote_agent_id)})(s)}return Le(()=>{L()}),o.$$set=b=>{"agent"in b&&n(0,s=b.agent),"selected"in b&&n(1,c=b.selected),"onSelect"in b&&n(2,d=b.onSelect)},o.$$.update=()=>{var b;1&o.$$.dirty&&n(8,t=s.turn_summaries||[]),32768&o.$$.dirty&&n(14,a=((b=$.state)==null?void 0:b.pinnedAgents)||{}),16385&o.$$.dirty&&n(7,r=(a==null?void 0:a[s.remote_agent_id])===!0),1&o.$$.dirty&&n(5,l=s.status===C.agentRunning||s.status===C.agentIdle)},n(6,i=!0),[s,c,d,v,k,l,!0,r,t,w,M,L,E,U,a,$,b=>{b.stopPropagation(),E(s.remote_agent_id)},b=>{b.stopPropagation(),U()},b=>{b.stopPropagation(),M(s.remote_agent_id)},()=>d(s.remote_agent_id),b=>b.key==="Enter"&&d(s.remote_agent_id)]}class Z extends j{constructor(e){super(),V(this,e,Vt,jt,X,{agent:0,selected:1,onSelect:2})}}function Xt(o){let e;return{c(){e=T(o[0])},m(n,t){f(n,e,t)},p(n,t){1&t&&B(e,n[0])},d(n){n&&m(e)}}}function Yt(o){let e,n,t;return n=new D({props:{size:2,color:"secondary",$$slots:{default:[Xt]},$$scope:{ctx:o}}}),{c(){e=R("div"),y(n.$$.fragment),S(e,"class","section-header svelte-1tegnqi")},m(a,r){f(a,e,r),x(n,e,null),t=!0},p(a,[r]){const l={};3&r&&(l.$$scope={dirty:r,ctx:a}),n.$set(l)},i(a){t||(g(n.$$.fragment,a),t=!0)},o(a){u(n.$$.fragment,a),t=!1},d(a){a&&m(e),_(n)}}}function Jt(o,e,n){let{title:t}=e;return o.$$set=a=>{"title"in a&&n(0,t=a.title)},[t]}class ee extends j{constructor(e){super(),V(this,e,Jt,Yt,X,{title:0})}}function ke(o,e,n){const t=o.slice();return t[7]=e[n],t[9]=n,t}function Se(o,e,n){const t=o.slice();return t[7]=e[n],t[9]=n,t}function Ae(o,e,n){const t=o.slice();return t[7]=e[n],t[9]=n,t}function be(o,e,n){const t=o.slice();return t[7]=e[n],t[9]=n,t}function Re(o,e,n){const t=o.slice();return t[7]=e[n],t[9]=n,t}function Ie(o,e,n){const t=o.slice();return t[7]=e[n],t[9]=n,t}function Kt(o){let e,n,t,a,r,l,i,$=o[1].pinned.length>0&&ze(o),s=o[1].readyToReview.length>0&&Pe(o),c=o[1].running.length>0&&He(o),d=o[1].idle.length>0&&Te(o),h=o[1].failed.length>0&&Ee(o),w=o[1].additional.length>0&&Ce(o);return{c(){$&&$.c(),e=I(),s&&s.c(),n=I(),c&&c.c(),t=I(),d&&d.c(),a=I(),h&&h.c(),r=I(),w&&w.c(),l=N()},m(v,k){$&&$.m(v,k),f(v,e,k),s&&s.m(v,k),f(v,n,k),c&&c.m(v,k),f(v,t,k),d&&d.m(v,k),f(v,a,k),h&&h.m(v,k),f(v,r,k),w&&w.m(v,k),f(v,l,k),i=!0},p(v,k){v[1].pinned.length>0?$?($.p(v,k),2&k&&g($,1)):($=ze(v),$.c(),g($,1),$.m(e.parentNode,e)):$&&(F(),u($,1,1,()=>{$=null}),P()),v[1].readyToReview.length>0?s?(s.p(v,k),2&k&&g(s,1)):(s=Pe(v),s.c(),g(s,1),s.m(n.parentNode,n)):s&&(F(),u(s,1,1,()=>{s=null}),P()),v[1].running.length>0?c?(c.p(v,k),2&k&&g(c,1)):(c=He(v),c.c(),g(c,1),c.m(t.parentNode,t)):c&&(F(),u(c,1,1,()=>{c=null}),P()),v[1].idle.length>0?d?(d.p(v,k),2&k&&g(d,1)):(d=Te(v),d.c(),g(d,1),d.m(a.parentNode,a)):d&&(F(),u(d,1,1,()=>{d=null}),P()),v[1].failed.length>0?h?(h.p(v,k),2&k&&g(h,1)):(h=Ee(v),h.c(),g(h,1),h.m(r.parentNode,r)):h&&(F(),u(h,1,1,()=>{h=null}),P()),v[1].additional.length>0?w?(w.p(v,k),2&k&&g(w,1)):(w=Ce(v),w.c(),g(w,1),w.m(l.parentNode,l)):w&&(F(),u(w,1,1,()=>{w=null}),P())},i(v){i||(g($),g(s),g(c),g(d),g(h),g(w),i=!0)},o(v){u($),u(s),u(c),u(d),u(h),u(w),i=!1},d(v){v&&(m(e),m(n),m(t),m(a),m(r),m(l)),$&&$.d(v),s&&s.d(v),c&&c.d(v),d&&d.d(v),h&&h.d(v),w&&w.d(v)}}}function Qt(o){let e,n,t;return n=new D({props:{size:3,color:"secondary",$$slots:{default:[en]},$$scope:{ctx:o}}}),{c(){e=R("div"),y(n.$$.fragment),S(e,"class","empty-state svelte-aiqmvp")},m(a,r){f(a,e,r),x(n,e,null),t=!0},p(a,r){const l={};32768&r&&(l.$$scope={dirty:r,ctx:a}),n.$set(l)},i(a){t||(g(n.$$.fragment,a),t=!0)},o(a){u(n.$$.fragment,a),t=!1},d(a){a&&m(e),_(n)}}}function Zt(o){let e,n,t,a,r,l;return t=new Qe({}),r=new D({props:{size:3,color:"secondary",$$slots:{default:[tn]},$$scope:{ctx:o}}}),{c(){e=R("div"),n=R("div"),y(t.$$.fragment),a=I(),y(r.$$.fragment),S(n,"class","l-loading-container svelte-aiqmvp"),S(e,"class","empty-state svelte-aiqmvp")},m(i,$){f(i,e,$),q(e,n),x(t,n,null),q(n,a),x(r,n,null),l=!0},p(i,$){const s={};32768&$&&(s.$$scope={dirty:$,ctx:i}),r.$set(s)},i(i){l||(g(t.$$.fragment,i),g(r.$$.fragment,i),l=!0)},o(i){u(t.$$.fragment,i),u(r.$$.fragment,i),l=!1},d(i){i&&m(e),_(t),_(r)}}}function ze(o){let e,n,t,a,r=[],l=new Map;e=new ee({props:{title:"Pinned"}});let i=O(o[1].pinned);const $=s=>s[7].remote_agent_id+s[9];for(let s=0;s<i.length;s+=1){let c=Ie(o,i,s),d=$(c);l.set(d,r[s]=Fe(d,c))}return{c(){y(e.$$.fragment),n=I(),t=R("div");for(let s=0;s<r.length;s+=1)r[s].c();S(t,"class","agent-grid svelte-aiqmvp")},m(s,c){x(e,s,c),f(s,n,c),f(s,t,c);for(let d=0;d<r.length;d+=1)r[d]&&r[d].m(t,null);a=!0},p(s,c){11&c&&(i=O(s[1].pinned),F(),r=Y(r,c,$,1,s,i,l,t,J,Fe,null,Ie),P())},i(s){if(!a){g(e.$$.fragment,s);for(let c=0;c<i.length;c+=1)g(r[c]);a=!0}},o(s){u(e.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);a=!1},d(s){s&&(m(n),m(t)),_(e,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Fe(o,e){var r;let n,t,a;return t=new Z({props:{agent:e[7],selected:e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[3]}}),{key:o,first:null,c(){n=N(),y(t.$$.fragment),this.first=n},m(l,i){f(l,n,i),x(t,l,i),a=!0},p(l,i){var s;e=l;const $={};2&i&&($.agent=e[7]),3&i&&($.selected=e[7].remote_agent_id===((s=e[0].state)==null?void 0:s.selectedAgentId)),t.$set($)},i(l){a||(g(t.$$.fragment,l),a=!0)},o(l){u(t.$$.fragment,l),a=!1},d(l){l&&m(n),_(t,l)}}}function Pe(o){let e,n,t,a,r=[],l=new Map;e=new ee({props:{title:"Ready to review"}});let i=O(o[1].readyToReview);const $=s=>s[7].remote_agent_id+s[9];for(let s=0;s<i.length;s+=1){let c=Re(o,i,s),d=$(c);l.set(d,r[s]=qe(d,c))}return{c(){y(e.$$.fragment),n=I(),t=R("div");for(let s=0;s<r.length;s+=1)r[s].c();S(t,"class","agent-grid svelte-aiqmvp")},m(s,c){x(e,s,c),f(s,n,c),f(s,t,c);for(let d=0;d<r.length;d+=1)r[d]&&r[d].m(t,null);a=!0},p(s,c){11&c&&(i=O(s[1].readyToReview),F(),r=Y(r,c,$,1,s,i,l,t,J,qe,null,Re),P())},i(s){if(!a){g(e.$$.fragment,s);for(let c=0;c<i.length;c+=1)g(r[c]);a=!0}},o(s){u(e.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);a=!1},d(s){s&&(m(n),m(t)),_(e,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function qe(o,e){var r;let n,t,a;return t=new Z({props:{agent:e[7],selected:e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[3]}}),{key:o,first:null,c(){n=N(),y(t.$$.fragment),this.first=n},m(l,i){f(l,n,i),x(t,l,i),a=!0},p(l,i){var s;e=l;const $={};2&i&&($.agent=e[7]),3&i&&($.selected=e[7].remote_agent_id===((s=e[0].state)==null?void 0:s.selectedAgentId)),t.$set($)},i(l){a||(g(t.$$.fragment,l),a=!0)},o(l){u(t.$$.fragment,l),a=!1},d(l){l&&m(n),_(t,l)}}}function He(o){let e,n,t,a,r=[],l=new Map;e=new ee({props:{title:"Running agents"}});let i=O(o[1].running);const $=s=>s[7].remote_agent_id+s[9];for(let s=0;s<i.length;s+=1){let c=be(o,i,s),d=$(c);l.set(d,r[s]=Oe(d,c))}return{c(){y(e.$$.fragment),n=I(),t=R("div");for(let s=0;s<r.length;s+=1)r[s].c();S(t,"class","agent-grid svelte-aiqmvp")},m(s,c){x(e,s,c),f(s,n,c),f(s,t,c);for(let d=0;d<r.length;d+=1)r[d]&&r[d].m(t,null);a=!0},p(s,c){11&c&&(i=O(s[1].running),F(),r=Y(r,c,$,1,s,i,l,t,J,Oe,null,be),P())},i(s){if(!a){g(e.$$.fragment,s);for(let c=0;c<i.length;c+=1)g(r[c]);a=!0}},o(s){u(e.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);a=!1},d(s){s&&(m(n),m(t)),_(e,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Oe(o,e){var r;let n,t,a;return t=new Z({props:{agent:e[7],selected:e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[3]}}),{key:o,first:null,c(){n=N(),y(t.$$.fragment),this.first=n},m(l,i){f(l,n,i),x(t,l,i),a=!0},p(l,i){var s;e=l;const $={};2&i&&($.agent=e[7]),3&i&&($.selected=e[7].remote_agent_id===((s=e[0].state)==null?void 0:s.selectedAgentId)),t.$set($)},i(l){a||(g(t.$$.fragment,l),a=!0)},o(l){u(t.$$.fragment,l),a=!1},d(l){l&&m(n),_(t,l)}}}function Te(o){let e,n,t,a,r=[],l=new Map;e=new ee({props:{title:"Idle agents"}});let i=O(o[1].idle);const $=s=>s[7].remote_agent_id+s[9];for(let s=0;s<i.length;s+=1){let c=Ae(o,i,s),d=$(c);l.set(d,r[s]=Me(d,c))}return{c(){y(e.$$.fragment),n=I(),t=R("div");for(let s=0;s<r.length;s+=1)r[s].c();S(t,"class","agent-grid svelte-aiqmvp")},m(s,c){x(e,s,c),f(s,n,c),f(s,t,c);for(let d=0;d<r.length;d+=1)r[d]&&r[d].m(t,null);a=!0},p(s,c){11&c&&(i=O(s[1].idle),F(),r=Y(r,c,$,1,s,i,l,t,J,Me,null,Ae),P())},i(s){if(!a){g(e.$$.fragment,s);for(let c=0;c<i.length;c+=1)g(r[c]);a=!0}},o(s){u(e.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);a=!1},d(s){s&&(m(n),m(t)),_(e,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Me(o,e){var r;let n,t,a;return t=new Z({props:{agent:e[7],selected:e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[3]}}),{key:o,first:null,c(){n=N(),y(t.$$.fragment),this.first=n},m(l,i){f(l,n,i),x(t,l,i),a=!0},p(l,i){var s;e=l;const $={};2&i&&($.agent=e[7]),3&i&&($.selected=e[7].remote_agent_id===((s=e[0].state)==null?void 0:s.selectedAgentId)),t.$set($)},i(l){a||(g(t.$$.fragment,l),a=!0)},o(l){u(t.$$.fragment,l),a=!1},d(l){l&&m(n),_(t,l)}}}function Ee(o){let e,n,t,a,r=[],l=new Map;e=new ee({props:{title:"Failed agents"}});let i=O(o[1].failed);const $=s=>s[7].remote_agent_id+s[9];for(let s=0;s<i.length;s+=1){let c=Se(o,i,s),d=$(c);l.set(d,r[s]=Ne(d,c))}return{c(){y(e.$$.fragment),n=I(),t=R("div");for(let s=0;s<r.length;s+=1)r[s].c();S(t,"class","agent-grid svelte-aiqmvp")},m(s,c){x(e,s,c),f(s,n,c),f(s,t,c);for(let d=0;d<r.length;d+=1)r[d]&&r[d].m(t,null);a=!0},p(s,c){11&c&&(i=O(s[1].failed),F(),r=Y(r,c,$,1,s,i,l,t,J,Ne,null,Se),P())},i(s){if(!a){g(e.$$.fragment,s);for(let c=0;c<i.length;c+=1)g(r[c]);a=!0}},o(s){u(e.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);a=!1},d(s){s&&(m(n),m(t)),_(e,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Ne(o,e){var r;let n,t,a;return t=new Z({props:{agent:e[7],selected:e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[3]}}),{key:o,first:null,c(){n=N(),y(t.$$.fragment),this.first=n},m(l,i){f(l,n,i),x(t,l,i),a=!0},p(l,i){var s;e=l;const $={};2&i&&($.agent=e[7]),3&i&&($.selected=e[7].remote_agent_id===((s=e[0].state)==null?void 0:s.selectedAgentId)),t.$set($)},i(l){a||(g(t.$$.fragment,l),a=!0)},o(l){u(t.$$.fragment,l),a=!1},d(l){l&&m(n),_(t,l)}}}function Ce(o){let e,n,t,a,r=[],l=new Map;e=new ee({props:{title:"Other agents"}});let i=O(o[1].additional);const $=s=>s[7].remote_agent_id+s[9];for(let s=0;s<i.length;s+=1){let c=ke(o,i,s),d=$(c);l.set(d,r[s]=De(d,c))}return{c(){y(e.$$.fragment),n=I(),t=R("div");for(let s=0;s<r.length;s+=1)r[s].c();S(t,"class","agent-grid svelte-aiqmvp")},m(s,c){x(e,s,c),f(s,n,c),f(s,t,c);for(let d=0;d<r.length;d+=1)r[d]&&r[d].m(t,null);a=!0},p(s,c){11&c&&(i=O(s[1].additional),F(),r=Y(r,c,$,1,s,i,l,t,J,De,null,ke),P())},i(s){if(!a){g(e.$$.fragment,s);for(let c=0;c<i.length;c+=1)g(r[c]);a=!0}},o(s){u(e.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);a=!1},d(s){s&&(m(n),m(t)),_(e,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function De(o,e){var r;let n,t,a;return t=new Z({props:{agent:e[7],selected:e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[3]}}),{key:o,first:null,c(){n=N(),y(t.$$.fragment),this.first=n},m(l,i){f(l,n,i),x(t,l,i),a=!0},p(l,i){var s;e=l;const $={};2&i&&($.agent=e[7]),3&i&&($.selected=e[7].remote_agent_id===((s=e[0].state)==null?void 0:s.selectedAgentId)),t.$set($)},i(l){a||(g(t.$$.fragment,l),a=!0)},o(l){u(t.$$.fragment,l),a=!1},d(l){l&&m(n),_(t,l)}}}function en(o){let e;return{c(){e=T("No agents available")},m(n,t){f(n,e,t)},d(n){n&&m(e)}}}function tn(o){let e;return{c(){e=T("Loading the Augment panel...")},m(n,t){f(n,e,t)},d(n){n&&m(e)}}}function nn(o){let e,n,t,a;const r=[Zt,Qt,Kt],l=[];function i($,s){var c,d;return(c=$[0].state)!=null&&c.agentOverviews?((d=$[0].state)==null?void 0:d.agentOverviews.length)===0?1:2:0}return n=i(o),t=l[n]=r[n](o),{c(){e=R("div"),t.c(),S(e,"class","agent-list svelte-aiqmvp")},m($,s){f($,e,s),l[n].m(e,null),a=!0},p($,[s]){let c=n;n=i($),n===c?l[n].p($,s):(F(),u(l[c],1,1,()=>{l[c]=null}),P(),t=l[n],t?t.p($,s):(t=l[n]=r[n]($),t.c()),g(t,1),t.m(e,null))},i($){a||(g(t),a=!0)},o($){u(t),a=!1},d($){$&&m(e),l[n].d()}}}function sn(o,e,n){let t,a,r,l;const i=ae(ge);Ue(o,i,s=>n(0,l=s));const $=ae(le.key);return o.$$.update=()=>{var s,c,d;1&o.$$.dirty&&n(5,t=it(((s=l.state)==null?void 0:s.agentOverviews)||[])),1&o.$$.dirty&&n(4,a=((c=l.state)==null?void 0:c.pinnedAgents)||{}),48&o.$$.dirty&&n(1,r=t.reduce((h,w)=>((a==null?void 0:a[w.remote_agent_id])===!0?h.pinned.push(w):w.status===C.agentIdle&&w.has_updates?h.readyToReview.push(w):w.status===C.agentRunning||w.status===C.agentStarting||w.workspace_status===ce.workspaceResuming?h.running.push(w):w.status===C.agentFailed?h.failed.push(w):w.status===C.agentIdle||w.workspace_status===ce.workspacePaused||w.workspace_status===ce.workspacePausing?h.idle.push(w):h.additional.push(w),h),{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]})),1&o.$$.dirty&&((d=l.state)!=null&&d.agentOverviews||$.focusAugmentPanel())},[l,r,i,function(s){i.update(c=>{if(c)return{...c,selectedAgentId:s}})},a,t]}class rn extends j{constructor(e){super(),V(this,e,sn,nn,X,{})}}function an(o){let e,n,t,a,r,l,i,$,s,c;return a=new mt({}),i=new rn({}),{c(){e=R("div"),n=R("h1"),t=R("span"),y(a.$$.fragment),r=T(`
    Remote Agents`),l=I(),y(i.$$.fragment),S(t,"class","l-main__title-logo svelte-1941nw6"),S(n,"class","l-main__title svelte-1941nw6"),S(e,"class","l-main svelte-1941nw6")},m(d,h){f(d,e,h),q(e,n),q(n,t),x(a,t,null),q(n,r),q(e,l),x(i,e,null),$=!0,s||(c=We(window,"message",o[0].onMessageFromExtension),s=!0)},p:Q,i(d){$||(g(a.$$.fragment,d),g(i.$$.fragment,d),$=!0)},o(d){u(a.$$.fragment,d),u(i.$$.fragment,d),$=!1},d(d){d&&m(e),_(a),_(i),s=!1,c()}}}function ln(o){const e=new tt(et),n=new at(e,void 0,lt,ot);e.registerConsumer(n),we(ge,n);const t=new le(e);return we(le.key,t),Ze(()=>(n.fetchStateFromExtension().then(()=>{n.update(a=>{if(!a)return;const r=[...a.activeWebviews,"home"];return a.pinnedAgents?{...a,activeWebviews:r}:{...a,activeWebviews:r,pinnedAgents:{}}})}),()=>{e.dispose(),t.dispose()})),[e]}new class extends j{constructor(o){super(),V(this,o,ln,an,X,{})}}({target:document.getElementById("app")});
