/**
 * Web Search Engine - Motor de Pesquisa Web
 * Integração com APIs de pesquisa para busca de informações
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

class WebSearchEngine {
    constructor() {
        this.searchProviders = new Map();
        this.cache = new Map();
        this.isInitialized = false;
        this.rateLimiter = new Map();
    }

    async initialize() {
        if (this.isInitialized) return;

        console.log('🔧 Inicializando Motor de Pesquisa Web...');
        
        this.setupSearchProviders();
        this.setupRateLimiting();
        
        this.isInitialized = true;
        console.log('✅ Motor de Pesquisa Web inicializado');
    }

    setupSearchProviders() {
        // DuckDuckGo (não requer API key)
        this.searchProviders.set('duckduckgo', {
            name: 'DuckDuckGo',
            searchUrl: 'https://api.duckduckgo.com/',
            enabled: true,
            rateLimit: 1000, // 1 segundo entre requests
            search: this.searchDuckDuckGo.bind(this)
        });

        // Bing Search (requer API key)
        this.searchProviders.set('bing', {
            name: 'Bing Search',
            searchUrl: 'https://api.bing.microsoft.com/v7.0/search',
            enabled: false, // Desabilitado por padrão (requer API key)
            rateLimit: 100, // 100ms entre requests
            search: this.searchBing.bind(this)
        });

        // Google Custom Search (requer API key)
        this.searchProviders.set('google', {
            name: 'Google Custom Search',
            searchUrl: 'https://www.googleapis.com/customsearch/v1',
            enabled: false, // Desabilitado por padrão (requer API key)
            rateLimit: 100,
            search: this.searchGoogle.bind(this)
        });

        // Fallback: Web scraping simples
        this.searchProviders.set('fallback', {
            name: 'Fallback Search',
            enabled: true,
            rateLimit: 2000,
            search: this.searchFallback.bind(this)
        });
    }

    setupRateLimiting() {
        for (const [provider, config] of this.searchProviders) {
            this.rateLimiter.set(provider, {
                lastRequest: 0,
                rateLimit: config.rateLimit
            });
        }
    }

    async search(query, numResults = 5) {
        try {
            // Verificar cache primeiro
            const cacheKey = `${query}_${numResults}`;
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < 300000) { // 5 minutos
                    return cached.results;
                }
            }

            // Tentar provedores em ordem de preferência
            const providers = ['duckduckgo', 'bing', 'google', 'fallback'];
            
            for (const providerName of providers) {
                const provider = this.searchProviders.get(providerName);
                if (!provider.enabled) continue;

                try {
                    // Verificar rate limiting
                    if (!this.canMakeRequest(providerName)) {
                        continue;
                    }

                    const results = await provider.search(query, numResults);
                    
                    if (results && results.length > 0) {
                        // Cache dos resultados
                        this.cache.set(cacheKey, {
                            results,
                            timestamp: Date.now()
                        });
                        
                        this.updateRateLimit(providerName);
                        return results;
                    }
                } catch (error) {
                    console.error(`Erro no provedor ${providerName}:`, error);
                    continue;
                }
            }

            return [];

        } catch (error) {
            console.error('Erro na pesquisa web:', error);
            return [];
        }
    }

    canMakeRequest(providerName) {
        const limiter = this.rateLimiter.get(providerName);
        const now = Date.now();
        return (now - limiter.lastRequest) >= limiter.rateLimit;
    }

    updateRateLimit(providerName) {
        const limiter = this.rateLimiter.get(providerName);
        limiter.lastRequest = Date.now();
    }

    async searchDuckDuckGo(query, numResults) {
        return new Promise((resolve, reject) => {
            const searchUrl = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`;
            
            https.get(searchUrl, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const response = JSON.parse(data);
                        const results = [];
                        
                        // Processar resultados instantâneos
                        if (response.Answer) {
                            results.push({
                                title: 'Resposta Instantânea',
                                url: response.AbstractURL || '',
                                snippet: response.Answer,
                                source: 'DuckDuckGo'
                            });
                        }

                        // Processar tópicos relacionados
                        if (response.RelatedTopics) {
                            response.RelatedTopics.slice(0, numResults - results.length).forEach(topic => {
                                if (topic.Text && topic.FirstURL) {
                                    results.push({
                                        title: topic.Text.split(' - ')[0] || 'Tópico Relacionado',
                                        url: topic.FirstURL,
                                        snippet: topic.Text,
                                        source: 'DuckDuckGo'
                                    });
                                }
                            });
                        }

                        // Se não há resultados suficientes, usar abstract
                        if (results.length < numResults && response.Abstract) {
                            results.push({
                                title: response.Heading || 'Informação',
                                url: response.AbstractURL || '',
                                snippet: response.Abstract,
                                source: 'DuckDuckGo'
                            });
                        }

                        resolve(results.slice(0, numResults));
                    } catch (error) {
                        reject(error);
                    }
                });
            }).on('error', reject);
        });
    }

    async searchBing(query, numResults) {
        // Implementação para Bing Search API
        // Requer API key configurada
        const apiKey = process.env.BING_SEARCH_API_KEY;
        if (!apiKey) {
            throw new Error('Bing Search API key não configurada');
        }

        return new Promise((resolve, reject) => {
            const searchUrl = `https://api.bing.microsoft.com/v7.0/search?q=${encodeURIComponent(query)}&count=${numResults}`;
            
            const options = {
                headers: {
                    'Ocp-Apim-Subscription-Key': apiKey
                }
            };

            https.get(searchUrl, options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const response = JSON.parse(data);
                        const results = [];
                        
                        if (response.webPages && response.webPages.value) {
                            response.webPages.value.forEach(page => {
                                results.push({
                                    title: page.name,
                                    url: page.url,
                                    snippet: page.snippet,
                                    source: 'Bing'
                                });
                            });
                        }

                        resolve(results);
                    } catch (error) {
                        reject(error);
                    }
                });
            }).on('error', reject);
        });
    }

    async searchGoogle(query, numResults) {
        // Implementação para Google Custom Search API
        // Requer API key e Search Engine ID
        const apiKey = process.env.GOOGLE_SEARCH_API_KEY;
        const searchEngineId = process.env.GOOGLE_SEARCH_ENGINE_ID;
        
        if (!apiKey || !searchEngineId) {
            throw new Error('Google Search API key ou Search Engine ID não configurados');
        }

        return new Promise((resolve, reject) => {
            const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${apiKey}&cx=${searchEngineId}&q=${encodeURIComponent(query)}&num=${numResults}`;
            
            https.get(searchUrl, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const response = JSON.parse(data);
                        const results = [];
                        
                        if (response.items) {
                            response.items.forEach(item => {
                                results.push({
                                    title: item.title,
                                    url: item.link,
                                    snippet: item.snippet,
                                    source: 'Google'
                                });
                            });
                        }

                        resolve(results);
                    } catch (error) {
                        reject(error);
                    }
                });
            }).on('error', reject);
        });
    }

    async searchFallback(query, numResults) {
        // Busca simples usando múltiplas fontes
        const results = [];
        
        // Simular resultados baseados na query
        const commonSources = [
            'stackoverflow.com',
            'github.com',
            'developer.mozilla.org',
            'docs.python.org',
            'nodejs.org'
        ];

        commonSources.slice(0, numResults).forEach((source, index) => {
            results.push({
                title: `${query} - ${source}`,
                url: `https://${source}/search?q=${encodeURIComponent(query)}`,
                snippet: `Informações sobre ${query} encontradas em ${source}`,
                source: 'Fallback'
            });
        });

        return results;
    }

    async fetchContent(url) {
        try {
            // Verificar cache
            if (this.cache.has(url)) {
                const cached = this.cache.get(url);
                if (Date.now() - cached.timestamp < 600000) { // 10 minutos
                    return cached.content;
                }
            }

            const content = await this.fetchUrl(url);
            const processedContent = this.processContent(content, url);
            
            // Cache do conteúdo
            this.cache.set(url, {
                content: processedContent,
                timestamp: Date.now()
            });

            return processedContent;

        } catch (error) {
            console.error('Erro ao buscar conteúdo:', error);
            return {
                url,
                title: 'Erro',
                content: 'Não foi possível buscar o conteúdo desta URL',
                error: error.message
            };
        }
    }

    async fetchUrl(url) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const client = urlObj.protocol === 'https:' ? https : http;
            
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: 'GET',
                headers: {
                    'User-Agent': 'Continue-Agent/1.0',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate'
                },
                timeout: 10000
            };

            const req = client.request(options, (res) => {
                let data = '';
                
                res.on('data', (chunk) => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    resolve(data);
                });
            });

            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Timeout na requisição'));
            });

            req.end();
        });
    }

    processContent(html, url) {
        // Processar HTML para extrair conteúdo relevante
        let content = html;
        
        // Remover scripts e styles
        content = content.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
        content = content.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
        
        // Extrair título
        const titleMatch = content.match(/<title[^>]*>([\s\S]*?)<\/title>/i);
        const title = titleMatch ? titleMatch[1].trim() : 'Sem título';
        
        // Extrair texto principal
        content = content.replace(/<[^>]+>/g, ' '); // Remover tags HTML
        content = content.replace(/\s+/g, ' '); // Normalizar espaços
        content = content.trim();
        
        // Limitar tamanho do conteúdo
        if (content.length > 5000) {
            content = content.substring(0, 5000) + '...';
        }

        return {
            url,
            title,
            content,
            length: content.length,
            fetchedAt: new Date().toISOString()
        };
    }

    clearCache() {
        this.cache.clear();
        console.log('Cache de pesquisa web limpo');
    }

    getStats() {
        return {
            cacheSize: this.cache.size,
            providers: Array.from(this.searchProviders.keys()),
            enabledProviders: Array.from(this.searchProviders.entries())
                .filter(([_, config]) => config.enabled)
                .map(([name, _]) => name)
        };
    }
}

module.exports = WebSearchEngine;
