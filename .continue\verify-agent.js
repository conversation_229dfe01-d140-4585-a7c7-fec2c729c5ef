/**
 * Script de Verificação do Continue Agent
 * Verifica se todas as configurações estão corretas para ativar o botão Agent
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verificando configuração do Continue Agent...\n');

// 1. Verificar arquivo de configuração
const configPath = path.join(__dirname, 'config.json');
if (fs.existsSync(configPath)) {
    console.log('✅ Arquivo config.json encontrado');
    
    try {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        // Verificar modelo
        if (config.models && config.models.length > 0) {
            const model = config.models[0];
            console.log('✅ Modelo configurado:', model.title);
            console.log('   • Provider:', model.provider);
            console.log('   • API Base:', model.apiBase);
            console.log('   • Supports Tools:', model.supportsTools ? '✅' : '❌');
        } else {
            console.log('❌ Nenhum modelo configurado');
        }
        
        // Verificar experimentalFeatures
        if (config.experimentalFeatures && config.experimentalFeatures.agentMode) {
            console.log('✅ Agent Mode habilitado');
        } else {
            console.log('❌ Agent Mode não habilitado');
        }
        
        // Verificar agentConfig
        if (config.agentConfig && config.agentConfig.enabled) {
            console.log('✅ Agent Config habilitado');
            console.log('   • Capabilities:', config.agentConfig.capabilities.length);
        } else {
            console.log('❌ Agent Config não habilitado');
        }
        
        // Verificar tools
        if (config.tools && config.tools.length > 0) {
            console.log('✅ Tools configuradas:', config.tools.length);
        } else {
            console.log('❌ Nenhuma tool configurada');
        }
        
    } catch (error) {
        console.log('❌ Erro ao ler config.json:', error.message);
    }
} else {
    console.log('❌ Arquivo config.json não encontrado');
}

// 2. Verificar arquivo .continuerc.json
const rcPath = path.join(__dirname, '.continuerc.json');
if (fs.existsSync(rcPath)) {
    console.log('✅ Arquivo .continuerc.json encontrado');
} else {
    console.log('❌ Arquivo .continuerc.json não encontrado');
}

// 3. Verificar modelo LLM
const modelPath = "C:\\Users\\<USER>\\.lmstudio\\models\\mradermacher\\Qwen3-30B-A3B-python-coder-i1-GGUF\\Qwen3-30B-A3B-python-coder.i1-Q4_K_S.gguf";
if (fs.existsSync(modelPath)) {
    console.log('✅ Modelo LLM encontrado no caminho especificado');
} else {
    console.log('⚠️ Modelo LLM não encontrado no caminho especificado');
    console.log('   Caminho:', modelPath);
}

// 4. Testar conexão com LM Studio
console.log('\n🔗 Testando conexão com LM Studio...');

const testConnection = async () => {
    try {
        const response = await fetch('http://localhost:1234/v1/models', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer lm-studio'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ LM Studio conectado');
            console.log('   • Modelos disponíveis:', data.data?.length || 0);
            
            if (data.data && data.data.length > 0) {
                console.log('   • Modelo ativo:', data.data[0].id);
            }
        } else {
            console.log('❌ LM Studio respondeu com erro:', response.status);
        }
    } catch (error) {
        console.log('❌ Não foi possível conectar ao LM Studio');
        console.log('   • Verifique se o LM Studio está rodando na porta 1234');
        console.log('   • Verifique se o modelo está carregado');
    }
};

// Executar teste de conexão se fetch estiver disponível
if (typeof fetch !== 'undefined') {
    testConnection();
} else {
    // Para Node.js mais antigo, usar require
    try {
        const fetch = require('node-fetch');
        testConnection();
    } catch {
        console.log('ℹ️ Teste de conexão não disponível (node-fetch não instalado)');
        console.log('   Verifique manualmente: http://localhost:1234');
    }
}

console.log('\n📋 Resumo:');
console.log('1. Configuração criada em:', configPath);
console.log('2. Arquivo RC criado em:', rcPath);
console.log('3. Modelo esperado em:', modelPath);
console.log('\n🔄 Para ativar o Agent:');
console.log('1. Reinicie o VSCode completamente');
console.log('2. Certifique-se que o LM Studio está rodando');
console.log('3. Abra o Continue (Ctrl+Shift+L)');
console.log('4. O botão "Agent" deve estar ativo!');

console.log('\n🤖 Continue Agent configurado com sucesso!');
