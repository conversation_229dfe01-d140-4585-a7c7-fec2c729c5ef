{"metadata": {"kernelspec": {"name": "SQL", "display_name": "SQL", "language": "sql"}, "language_info": {"name": "sql", "version": ""}}, "nbformat_minor": 2, "nbformat": 4, "cells": [{"cell_type": "markdown", "source": "## Creating a SQL Server in a Linux Container [Using PowerShell]\r\n\r\n|Module|Link|\r\n|------------|---------------------------------------|\r\n|SqlServer|https://www.powershellgallery.com/packages/SqlServer/|\r\n|ReportingServicesTools|https://www.powershellgallery.com/packages/ReportingServicesTools/|\r\n|MicrosoftPowerBIMgmt|https://www.powershellgallery.com/packages/MicrosoftPowerBIMgmt/|\r\n|SqlServerDsc|https://www.powershellgallery.com/packages/SqlServerDsc/|\r\n|Az.Sql|https://www.powershellgallery.com/packages/Az.Sql/|\r\n\r\n<pre>\r\nIf you don't already have a terminal window open, you need to first: <a href=\"command:workbench.action.terminal.focus\">Open the terminal</a>  \r\n</pre>\r\n<pre> \r\nYou probably don't have this directory on your machine, so run this: <a href=\"command:workbench.action.terminal.sendSequence?%7B%22text%22%3A%22mkdir%20C:%2FSQLData%2FDocker%2FSQLDev66%22%7D\">mkdir C:/SQLData/Docker/SQLDev63 </a>\r\n</pre>\r\n<pre>\r\n<a href=\"command:workbench.action.terminal.sendSequence?%7B%22text%22%3A%22Invoke-Expression%20(Invoke-WebRequest%20https:%2F%2Fgist.githubusercontent.com%2FSQLvariant%2F63193826e2352f2a8c1c85f63c724501%2Fraw%2Fd424e4127ceda141cc091aa7cfba7a11e410143e/DockerDesktop-with-SQL-PowerShell-63.ps1)%22%7D\">Spin up a Docker Container with Invoke-Expression (Invoke-WebRequest https://gist.githubusercontent.com/SQLvariant) </a> Just click enter after the command is placed into the terminal. Just click enter after the command is placed into the terminal.  When prompted, the sa password is Test1234, but you can obviously change this.\r\n</pre>\r\n<pre> \r\nView the PowerShell Script with <a href=\"command:workbench.action.terminal.sendSequence?%7B%22text%22%3A%22PSEdit%20C:%2FSQLData%2FDocker-Desktop-with-SQL-PowerShell-63.ps1%22%7D\">PSEdit </a>\r\n</pre>\r\n", "metadata": {}}, {"cell_type": "code", "source": "SELECT name, create_date\r\nFROM sys.databases", "metadata": {}, "outputs": [], "execution_count": 0}]}