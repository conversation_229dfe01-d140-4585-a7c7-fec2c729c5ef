### [2.4.1-beta1] - 2025-02-28

#### Code Changes

- Avoid querying for cursor position when it's not necessary (#4448)
- Handle buffer changes made by an event handler (#4442)
- Update `SelectCommandArgument` to properly handle POSIX style options for CLI commands (#4016)

#### Build Changes

- Update PSReadLine build to target `netstandard2.0` (#4584)
- Update documentation about the building of PSReadLine (#4286) (Thanks @sangafabrice!)
- Update `HelpInfoUri` for 7.5 (#4284)
- Update the release pipeline to remove `AzFeed` from display name (#4204)
- Update the OneBranch pipeline to keep it compliant and remove SBOM files from module (#4201)
- Make sure the `CodeQL` result from release pipeline gets uploaded (#4082)
- Add 'ob_restore_phase' for every task before the signing task to work around the signing issue (#4046)
- Change the NuGet feed to use the governed PowerShell feed (#4044)
- Update "Code of Conduct" and "Security Policy" (#4037)
- Disable SBOM, signing, and codeQL for the publish job (#3986)
- Update triage messages to use the latest stable version (#3985)
- Fix the release stage and update the changelog for v2.3.5 servicing release (#3984)
- Add the release stage to the pipeline and exclude test folders from Component Governance (#3982)
- Change back to 'external_distribution' for nupkg signing (#3977)
- Migrate PSReadLine release build pipeline to OneBranch (#3975)

[2.4.1-beta1]: https://github.com/PowerShell/PSReadLine/compare/v2.4.0-beta0...v2.4.1-beta1

### [2.4.0-beta0] - 2024-03-01

- Fix the null-reference exception when running `Debug-Job` on a thread job (#3957)
- Add needed permission to the workflow (#3944, #3945, #3946)
- Fix copying text to system clipboard on Linux using xclip (#3937)
- Use the correct directory separator for tab completion based on the platform we are working with (#3935)
- Update the minimal PS version required to be 5.1 (#3936)
- Little code style cleanup for the `GetCompletions()` method (#3898)
- Stop trying to de-duplicate completion results (#3897)
- Windows keyboard layout handling: get the current layout from the parent terminal process (#3786) (Thanks @ForNeVeR!)
- Add "resolution no activity" label to the bot-close list (#3852)
- Fix a few VI key handlers to close edit group properly (#3845)
- Update the documentation issue template to point to the PowerShell-Doc repo (#3839, #3840, #3841)
- Handle large history file properly by reading lines in the streaming way (#3810)
- Update build script to always include the `ProjectUri` info (#3821)

[2.4.0-beta0]: https://github.com/PowerShell/PSReadLine/compare/v2.3.4...v2.4.0-beta0

### [2.3.6] - 2024-10-02

This is a servicing release that excludes SBOM files from the module.

- Update the OneBranch pipeline to keep it compliant and remove SBOM files from module (#4201)
- Make sure the `CodeQL` result from release pipeline gets uploaded (#4082)
- Add 'ob_restore_phase' for every task before the signing task to work around the signing issue (#4046)
- Change the NuGet feed to use the governed PowerShell feed (#4044)
- Disable SBOM, signing, and codeQL for the publish job (#3986)

[2.3.6]: https://github.com/PowerShell/PSReadLine/compare/v2.3.5...v2.3.6

### [2.3.5] - 2024-04-02

This is a servicing release that excludes test components from SBOM generation.

- Add the release stage to the pipeline and exclude test folders from Component Governance (#3982)
- Change back to 'external_distribution' for nupkg signing (#3977)
- Migrate PSReadLine release build pipeline to OneBranch (#3975)
- Fix the null-reference exception when running `Debug-Job` on a thread job (#3957)
- Update build script to always include the `ProjectUri` info (#3821)

[2.3.5]: https://github.com/PowerShell/PSReadLine/compare/v2.3.4...v2.3.5

### [2.3.4] - 2023-10-02

- Choose the inline prediction color based on the environment (#3808)
- Update the stable version of PSReadLine used in the auto triage messages (#3804)
- Update `Compliance_Job` to add a new variable group for APIScan (#3803)

[2.3.4]: https://github.com/PowerShell/PSReadLine/compare/v2.3.3...v2.3.4

### [2.3.3] - 2023-09-18

- Re-package the `2.3.2-beta2` version to `2.3.3` as an official stable release.

[2.3.3]: https://github.com/PowerShell/PSReadLine/compare/v2.3.2-beta2...v2.3.3

### [2.3.2-beta2] - 2023-08-17

- Work around `InvalidOperationException` from Console API (#3755) (Thanks @jazzdelightsme!)
- Add the `TerminateOrphanedConsoleApps` option on Windows to kill orphaned console-attached process that may mess up reading from Console input (#3764) (Thanks @jazzdelightsme!)
- Fix bot to add `needs-triage` label to newly opened issue (#3772)
- Update `actions/checkout` used in GitHub action to v3 (#3773)
- Supports the text-object command `diw` in the VI edit mode (#2059) (Thanks @springcomp!)
- Fix `NullReferenceException` when processing event subscribers (#3781)
- Point to `F7History` in the comment of the `F7` key-binding sample (#3782)

[2.3.2-beta2]: https://github.com/PowerShell/PSReadLine/compare/v2.3.1-beta1...v2.3.2-beta2

### [2.3.1-beta1] - 2023-05-03

- Append reset VT sequence before rendering the ineline prediction (#3669)
- Support tooltip rendering in the prediction list view (#3667, #3671)
- Fix the broken doc link about `PowerShellGet` (#3657) (Thanks @vimode!)
- Add a sample for transforming Unicode code point to Unicode char by `Alt+x` (#3652)
- Avoid running `AddToHistoryHandler` on command lines loaded from history file (#3643)
- Force refreshing suggestion in the inline view when plugin is in use (#3644)
- Improve the sensitive history scrubbing to allow retrieving token from `az`, `gcloud`, and `kubectl` (#3641)
- Set the current location in `PredictionClient` when it's supported (#3639)
- Improve the default sensitive history scrubbing to allow safe property access (#3630)
- Make PSReadLine script hidden from debugger (#3629)

[2.3.1-beta1]: https://github.com/PowerShell/PSReadLine/compare/v2.3.0-beta0...v2.3.1-beta1

### [2.3.0-beta0] - 2023-03-07

- Fix the menu completion to better handle the backspace key (#3574)
- Improve the list view to be scrollable and auto-adjust the list view height (#3583)
- Use 'Visual Studio 2022' as the image for `appveyor` CI (#3594)
- Fix some typos in this repository (#3547) (Thanks @spaette!)
- De-duplicate prediction results with the history results (#3543)
- Updating Fabric bot (#3540, #3576)
- Change default color for inline prediction to `dim` (#3493)
- Make tab completion show results whose `ListItemText` are different by case only (#3456) (Thanks @dkaszews!)
- Fix to use the default member color for members (#3450)
- Update the samples in README.md (#3440, #3424)
- Place 'ViDGChord' in the right group (#3422)
- Fix the description of `CapitalizeWord` (#3384)
- Add support for upcasing, downcasing, and capitalizing word (#3365) (Thanks @3N4N!)
- No list view prediction when the first line was scrolled up off the buffer (#3372)
- Fix wrong cursor position in menu completion (#3373)
- Fix `ViModeIndicator = Cursor` for Windows Terminal (#3374)
- Fix parameter dynamic help when the help content is specified in ParameterAttribute (#3370)
- Handle multi-line description for parameter help content (#3358)
- Update module version in bot messages (#3361)

[2.3.0-beta0]: https://github.com/PowerShell/PSReadLine/compare/v2.2.6...v2.3.0-beta0

### [2.2.6] - 2022-06-27

- Enable Predictive Intellisense by default (#3351)

[2.2.6]: https://github.com/PowerShell/PSReadLine/compare/v2.2.5...v2.2.6

### [2.2.5] - 2022-05-03

- Re-package the `2.2.4-beta1` version to `2.2.5` as an official servicing release.

[2.2.5]: https://github.com/PowerShell/PSReadLine/compare/v2.2.4-beta1...v2.2.5

### [2.2.4-beta1] - 2022-04-27

- Sync ReadKeyProc thread with pipeline thread (#3294)
- Update build to use net462 (#3285)

[2.2.4-beta1]: https://github.com/PowerShell/PSReadLine/compare/v2.2.3...v2.2.4-beta1

### [2.2.3] - 2022-04-20

- Respect cancellation in `ReadOneOrMoreKeys()` (#3274, #3280)

[2.2.3]: https://github.com/PowerShell/PSReadLine/compare/v2.2.2...v2.2.3

### [2.2.2] - 2022-02-22

- Update to use the 1.0.0 version of `Microsoft.PowerShell.Pager` (#3206)
- No need to clear lines when it's not a menu completion rendering (#3199)

[2.2.2]: https://github.com/PowerShell/PSReadLine/compare/v2.2.1-rc1...v2.2.2

### [2.2.1-rc1] - 2022-01-28

- Update build agent to use `PSMMS2019-Secure` per requirement (#3174, #3187)
- Update the `HelpInfoURI` to point to the latest help content (#3171)
- Update the version of PowerShell nuget packages to 7.2.0 (#3170)
- Update `README.md` with updated links to content helping get set up (#3149, #3152) (Thanks @cgorshing!)
- Make `Ctrl+r` and `Ctrl+s` in `Vi` edit mode work the same way as in `Emacs` edit mode (#3148) (Thanks @davetapley!)
- Make `HistorySearchBackward` and `HistorySearchForward` able to navigate the list view (#3144)

[2.2.1-rc1]: https://github.com/PowerShell/PSReadLine/compare/v2.2.0-beta5...v2.2.1-rc1

### [2.2.0-beta5] - 2022-01-05

- Improve `RecomputeInitialCoords` to be more robust and handle a couple special cases (#3074)
- Add `BasicScrollingConsole` to support test cases for scrolling up scenarios (#3081)
- Fix the regex matching in the issue-triage script (#3082, #3110, #3113)
- Skip the AST analysis when command-line input has any parsing errors (#3075)
- Trigger the GitHub action by issue-open event (#3041)
- Fix the double quotes in the comment text for duplicate-issue handling (#3029, #3030)
- Update GitHub action to handle the `TypeLoadException` issue (#3025)
- Handle screen buffer scrolling correctly for inline dynamic help (#2951)
- Fix 2 menu completion issues that happen when we need to scroll screen buffer (#2949)

[2.2.0-beta5]: https://github.com/PowerShell/PSReadLine/compare/v2.2.0-beta4...v2.2.0-beta5

### [2.2.0-beta4] - 2021-10-27

- Generate `OnIdle` event only if the editing buffer is empty (#2934)
- Add private contract delegate for PSES to handle idle (#1679)
- Disable warning for the private contract field used by PSES (#2935)
- Reset all ANSI attributes before changing color in rendering to avoid color leaking (#2925)
- Make the default sensitive history scrubbing function a little smarter (#2921)
- Generate ADO SBOM for PSReadLine (#2918)
- Update the issue templates to use form templates (#2898, #2900, #2917)
- Update the inline suggestion rendering to not exceed the max window buffer (#2892)
- VI Mode: "Undo" now leaves the cursor under the position at the start of the deletion (#2045) (Thanks @springcomp!)
- Update `CreateCharInfoBuffer` to support continuous 'NextLineToken' (#2880)
- Fix `GotoBrace` to handle the case when the text buffer is empty (#2879)
- Fix rendering when continuation prompt is an empty string (#2875)
- Release mutex when facing `AbandonedMutexException` (#2867)
- Migrate the release build to use the 1ES agent pool and also fix the API scan (#2859)
- Pass the cancellation token along instead of using the default one (#2636)
- Don't ignore `ConsoleKey.Packet` type as that is simply Unicode (#2632)

[2.2.0-beta4]: https://github.com/PowerShell/PSReadLine/compare/v2.2.0-beta3...v2.2.0-beta4

### [2.2.0-beta3] - 2021-06-01

- Update the use of the prediction interface to adapt to the breaking changes introduced in PowerShell 7.2.0-preview.6 (#2524)
- Fix a null-ref exception in `DynamicHelpImpl` (#2292)

[2.2.0-beta3]: https://github.com/PowerShell/PSReadLine/compare/v2.2.0-beta2...v2.2.0-beta3

### [2.2.0-beta2] - 2021-02-23

- Update PSReadLine corresponding to the prediction interface updates (#2225)
- Add white spaces to the emacs dispatch table (#2223)
- Add the `SelectCommandArgument` bind-able function (#2222) (Thanks @ThePSAdmin for the idea!)
- Move prediction functions to a new group (#2211)
- Remove `LineIsMultiline` in favor of multi-line agnostic algorithms (#1125) (#2047) (Thanks @springcomp!)
- Add the "Dynamic Help" feature to PSReadLine (#1777)
- Prevent crash in `GotoFirstNonBlankOfLine` (#2050) (#2051) (Thanks @springcomp!)
- Refactor the usage of `_clipboard` (#2022) (Thanks @springcomp!)
- Make `d0` to delete to the start of the current logical line in a multiline buffer in VI mode (#2002) (Thanks @springcomp!)
- Add github action to pre-triage new issues (#2117, #2118)
- Highlight the install command in README.md (#2088) (Thanks @jiriurban21!)
- Deleting backward to or until a character should preserve the character under the cursor (#2007) (Thanks @springcomp!)
- Use `d^` to delete from the first non-blank character of a logical line (#2001) (Thanks @springcomp!)
- Update nuget.config based on guidance (#2003)
- Update the release build to use ESRP signing and unify the compliance job (#1983)
- Add readonly modifier to some private fields (#1984)
- Add argument selection handler to the sample profile (#1947) (Thanks @ThePSAdmin!)
- Fix three issues with the menu completion (#1946)

[2.2.0-beta2]: https://github.com/PowerShell/PSReadLine/compare/v2.2.0-beta1...v2.2.0-beta2

### Version 2.2.0-beta1

Changes:

* Add the prediction `ListView` and also hook up with the `CommandPrediction` APIs introduced in PS 7.1 (#1909)
* Update the release build to work with new module artifacts (#1930)

### Version 2.1.0

Changes:

* Update ChangeLog and version for 2.1.0 GA release

### Version 2.1.0-rc1

Pre-release notes:

Bug fixes:

* Add a key binding example: `ForwardCharAndAcceptNextSuggestionWord` (#1601)
* Update `HelpInfoURI` for the PSReadLine module (#1589)
* Fix incremental history saving when the history queue is full (#1602)
* Make PSReadline to not force the background color during render (#1626) (Thanks @DHowett)
* Update broken link in `README.md` (#1631) (Thanks @escape0707)
* Fix `UpHat` (^) in vi-mode to move the cursor while yanking (#1656) (Thanks @springcomp)
* vi-mode: Make `dd` deletes the logical line instead of the entire buffer (#1658) (Thanks @springcomp)
* Improve the bug reporting message (#1698)
* vi-mode: Add `dG` to delete to the end of multiline buffer (#1692) (Thanks @springcomp)
* Remove duplicated code (#1700) (Thanks @springcomp)
* vi-mode: `dd` now handles single line or multiline buffers consistently (#1694) (Thanks @springcomp)
* vi-mode: Make `D` and `d$` delete to the end of the current logical line (#1695) (Thanks @springcomp)
* vi-mode - Make `dj` delete the current and next `n` logical lines (#1697) (Thanks @springcomp)
* Add comment in `SamplePSReadLineProfile.ps1` (#1712) (Thanks @sethvs)
* Update the release build to satisfy the compliance requirement (#1702, #1714, #1726, #1731)
* vi-mode: Use `dk` to delete the previous `n` logical lines and the current logical line in a multi-line buffer (#1737) (Thanks @springcomp)
* vi-mode: Add `dgg` to delete from the beginning of the buffer to the current logical line (#1752) (Thanks @springcomp)
* Use const strings for some often used `ANSI` control sequences (#1809)
* Correct the descriptions of `SelectBackwardsLine` and `SelectLine` (#1857)
* Fix the inline prediction to not leak color (#1861)
* Rename `PredictionColor` to `InlinePredictionColor` (#1860)
* Fix a rendering issue regarding cleanup of previous logical lines (#1865)
* Allow `MaximumHistoryCount` to be set from user's profile (#1869)

### Version 2.1.0-beta2

Pre-release notes:

Bug fixes:

* Fix the environment data script and print buffer info in case of exception (#1482)
* Add the parameter `-PredictionSource` to `Set-PSReadLineOption`, with the options `None` and `History` (#1496)
* Make the predictive suggestion feature disabled by default (`PredictionSource` set to `None`). It can be enabled by `Set-PSReadLineOption -PredictionSource History` (#1496)
* Make the functions `AcceptSuggestion` and `AcceptNextSuggestionWord` bindable (#1496)
* Fix the `ArgumentOutOfRangeException` caused by trimming the command line before saving to history (#1496)
* Refactor `SelfInsert` to avoid duplicate logic (#1510)
* Relax the sensitive words we filter by changing `key` to `apikey` to reduce false positives (#1517)
* Make edit group more stable in VI mode (#1526)
* Make `ViForwardChar` able to accept suggestions (#1528)
* Update build as we move help content to the `PowerShell-Docs` repository (#1537)
* Reset all VT attributes for `PromptText` if it contains VT sequences (#1544)
* Expose `ViBackwardChar` and `ViForwardChar` as bindable functions (#1547)

### Version 2.1.0-beta1

Pre-release notes:

* Experimental support for fish-like suggestions in PSReadLine.

### Version 2.0.4

Bug fixes:

* Revert the update to the module `HelpInfoURI`
* Fix the env-data script and write buffer info in case of exception (#1482)
* Improve the bug reporting message (#1698)
* Update build to use .NET 3.1 for compliance (#1702)

### Version 2.0.3

Bug fixes:

* Update `HelpInfoURI` for the PSReadLine module (#1589)
* Make PSReadline to not force the background color during render (#1626) (Thanks @DHowett)

### Version 2.0.2

Bug fixes:

* Add the template parameter to the bug report link (#1441) (Thanks @anthonyvdotbe)
* Update the release build to create the NuGet package for publish (#1480)
* Run script in local scope to make PSReadLine works in PSES in `ConstrainedLanguageMode` (#1527)

### Version 2.0.1

Bug fixes:

* Correct the default foreground and background colors so the appropriate default value is set for `SelectionColor` (#1435)
* Add the `-Chord` parameter to `Get-PSReadLineKeyHandler` to allow searching for specific key bindings (#1298) (Thanks @theaquamarine)
* Update docs to reflect the new `-Chord` parameter to `Get-PSReadLineKeyHandler` (#1438)

### Version 2.0.0

Bug fixes:

* Name the `ReadKey` thread for debug purpose (#1313)
* Compliance: Update the .NET Core version and PowerShell SDK package (#1320)
* Build: Enable F5 debugging of PSReadLine in Visual Studio (#1319) (Thanks @theaquamarine)
* Build: Simply the way to get semantic version information in build (#1324)

### Version 2.0.0-rc2

Pre-release notes:

Bug fixes:

* Add `ProjectUri` in module manifest (#1245)
* Fix docs to note the `-PromptText` parameter of `Set-PSReadLineOption` is now of the `String[]` type (#1244) (Thanks @heaths)
* Update key bindings in sample and docs now that the `Shift` key is inferred (#1248)
* Update `HelpInfoURI` of the `PSReadLine` module to point to latest help content (#1251)
* Fix `RepeatLastCharSearch` and `RepeatLastCharSearchBackwards` to not flip their functionalities (#1253) (Thanks @mikebattista)
* Fix the exception when searching history with `ctrl+r` or `ctrl+s` (#1256)
* Preserve and clear the saved current line properly to make the history operations work as expected (#1259)
* Update release build with compliance tasks (#1260)
* Update README.md to put `PSReadLine` and `PowerShellGet` in backtick quotes (#1280)
* Fix a few VI edit mode issues (#1262)
* Fix and enhance `SmartInsertQuote` to work better with closing braces, keywords and variables (#1288, #1289) (Thanks @theaquamarine)
* Allow `InsertPairedBraces` to wrap selected text (#1293) (Thanks @theaquamarine)

### Version 2.0.0-rc1

Pre-release notes:

Bug fixes:

* Fix an assertion in debug build that crashes PSReadLine (#1199) (Thanks @springcomp)
* Fix an `IndexOutOfRangeException` in VI mode (#1200) (Thanks @springcomp)
* Improve handling of color in prompts (#1180) (Thanks @lzybkr)
* Fix an 'ArgumentOutOfRangeException' when error prompt contains CJK (#1206)
* Add instructions for updating `PowerShellGet` for Windows PowerShell users (#1222) (Thanks @espoelstra)
* Correct cursor positioning (#1221) (Thanks @msftrncs)
* Utilize LengthInBufferCells when creating Completions Menu (#1214) (Thanks @msftrncs)
* Ensure the desired column number is used while moving up or down in VI mode (#1122) (Thanks @springcomp)
* Make the continuation prompt's color sequence always explicitly specified (#1238)

### Version 2.0.0-beta6

Pre-release notes:

Bug fixes:

* Fix `GenerateRender` to not throw `IndexOutOfRangeException` and `NullReferenceException` (#1049)
* Filter sensitive history items and avoid writing them to the history file (#1058, #1061, #1068)
* Clear the previous menu rendering correctly (#1073)
* Fix correct way to get substring based on buffercells when encountering `CJK` chars (#1100)
* Correct cursor jumping from line 2 to line 1 due to line 1 being empty (#1108) (Thanks @msftrncs)
* In VI mode, moving left or right should stick cursor on logical line (#1120) (Thanks @springcomp)
* Fix for a `NullReferenceException` thrown when `PSES` calls `ForcePSEventHandling` (#1097)
* Return error if color property or value is invalid with `Set-PSReadLineOption -Colors` (#1124)
* Handle cursor being moved off the end of buffer in `MoveCursor` (#1146) (Thanks @msftrncs)
* Supporting line-wise yanks, including paste and undo (#811) (Thanks @springcomp)
* `MoveCursor` should not call `SetCursorPosition` when there is a pending rendering (#1141) (Thanks @msftrncs)
* Fix `y$` to yank to the end of the logical line instead of to the end of the whole buffer (#1168) (Thanks @springcomp)
* Make `y0` yank up to the start of the logical line in `VI` mode (#1167) (Thanks @springcomp)
* Fix `NullReferenceException` in `OnBreak` (#1179) (Thanks @lzybkr)
* Set cursor via the public API `SetCursorPosition` in `AcceptLineImpl` for better buffer check (#1182) (Thanks @msftrncs)

### Version 2.0.0-beta5

Pre-release notes:

Bug fixes:

* Handle the dead key on Windows properly (#933, #945)
* Fix duplicate menu at end of buffer (#937) (Thanks @parkovski)
* Get Buffer Info as part of the Environment data in bug reporting (#942)
* Add API to detect if the screen reader is active (#947)
* Fix `ArgumentOutOfRangeException` thrown when changing color of the error prompt (#967)
* Fix the 'ArgumentOutOfRangeException' caused by top of the text being scrolled up-off the buffer (#979)
* Add the missing single curly quote and double curly quote to `IsSingleQuote` and `IsDoubleQuote` checks (#985) (Thanks @msftrncs)
* Fix the color of operator token in the `ArgumentMode` in syntax highlighting (#1003) (Thanks @msftrncs)
* Prioritize highlight of command names, prevent bleed from aligned nested tokens in syntax highlighting (#989) (Thanks @msftrncs)
* Prevent 'ArgumentOutOfRangeException' when showing the tab completion menu (#984) (Thanks @msftrncs)
* Make `PageUp/PageDown` and `CtrlPageUp/CtrlPageDown` windows only (#1032)
* Fix `UpArrow` when the cursor is at the end of a wrapped line in a multiple-line text (#1028)

### Version 2.0.0-beta4

Pre-release notes:

Bug fixes:

* Add Shift-Tab Windows ANSI escape (#695) (Thanks @parkovski)
* Add script block vi mode indicator option (#695) (Thanks @parkovski)
* Fix initialization for non-US keyboards (#768)
* Fix unintentional font change in Windows console (#771)
* Fix tooltips and menu at bottom of buffer (#783)
* Respect the change to `[console]::BackgroundColor` by saving/restoring the initial foreground/background colors (#785)
* Remove uses of CSI # S / ScrollConsoleScreenBuffer (#790)
* Support vi mode `G` and `gg` movements in multi-line buffers (#793) (Thanks @springcomp!)
* Fix rendering issue when menu complete shifts due to length of completion (#802)
* Make Get-PSReadLineOption show AnsiEscapeTimeout (#800) (Thanks @jazzdelightsme!)
* Fix the crash in `ViAppendLine` when cursor is at the end of the input (#797) (Thanks @springcomp!)
* Fix cursor position during menu complete (#809)
* Supports `_` and `$` to move to the beginning and end of the logical line in vi mode (#812) (Thanks @springcomp!)
* Add `xtermjs` keybindings (#878)
* Fix the regression to make long lines work properly at the end of screen buffer (#895)
* Fix 'Shift+Backspace/Escape' to work the same as 'Backspace/Escape' on Windows (#898)
* Make sure to generate the 'OnIdle' event when there are other subscribers (#899)

### Version 2.0.0-beta3

Pre-release notes:

Bug fixes:

* Fix exception after tab completion with a small buffer (#704) (Thanks @jianyunt)
* Fix cursor placement after tab completion with progress (#708) (Thanks @jianyunt)
* Ensure space is always inserted (#719) (Thanks @powercode)
* Add support for VSCode/Atom (#626) (Thanks @SeeminglyScience)

### Version 2.0.0-beta2

Pre-release notes:

Bug fixes:

* Fix issue with keyboard layout changes (#667) (Thanks @powercode)
* Fix cursor placement after window resize (#682) (Thanks @jianyunt)
* Fix rendering of long lines on non-Windows (#686) (Thanks @jianyunt)
* Fix Ctrl+h/Backspace on non-Windows (#619) (Thanks @daxian-dbw)
* Fix some custom key bindings on Windows (#580) (Thanks @daxian-dbw)
* Fixed CompleteMenu in vi insert mode (#651) (Thanks @srdubya)
* Fix ExtraPromptLineCount for ClearScreen (#634) (Thanks @daxian-dbw)
* Support color escape sequences in PromptText (#653) (Thanks @powercode)
* Support Ctrl+u in vi insert mode (#628) (Thanks @srdubya)
* Fix keys needing AltGr (#617)
* Fix vi-mode cursor placement issues (#623) (Thanks @srdubya)
* Ignore exceptions when setting OutputEncoding
* Fix hang on closing on pre-Win10 (#609)
* Enable Ctrl+c on non-Windows (#610)
* Improve VT emulation for pre-Win10 (#605)

### Version 2.0.0-beta1

Pre-release notes:

There are known issues:

* Some custom key bindings are broken (#580)

Breaking changes:
* Requires PowerShell V5 or later and .Net 4.6.1
* Set-PSReadLineOption options have changed
  - To specify colors, use the new `-Color` parameter and pass a Hashtable
  - All other color options have been removed include `-ResetTokenColors`
  - To specify background colors, you must now use a VT escape sequence.
* Changing the end of the prompt to red may not work automatically anymore if
  your prompt is non-trivial. To fix, use `Set-PSReadLineOption -PromptText "> "`.
* Consistently use `PSReadLine` instead of `PSReadline` everywhere, APIs, cmdlets, files, etc.
* Building requires VS2017

New features:
* Interactive filtering during menu complete (#515) (Thanks @MVKozlov!)
* Redirected input now works (#564) (Thanks @parkovski!)
* Tooltips always on in MenuComplete (only displayed
  if they give more information than the completion.)
* Get-PSReadLineKeyHandler output is grouped by category. (#114)
* Support for VT escape sequences to specify colors.
* Shift+Insert bound to Paste in Windows mode (#484)
* Ctrl+t bound to SwapCharacters in Emacs mode (#538) (Thanks andrewcromwell!)
* Ctrl+x,Ctrl+e bound to ViEditVisually in Emacs mode (#478)
* HistoryNoDuplicates is now on by default (#208)

Bug fixes:
* DeleteChar no longer deletes any text left of the cursor in Windows/Emacs
  and matches vim behavior of working like <x> in command mode.
* Fix InvokePrompt when the prompt is > 1 line.
* Fix YankToPercent off by 1 error.
* Fix error reported when running in container.
* Catch and ignore exceptions in InvokePrompt (#583)
* Get new completions on 2nd tab if 1st had 1 result (#238)
* Tab replaced with 4 spaces during paste (#144)
* Fix rendering after buffer resize (#418)
* Invoke external editor w/o AcceptLine (#339)
* Fix exception with UpArrow after a command line edit in vi-mode (#573) (Thanks @srdubya!)
* Treat DingDuration=0 as BellStyle.None (#364)
* Color long name parameters like --force as a parameter (#398)
* Allow CaptureScreen to be used w/o a key binding (#419)
* Ignore duplicates during tab completion (#413)
* Fix exception with negative count in Delete (#502)
* Use correct help file name (#507)
* Fix exception in ShowKeyBinding w/ custom handler (#505)
* Normalize filename for saving history (#512)
* Treat end of buffer as whitespace for vi words (#536)
* Fix exception with invalid CompletionResult (#534)
* Fix exception with negative count in Kill* (#540) (Thanks andrewcromwell!)
* Fixes to SwapCharacters (#538) (Thanks andrewcromwell!)
* Fix cursor placement with CJK characters (#542)
* Fix key bindings with certain Windows keyboard layouts (#168 #556)
* Remove Ding from Backspace on an empty line (#422)
* Fix occasional hang reading history file (#524)
* HistorySearchCursorMovesToEnd previously only worked in incremental search.
  It is now honored in the non-incremental search (HistorySearch)
  and also in Vi Command mode for non-search history recall. (#438 #530) (Thanks @srdubya!)


### Version 1.2

New features:
* Vi editing mode

New functions:
* InsertLineAbove
    - A new empty line is created above the current line regardless of where the cursor
      is on the current line.  The cursor moves to the beginning of the new line.
* InsertLineBelow
    - A new empty line is created below the current line regardless of where the cursor
      is on the current line.  The cursor moves to the beginning of the new line.

New key bindings:
* Ctrl+Enter bound to InsertLineAbove in Windows mode
* Ctrl+Shift+Enter bound to InsertLineBelow in Windows mode

Bug fixes:
* Home the first line of multi-line input fixed
* CaptureScreen captures colors colors correctly instead of guessing
* CaptureScreen scrolls the screen to ensure the selected line is visible
* CaptureScreen allows j/k for up/down (for the vi fans)
* Fixed uncommon syntax coloring bug with bare words that start with a variable
* Added sample handler for F7 emulation
* Fixed sample handler for Set-StrictMode error
* Improved error message when errors occur in custom handlers

### Version 1.1 (shipped w/ Windows 10)

Breaking change:
* Namespace PSConsoleUtilities has been renamed to Microsoft.PowerShell
* Default history file location changed to match Windows 10

### Version ********

New features:
* Enter now does some extra validation before accepting the input.  If there are any parse
  errors or a command is not found, an error message is displayed, but you can continue editing,
  the erroneous line will not be added to history, and the error message will be cleared after
  you make an edit.
  You can press Enter a second time to force accepting the line if you choose.

  If you don't like the new behavior for Enter, you can revert to the old behavior with:
      Set-PSReadLineKeyHandler -Key Enter -Function AcceptLine

Bug fixes:
* Occasional exception with AcceptAndGetNext (Ctrl+O) followed by something other than Enter
* Event handlers that register for the engine event PowerShell.OnIdle should work now.
* ClearScreen now scrolls the screen to preserve as much output as possible
* Fix exception on error input during rendering after certain keywords like process, begin, or end.
* Fix exception after undo from menu completion
* Support CancelLine (Ctrl+C) and Abort (Ctrl+G in emacs) to cancel DigitArgument
* History recall now ignores command lines from other currently running sessions, but you can
  still find command lines from those sessions when searching history.
* Color any non-whitespace prompt character when there is an error, not just non-characters
* ScrollDisplayToCursor works a little better now.

New functions:
* ValidateAndAcceptLine
    Validate the command line (by making sure there are no parse errors, commands all exist,
    and possibly other sorts of validation), display an error if any errors, but don't add
    the command to history and clear the error after an edit.
* DeleteCharOrExit
    - emulate Ctrl+D in bash properly by exiting the process if the command line is empty
* ScrollDisplayUpLine, ScrollDisplayDownLine
    Scroll the screen up or down by a line instead of by the screen

New key bindings:
* Ctrl+D bound to DeleteCharOrExit in Emacs mode
* Ctrl+N/Ctrl+P bound to NextHistory/PreviousHistory in Emacs mode
* Ctrl+PageUp bound to ScrollDisplayUpLine
* Ctrl+PageDown bound to ScrollDisplayDownLine
* Alt+F7 bound to ClearHistory in Windows mode

New options:
* Set-PSReadLineOption
      -ErrorForegroundColor
      -ErrorBackgroundColor
          Colors used when ValidateAndAcceptLine reports an error

      -CommandValidationHandler
          A delegate that is called from ValidateAndAcceptLine that can perform custom validation
          and also fix the command line, e.g. to correct common typos.

New cmdlet:
* Remove-PSReadLineKeyHandler
    This will remove a key binding for previously bound keys.

Breaking change:
* Demo mode removed
    - Trying to bind functions EnableDemoMode or DisableDemoMode will result in an error.

### Version ********

New features:
* Multi-line editing is now practical.  Home/End go to the start/end of the current line or
  start/end of the input in a reasonable way.  Up/Down arrows will move across lines if the
  input has multiple lines and you are not in the middle of recalling history.

Bug fixes:
* Color the prompt character if there is an error for any non-alphanumeric character
* Fix an issue related to undo (which was commonly hit via Escape) and using history search
* Fix a bug where PowerShell events are not written to the console until PSReadLine returns
* Fixed so PowerTab now works with PSReadLine
* Highlight from history search is cleared before accepting a line now.
* Fixed MenuComplete so it clears the menu (which only happened on some systems)

New functions:
* NextLine
* PreviousLine
    - These functions are added for completeness, neither is particularly useful as the usual
      bindings for UpArrow/DownArrow are smart enough to recall history or change lines
      depending on the context.

New key bindings:
* F8/Shift+F8 bound to HistorySearchBackward/HistorySearchForward in Windows mode

### Version ********

Bug fixes:
* Fixed MenuComplete to actually work

### Version ********

New features:
* Added binding Ctrl+SpaceBar to MenuComplete in Windows and Emacs modes
  - If you want to old behavior of Ctrl+Spacebar, bind it to PossibleCompletions
* Added binding Alt+. (YankLastArg) to Windows mode
* Added diagnostics when an exception occurs to help reporting bugs

Bug fixes:
* SaveHistoryPath option fixed
* Fix ShowKeyBindings to not write blank lines
* Fixed bug with undo

### Version 1.0.0.9

New features:
* MenuComplete - like an interactive show completion
* Automatically save history
    - at process exit
    - incrementally and shared across sessions
    - don't save
  See parameters HistorySaveStyle and HistorySavePath to Set-PSReadLineOption
* Added sample custom binding for quickly changing directories in SamplePSReadLineProfile.ps1

Bug fixes:
* Items loaded from history work with RevertLine
* Recalling current line after up arrow works again

### Version 1.0.0.8

New features:
* SamplePSReadLineProfile.ps1 added with examples of custom key bindings
* Word movement takes DigitArgument
* HistoryNoDuplicates now works a little differently
    - Duplicates are saved (it was a dubious memory optimization anyway)
    - Recall will recall the most recently executed item instead of the first
* When at the last word, NextWord/ForwardWord now move to the end of line instead
  of the last character of the word.
* HistorySearchBackward/HistorySearchForward changes behavior slightly:
    - use emphasis like InteractiveHistorySearch
    - cursor always moves to end like PreviousHistory/NextHistory
* New api GetSelectionState to get the current selection (if any).
* New functions:
    - SelectBackwardsLine
    - SelectLine
    - SelectAll
    - CopyOrCancelLine
* New key bindings in Windows mode:
    - Alt+0 through Alt+9 and Alt+-: DigitArgument
    - Ctrl+R/Ctrl+S for interactive history search

Bug fixes:
* Backspace after a failed interactive history search (Ctrl+R) caused searching
  to fail if you typed anything other than backspace.

### Version *******

New features:
* CaptureScreen - copies selected portion of screen to clipboard in text and rtf
* InvokePrompt - re-evaluate the prompt while preserving the current input
* New functions to scroll the screen w/o using the mouse:
    - ScrollScreenUp
    - ScrollScreenDown
    - ScrollScreenTop
    - ScrollScreenToCursor
* Many small bug fixes

### Version *******

New features:
* CharacterSearch/CharacterSearchBackward
* AcceptAndGetNext (Ctrl+O in bash)
* Get-PSReadLineKeyHandler now returns unbound functions
* Get-PSReadLineKeyHandler has 2 new parameters: -Bound and -Unbound
* Set-PSReadLineKeyHandler parameter -LongDescription is now -Description
  (not breaking because an alias was left)
* WhatIsKey - display binding for a key
* ShowKeyBindings - show all bound keys
* Keyboard selection of text for cut/copy/delete.  New functions:
  - Cut
  - Copy
  - KillRegion
  - SelectBackwardChar
  - SelectForwardChar
  - SelectBackwardWord
  - SelectForwardWord
  - SelectNextWord
  - SelectShellForwardWord
  - SelectShellBackwardWord

Breaking change:
* The properties in the output of Get-PSReadLineKeyHandler have changed.
  This is unlikely to break anyone though.

### Version *******

New features:
* Delimiter support in *Word functions
* DigitArgument (Alt-0,Alt-1,Alt-2,...,Alt-9,Alt--) to pass numeric arguments
* YankLastArg/YankNthArg to extract arguments from previous command lines
* History search is now case insensitive with an option to make it case sensitive

Bugs fixed:
* Shift+Backspace works like Backspace
* Ctrl+R with long search lines no longer causes big problems

Behavior change:
* Word functions now use delimiters.  The previous behavior is available
  via a Shell*Word function, e.g. instead of KillWord, use ShellKillWord.

### Version 1.0.0.4

New features:
* Interactive history search (Ctrl+R)
* Brace matching function added (GotoBrace)
* Clear screen (Ctrl+L)

Bugs fixed:
* When showing possible completions, truncate at newline
* Prompt before showing a large number of completions
* Undo after paste works now
* Long pause after clicking on X to close powershell is now fixed

### Version 1.0.0.3

Bugs fixed:
* Removed CLR 4.5 dependency
* Fix bug where console paste didn't display everything in some cases

### Version 1.0.0.2

New features:
* Add a "demo mode" that shows keys pressed
* Add ETW event source for demo mode, key logger, macro recorder etc.
* Undo/redo
* Get-PSReadLineOption cmdlet
* Make specifying key handlers for built-ins simpler
* Current un-entered line is saved and recalled when cycling through history
* Support syntax coloring of member names

Bugs fixed:
* Speed up pasting from the console
* Fix coloring of some operators
* Fix coloring in some command arguments
* Ctrl-C is handled a little better

Breaking changes:
* CLR 4.5 is now required.
* SetBufferState is gone because it doesn't support Undo/Redo

### Version 1.0.0.1

New features:
* History imported when module is loaded
* Ctrl+End/Ctrl+Home bindings emulate cmd
* Arbitrary two key chords
* Key handlers passed the invoking key and an optional argument
* Made Ding public for custom handlers

Bugs fixed:
* Alternate keyboards now supported
* Ctrl-C now properly emulates cmd

Breaking changes:
* MinimumHistoryCommandLength parameter removed from Set-PSReadLineOption
    - Can use this instead:
        Set-PSReadLineOption -AddToHistoryHandler { $args[0].Length -gt 3 }

### Version 1.0.0.0

Initial release

