import{S as W,i as H,s as N,a as x,D as U,t as m,q as d,g as O,ae as P,E as A,G as V,a0 as D,I as Q,j as T,d as h,o as b,p as z,a8 as E,h as f,c as p,e as F,F as v,V as G,U as g,J as C,K as j,L as w,M as y,W as X}from"./SpinnerAugment-VfHtkDdv.js";import{B as Y}from"./IconButtonAugment-BlRCK7lJ.js";const Z=s=>({}),I=s=>({}),_=s=>({}),J=s=>({});function K(s){let t,l;const c=s[10].iconLeft,o=C(c,s,s[20],J);return{c(){t=v("div"),o&&o.c(),f(t,"class","c-button--icon svelte-1u3rjsd")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&j(o,c,i,i[20],l?y(c,i[20],a,_):w(i[20]),J)},i(i){l||(d(o,i),l=!0)},o(i){m(o,i),l=!1},d(i){i&&h(t),o&&o.d(i)}}}function M(s){let t,l,c;return l=new X({props:{size:s[0]===.5?1:s[0],weight:s[1]==="ghost"?"regular":"medium",$$slots:{default:[tt]},$$scope:{ctx:s}}}),{c(){t=v("div"),V(l.$$.fragment),f(t,"class","c-button--text svelte-1u3rjsd")},m(o,i){p(o,t,i),A(l,t,null),c=!0},p(o,i){const a={};1&i&&(a.size=o[0]===.5?1:o[0]),2&i&&(a.weight=o[1]==="ghost"?"regular":"medium"),1048576&i&&(a.$$scope={dirty:i,ctx:o}),l.$set(a)},i(o){c||(d(l.$$.fragment,o),c=!0)},o(o){m(l.$$.fragment,o),c=!1},d(o){o&&h(t),U(l)}}}function tt(s){let t;const l=s[10].default,c=C(l,s,s[20],null);return{c(){c&&c.c()},m(o,i){c&&c.m(o,i),t=!0},p(o,i){c&&c.p&&(!t||1048576&i)&&j(c,l,o,o[20],t?y(l,o[20],i,null):w(o[20]),null)},i(o){t||(d(c,o),t=!0)},o(o){m(c,o),t=!1},d(o){c&&c.d(o)}}}function S(s){let t,l;const c=s[10].iconRight,o=C(c,s,s[20],I);return{c(){t=v("div"),o&&o.c(),f(t,"class","c-button--icon svelte-1u3rjsd")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&j(o,c,i,i[20],l?y(c,i[20],a,Z):w(i[20]),I)},i(i){l||(d(o,i),l=!0)},o(i){m(o,i),l=!1},d(i){i&&h(t),o&&o.d(i)}}}function it(s){let t,l,c,o,i,a=s[9].iconLeft&&K(s),u=s[9].default&&M(s),r=s[9].iconRight&&S(s);return{c(){t=v("div"),a&&a.c(),l=G(),u&&u.c(),c=G(),r&&r.c(),f(t,"class",o=E(`c-button--content c-button--size-${s[0]}`)+" svelte-1u3rjsd")},m(n,$){p(n,t,$),a&&a.m(t,null),F(t,l),u&&u.m(t,null),F(t,c),r&&r.m(t,null),i=!0},p(n,$){n[9].iconLeft?a?(a.p(n,$),512&$&&d(a,1)):(a=K(n),a.c(),d(a,1),a.m(t,l)):a&&(b(),m(a,1,1,()=>{a=null}),z()),n[9].default?u?(u.p(n,$),512&$&&d(u,1)):(u=M(n),u.c(),d(u,1),u.m(t,c)):u&&(b(),m(u,1,1,()=>{u=null}),z()),n[9].iconRight?r?(r.p(n,$),512&$&&d(r,1)):(r=S(n),r.c(),d(r,1),r.m(t,null)):r&&(b(),m(r,1,1,()=>{r=null}),z()),(!i||1&$&&o!==(o=E(`c-button--content c-button--size-${n[0]}`)+" svelte-1u3rjsd"))&&f(t,"class",o)},i(n){i||(d(a),d(u),d(r),i=!0)},o(n){m(a),m(u),m(r),i=!1},d(n){n&&h(t),a&&a.d(),u&&u.d(),r&&r.d()}}}function ot(s){let t,l;const c=[{size:s[0]},{variant:s[1]},{color:s[2]},{highContrast:s[3]},{disabled:s[4]},{loading:s[6]},{alignment:s[7]},{radius:s[5]},s[8]];let o={$$slots:{default:[it]},$$scope:{ctx:s}};for(let i=0;i<c.length;i+=1)o=x(o,c[i]);return t=new Y({props:o}),t.$on("click",s[11]),t.$on("keyup",s[12]),t.$on("keydown",s[13]),t.$on("mousedown",s[14]),t.$on("mouseover",s[15]),t.$on("focus",s[16]),t.$on("mouseleave",s[17]),t.$on("blur",s[18]),t.$on("contextmenu",s[19]),{c(){V(t.$$.fragment)},m(i,a){A(t,i,a),l=!0},p(i,[a]){const u=511&a?O(c,[1&a&&{size:i[0]},2&a&&{variant:i[1]},4&a&&{color:i[2]},8&a&&{highContrast:i[3]},16&a&&{disabled:i[4]},64&a&&{loading:i[6]},128&a&&{alignment:i[7]},32&a&&{radius:i[5]},256&a&&P(i[8])]):{};1049091&a&&(u.$$scope={dirty:a,ctx:i}),t.$set(u)},i(i){l||(d(t.$$.fragment,i),l=!0)},o(i){m(t.$$.fragment,i),l=!1},d(i){U(t,i)}}}function st(s,t,l){const c=["size","variant","color","highContrast","disabled","radius","loading","alignment"];let o=D(t,c),{$$slots:i={},$$scope:a}=t;const u=Q(i);let{size:r=2}=t,{variant:n="solid"}=t,{color:$="neutral"}=t,{highContrast:k=!1}=t,{disabled:L=!1}=t,{radius:R="medium"}=t,{loading:B=!1}=t,{alignment:q="center"}=t;return s.$$set=e=>{t=x(x({},t),T(e)),l(8,o=D(t,c)),"size"in e&&l(0,r=e.size),"variant"in e&&l(1,n=e.variant),"color"in e&&l(2,$=e.color),"highContrast"in e&&l(3,k=e.highContrast),"disabled"in e&&l(4,L=e.disabled),"radius"in e&&l(5,R=e.radius),"loading"in e&&l(6,B=e.loading),"alignment"in e&&l(7,q=e.alignment),"$$scope"in e&&l(20,a=e.$$scope)},[r,n,$,k,L,R,B,q,o,u,i,function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},a]}class et extends W{constructor(t){super(),H(this,t,st,ot,N,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5,loading:6,alignment:7})}}export{et as B};
