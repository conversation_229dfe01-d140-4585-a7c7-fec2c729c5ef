/**
 * Refactoring Engine - Motor de Refatoração Inteligente
 * Refatoração segura com análise de impacto
 */

const vscode = require('vscode');

class RefactoringEngine {
    constructor() {
        this.refactoringRules = new Map();
        this.impactAnalyzer = null;
        this.isInitialized = false;
    }

    async initialize() {
        if (this.isInitialized) return;

        console.log('🔧 Inicializando Motor de Refatoração...');
        
        this.setupRefactoringRules();
        this.setupImpactAnalyzer();
        
        this.isInitialized = true;
        console.log('✅ Motor de Refatoração inicializado');
    }

    setupRefactoringRules() {
        // Regras de refatoração por linguagem
        this.refactoringRules.set('javascript', {
            extractFunction: this.extractFunctionJS.bind(this),
            extractVariable: this.extractVariableJS.bind(this),
            renameSymbol: this.renameSymbolJS.bind(this),
            moveFunction: this.moveFunctionJS.bind(this),
            splitClass: this.splitClassJS.bind(this),
            mergeClasses: this.mergeClassesJS.bind(this),
            eliminateDeadCode: this.eliminateDeadCodeJS.bind(this),
            optimizeImports: this.optimizeImportsJS.bind(this)
        });

        this.refactoringRules.set('python', {
            extractFunction: this.extractFunctionPython.bind(this),
            extractVariable: this.extractVariablePython.bind(this),
            renameSymbol: this.renameSymbolPython.bind(this),
            moveFunction: this.moveFunctionPython.bind(this),
            splitClass: this.splitClassPython.bind(this),
            optimizeImports: this.optimizeImportsPython.bind(this)
        });

        this.refactoringRules.set('generic', {
            extractFunction: this.extractFunctionGeneric.bind(this),
            extractVariable: this.extractVariableGeneric.bind(this),
            renameSymbol: this.renameSymbolGeneric.bind(this)
        });
    }

    setupImpactAnalyzer() {
        this.impactAnalyzer = {
            analyzeRenameImpact: this.analyzeRenameImpact.bind(this),
            analyzeMoveImpact: this.analyzeMoveImpact.bind(this),
            analyzeExtractionImpact: this.analyzeExtractionImpact.bind(this),
            analyzeDeletionImpact: this.analyzeDeletionImpact.bind(this)
        };
    }

    async refactorCode(code, refactoringType, options, context) {
        try {
            const language = this.detectLanguage(context.currentFile?.language || 'generic');
            const rules = this.refactoringRules.get(language) || this.refactoringRules.get('generic');
            
            if (!rules[refactoringType]) {
                throw new Error(`Refatoração '${refactoringType}' não suportada para ${language}`);
            }

            // Análise de impacto antes da refatoração
            const impactAnalysis = await this.analyzeImpact(code, refactoringType, options, context);
            
            if (impactAnalysis.hasBreakingChanges) {
                const proceed = await this.confirmBreakingChanges(impactAnalysis);
                if (!proceed) {
                    return { cancelled: true, reason: 'Usuário cancelou devido a mudanças críticas' };
                }
            }

            // Executar refatoração
            const result = await rules[refactoringType](code, options, context);
            
            // Validar resultado
            const validation = await this.validateRefactoring(code, result.code, context);
            
            return {
                originalCode: code,
                refactoredCode: result.code,
                changes: result.changes,
                impactAnalysis,
                validation,
                suggestions: result.suggestions || []
            };

        } catch (error) {
            console.error('Erro na refatoração:', error);
            return {
                error: error.message,
                originalCode: code
            };
        }
    }

    detectLanguage(language) {
        const languageMap = {
            'javascript': 'javascript',
            'typescript': 'javascript',
            'javascriptreact': 'javascript',
            'typescriptreact': 'javascript',
            'python': 'python',
            'java': 'java',
            'csharp': 'csharp'
        };

        return languageMap[language] || 'generic';
    }

    async analyzeImpact(code, refactoringType, options, context) {
        const analysis = {
            hasBreakingChanges: false,
            affectedFiles: [],
            affectedSymbols: [],
            risks: [],
            recommendations: []
        };

        switch (refactoringType) {
            case 'renameSymbol':
                return await this.impactAnalyzer.analyzeRenameImpact(code, options, context);
            case 'moveFunction':
                return await this.impactAnalyzer.analyzeMoveImpact(code, options, context);
            case 'extractFunction':
                return await this.impactAnalyzer.analyzeExtractionImpact(code, options, context);
            default:
                return analysis;
        }
    }

    async analyzeRenameImpact(code, options, context) {
        const analysis = {
            hasBreakingChanges: false,
            affectedFiles: [],
            affectedSymbols: [],
            risks: [],
            recommendations: []
        };

        const { oldName, newName } = options;
        
        // Verificar se o símbolo é usado em outros arquivos
        if (context.relatedFiles) {
            for (const file of context.relatedFiles) {
                if (file.content && file.content.includes(oldName)) {
                    analysis.affectedFiles.push(file.path);
                    analysis.hasBreakingChanges = true;
                }
            }
        }

        // Verificar conflitos de nome
        if (context.symbols) {
            const conflictingSymbol = context.symbols.find(s => s.name === newName);
            if (conflictingSymbol) {
                analysis.risks.push(`Conflito de nome: '${newName}' já existe em ${conflictingSymbol.file}`);
                analysis.hasBreakingChanges = true;
            }
        }

        if (analysis.hasBreakingChanges) {
            analysis.recommendations.push('Considere usar "Find and Replace" em todo o projeto');
            analysis.recommendations.push('Execute testes após a refatoração');
        }

        return analysis;
    }

    async analyzeMoveImpact(code, options, context) {
        const analysis = {
            hasBreakingChanges: false,
            affectedFiles: [],
            affectedSymbols: [],
            risks: [],
            recommendations: []
        };

        const { functionName, targetFile } = options;
        
        // Verificar dependências da função
        const functionDependencies = this.extractFunctionDependencies(code, functionName);
        
        if (functionDependencies.length > 0) {
            analysis.risks.push(`Função depende de: ${functionDependencies.join(', ')}`);
            analysis.hasBreakingChanges = true;
        }

        // Verificar se a função é usada externamente
        if (context.relatedFiles) {
            for (const file of context.relatedFiles) {
                if (file.content && file.content.includes(functionName)) {
                    analysis.affectedFiles.push(file.path);
                    analysis.hasBreakingChanges = true;
                }
            }
        }

        if (analysis.hasBreakingChanges) {
            analysis.recommendations.push('Atualize imports nos arquivos afetados');
            analysis.recommendations.push('Mova dependências junto com a função');
        }

        return analysis;
    }

    async analyzeExtractionImpact(code, options, context) {
        const analysis = {
            hasBreakingChanges: false,
            affectedFiles: [],
            affectedSymbols: [],
            risks: [],
            recommendations: []
        };

        const { startLine, endLine, newFunctionName } = options;
        
        // Extrair código selecionado
        const lines = code.split('\n');
        const selectedCode = lines.slice(startLine - 1, endLine).join('\n');
        
        // Analisar variáveis usadas
        const usedVariables = this.extractUsedVariables(selectedCode);
        const declaredVariables = this.extractDeclaredVariables(selectedCode);
        
        // Verificar variáveis que precisam ser passadas como parâmetros
        const externalVariables = usedVariables.filter(v => !declaredVariables.includes(v));
        
        if (externalVariables.length > 0) {
            analysis.recommendations.push(`Parâmetros necessários: ${externalVariables.join(', ')}`);
        }

        // Verificar se há valores de retorno
        const hasReturn = selectedCode.includes('return');
        if (hasReturn) {
            analysis.recommendations.push('Função extraída retorna valor - verifique o tipo de retorno');
        }

        return analysis;
    }

    async analyzeDeletionImpact(code, options, context) {
        // Implementar análise de impacto para deleção
        return {
            hasBreakingChanges: false,
            affectedFiles: [],
            affectedSymbols: [],
            risks: [],
            recommendations: []
        };
    }

    async confirmBreakingChanges(impactAnalysis) {
        const message = `Esta refatoração pode causar mudanças críticas:\n\n` +
                       `Arquivos afetados: ${impactAnalysis.affectedFiles.length}\n` +
                       `Riscos: ${impactAnalysis.risks.join(', ')}\n\n` +
                       `Deseja continuar?`;
        
        const result = await vscode.window.showWarningMessage(
            message,
            { modal: true },
            'Continuar',
            'Cancelar'
        );
        
        return result === 'Continuar';
    }

    async validateRefactoring(originalCode, refactoredCode, context) {
        const validation = {
            isValid: true,
            errors: [],
            warnings: [],
            suggestions: []
        };

        try {
            // Validação básica de sintaxe
            const syntaxCheck = this.checkSyntax(refactoredCode, context.currentFile?.language);
            if (!syntaxCheck.isValid) {
                validation.isValid = false;
                validation.errors.push(...syntaxCheck.errors);
            }

            // Verificar se não há perda de funcionalidade
            const functionalityCheck = this.checkFunctionality(originalCode, refactoredCode);
            if (!functionalityCheck.isValid) {
                validation.warnings.push(...functionalityCheck.warnings);
            }

            // Verificar melhorias de qualidade
            const qualityCheck = this.checkQualityImprovement(originalCode, refactoredCode);
            validation.suggestions.push(...qualityCheck.suggestions);

        } catch (error) {
            validation.isValid = false;
            validation.errors.push('Erro na validação: ' + error.message);
        }

        return validation;
    }

    checkSyntax(code, language) {
        // Verificação básica de sintaxe
        const result = { isValid: true, errors: [] };
        
        try {
            if (language === 'javascript' || language === 'typescript') {
                // Verificações básicas para JavaScript
                const openBraces = (code.match(/{/g) || []).length;
                const closeBraces = (code.match(/}/g) || []).length;
                
                if (openBraces !== closeBraces) {
                    result.isValid = false;
                    result.errors.push('Chaves não balanceadas');
                }

                const openParens = (code.match(/\(/g) || []).length;
                const closeParens = (code.match(/\)/g) || []).length;
                
                if (openParens !== closeParens) {
                    result.isValid = false;
                    result.errors.push('Parênteses não balanceados');
                }
            }
        } catch (error) {
            result.isValid = false;
            result.errors.push('Erro na verificação de sintaxe: ' + error.message);
        }

        return result;
    }

    checkFunctionality(originalCode, refactoredCode) {
        const result = { isValid: true, warnings: [] };
        
        // Verificar se o número de funções é similar
        const originalFunctions = (originalCode.match(/function\s+\w+/g) || []).length;
        const refactoredFunctions = (refactoredCode.match(/function\s+\w+/g) || []).length;
        
        if (Math.abs(originalFunctions - refactoredFunctions) > 1) {
            result.warnings.push('Número de funções mudou significativamente');
        }

        return result;
    }

    checkQualityImprovement(originalCode, refactoredCode) {
        const suggestions = [];
        
        // Verificar redução de complexidade
        const originalComplexity = this.calculateComplexity(originalCode);
        const refactoredComplexity = this.calculateComplexity(refactoredCode);
        
        if (refactoredComplexity < originalComplexity) {
            suggestions.push(`Complexidade reduzida de ${originalComplexity} para ${refactoredComplexity}`);
        }

        // Verificar redução de linhas
        const originalLines = originalCode.split('\n').length;
        const refactoredLines = refactoredCode.split('\n').length;
        
        if (refactoredLines < originalLines) {
            suggestions.push(`Código reduzido de ${originalLines} para ${refactoredLines} linhas`);
        }

        return { suggestions };
    }

    calculateComplexity(code) {
        // Cálculo simplificado de complexidade ciclomática
        const controlStructures = [
            /if\s*\(/g,
            /else\s+if\s*\(/g,
            /while\s*\(/g,
            /for\s*\(/g,
            /switch\s*\(/g,
            /case\s+/g,
            /catch\s*\(/g,
            /&&/g,
            /\|\|/g,
            /\?/g
        ];

        let complexity = 1;
        
        controlStructures.forEach(regex => {
            const matches = code.match(regex);
            if (matches) {
                complexity += matches.length;
            }
        });

        return complexity;
    }

    extractFunctionDependencies(code, functionName) {
        const dependencies = [];
        
        // Extrair função específica
        const functionRegex = new RegExp(`function\\s+${functionName}[^{]*{([^{}]*{[^{}]*}[^{}]*)*[^{}]*}`, 'g');
        const match = functionRegex.exec(code);
        
        if (match) {
            const functionBody = match[0];
            
            // Buscar por chamadas de função
            const functionCalls = functionBody.match(/\b\w+\s*\(/g);
            if (functionCalls) {
                functionCalls.forEach(call => {
                    const funcName = call.replace(/\s*\($/, '');
                    if (funcName !== functionName && !['console', 'Math', 'Object', 'Array'].includes(funcName)) {
                        dependencies.push(funcName);
                    }
                });
            }
        }

        return [...new Set(dependencies)]; // Remover duplicatas
    }

    extractUsedVariables(code) {
        const variables = [];
        
        // Regex simples para identificar variáveis
        const variableRegex = /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g;
        let match;
        
        while ((match = variableRegex.exec(code)) !== null) {
            const varName = match[1];
            if (!['function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'return'].includes(varName)) {
                variables.push(varName);
            }
        }

        return [...new Set(variables)];
    }

    extractDeclaredVariables(code) {
        const variables = [];
        
        // Buscar declarações de variáveis
        const declarationRegex = /(?:var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
        let match;
        
        while ((match = declarationRegex.exec(code)) !== null) {
            variables.push(match[1]);
        }

        return variables;
    }

    // Implementações específicas de refatoração (JavaScript)
    async extractFunctionJS(code, options, context) {
        const { startLine, endLine, newFunctionName } = options;
        const lines = code.split('\n');
        
        // Extrair código selecionado
        const selectedCode = lines.slice(startLine - 1, endLine).join('\n');
        
        // Analisar variáveis
        const usedVariables = this.extractUsedVariables(selectedCode);
        const declaredVariables = this.extractDeclaredVariables(selectedCode);
        const parameters = usedVariables.filter(v => !declaredVariables.includes(v));
        
        // Criar nova função
        const newFunction = `
function ${newFunctionName}(${parameters.join(', ')}) {
${selectedCode.split('\n').map(line => '    ' + line).join('\n')}
}`;

        // Substituir código original pela chamada da função
        const functionCall = `${newFunctionName}(${parameters.join(', ')});`;
        
        const newLines = [
            ...lines.slice(0, startLine - 1),
            functionCall,
            ...lines.slice(endLine),
            '',
            newFunction
        ];

        return {
            code: newLines.join('\n'),
            changes: [`Função '${newFunctionName}' extraída`],
            suggestions: ['Considere mover a nova função para um local mais apropriado']
        };
    }

    async extractVariableJS(code, options, context) {
        const { expression, variableName, line } = options;
        const lines = code.split('\n');
        
        // Substituir expressão pela variável
        const targetLine = lines[line - 1];
        const newDeclaration = `const ${variableName} = ${expression};`;
        const modifiedLine = targetLine.replace(expression, variableName);
        
        const newLines = [
            ...lines.slice(0, line - 1),
            newDeclaration,
            modifiedLine,
            ...lines.slice(line)
        ];

        return {
            code: newLines.join('\n'),
            changes: [`Variável '${variableName}' extraída`]
        };
    }

    async renameSymbolJS(code, options, context) {
        const { oldName, newName } = options;
        
        // Substituição simples (em produção, seria mais sofisticada)
        const newCode = code.replace(new RegExp(`\\b${oldName}\\b`, 'g'), newName);
        
        return {
            code: newCode,
            changes: [`Símbolo '${oldName}' renomeado para '${newName}'`]
        };
    }

    // Implementações genéricas
    async extractFunctionGeneric(code, options, context) {
        return await this.extractFunctionJS(code, options, context);
    }

    async extractVariableGeneric(code, options, context) {
        return await this.extractVariableJS(code, options, context);
    }

    async renameSymbolGeneric(code, options, context) {
        return await this.renameSymbolJS(code, options, context);
    }

    // Métodos auxiliares para outras refatorações
    async moveFunctionJS(code, options, context) {
        // Implementar movimentação de função
        return { code, changes: ['Movimentação de função não implementada'] };
    }

    async splitClassJS(code, options, context) {
        // Implementar divisão de classe
        return { code, changes: ['Divisão de classe não implementada'] };
    }

    async mergeClassesJS(code, options, context) {
        // Implementar fusão de classes
        return { code, changes: ['Fusão de classes não implementada'] };
    }

    async eliminateDeadCodeJS(code, options, context) {
        // Implementar eliminação de código morto
        return { code, changes: ['Eliminação de código morto não implementada'] };
    }

    async optimizeImportsJS(code, options, context) {
        // Implementar otimização de imports
        return { code, changes: ['Otimização de imports não implementada'] };
    }

    // Implementações Python (básicas)
    async extractFunctionPython(code, options, context) {
        // Implementação similar ao JavaScript, mas com sintaxe Python
        return { code, changes: ['Extração de função Python não implementada'] };
    }

    async extractVariablePython(code, options, context) {
        return { code, changes: ['Extração de variável Python não implementada'] };
    }

    async renameSymbolPython(code, options, context) {
        const { oldName, newName } = options;
        const newCode = code.replace(new RegExp(`\\b${oldName}\\b`, 'g'), newName);
        
        return {
            code: newCode,
            changes: [`Símbolo '${oldName}' renomeado para '${newName}'`]
        };
    }

    async moveFunctionPython(code, options, context) {
        return { code, changes: ['Movimentação de função Python não implementada'] };
    }

    async splitClassPython(code, options, context) {
        return { code, changes: ['Divisão de classe Python não implementada'] };
    }

    async optimizeImportsPython(code, options, context) {
        return { code, changes: ['Otimização de imports Python não implementada'] };
    }
}

module.exports = RefactoringEngine;
