<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.PowerShell.EditorServices</name>
    </assembly>
    <members>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService">
            <summary>
            Service for managing the editor context from PSES extensions.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService.GetCurrentLspFileContextAsync">
            <summary>
            Get the file context of the currently open file.
            </summary>
            <returns>The file context of the currently open file.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService.OpenNewUntitledFileAsync">
            <summary>
            Open a fresh untitled file in the editor.
            </summary>
            <returns>A task that resolves when the file has been opened.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService.OpenFileAsync(System.Uri)">
            <summary>
            Open the given file in the editor.
            </summary>
            <param name="fileUri">The absolute URI to the file to open.</param>
            <returns>A task that resolves when the file has been opened.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService.OpenFileAsync(System.Uri,System.Boolean)">
            <summary>
            Open the given file in the editor.
            </summary>
            <param name="fileUri">The absolute URI to the file to open.</param>
            <param name="preview">If true, open the file as a preview.</param>
            <returns>A task that resolves when the file is opened.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService.CloseFileAsync(System.Uri)">
            <summary>
            Close the given file in the editor.
            </summary>
            <param name="fileUri">The absolute URI to the file to close.</param>
            <returns>A task that resolves when the file has been closed.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService.SaveFileAsync(System.Uri)">
            <summary>
            Save the given file in the editor.
            </summary>
            <param name="fileUri">The absolute URI of the file to save.</param>
            <returns>A task that resolves when the file has been saved.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService.SaveFileAsync(System.Uri,System.Uri)">
            <summary>
            Save the given file under a new name in the editor.
            </summary>
            <param name="oldFileUri">The absolute URI of the file to save.</param>
            <param name="newFileUri">The absolute URI of the location to save the file.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService.SetSelectionAsync(Microsoft.PowerShell.EditorServices.Extensions.ILspFileRange)">
            <summary>
            Set the selection in the currently focused editor window.
            </summary>
            <param name="range">The range in the file to select.</param>
            <returns>A task that resolves when the range has been selected.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorContextService.InsertTextAsync(System.Uri,System.String,Microsoft.PowerShell.EditorServices.Extensions.ILspFileRange)">
            <summary>
            Insert text into a given file.
            </summary>
            <param name="fileUri">The absolute URI of the file to insert text into.</param>
            <param name="text">The text to insert into the file.</param>
            <param name="range">The range over which to insert the given text.</param>
            <returns>A task that resolves when the text has been inserted.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider">
            <summary>
            Object to provide extension service APIs to extensions to PSES.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.LanguageServer">
            <summary>
            A service wrapper around the language server allowing sending notifications and requests to the LSP client.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.ExtensionCommands">
            <summary>
            Service providing extension command registration and functionality.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.Workspace">
            <summary>
            Service providing editor workspace functionality.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.EditorContext">
            <summary>
            Service providing current editor context functionality.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.EditorUI">
            <summary>
            Service providing editor UI functionality.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.GetService(System.String)">
            <summary>
            Get an underlying service object from PSES by type name.
            </summary>
            <param name="psesServiceFullTypeName">The full type name of the service to get.</param>
            <returns>The service object requested, or null if no service of that type name exists.</returns>
            <remarks>
            This method is intended as a trapdoor and should not be used in the first instance.
            Consider using the public extension services if possible.
            </remarks>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.GetService(System.String,System.String)">
            <summary>
            Get an underlying service object from PSES by type name.
            </summary>
            <param name="fullTypeName">The full type name of the service to get.</param>
            <param name="assemblyName">The assembly name from which the service comes.</param>
            <returns>The service object requested, or null if no service of that type name exists.</returns>
            <remarks>
            This method is intended as a trapdoor and should not be used in the first instance.
            Consider using the public extension services if possible.
            </remarks>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.GetServiceByAssemblyQualifiedName(System.String)">
            <summary>
            Get a PSES service by its fully assembly qualified name.
            </summary>
            <param name="asmQualifiedTypeName">The fully assembly qualified name of the service type to load.</param>
            <returns>The service corresponding to the given type, or null if none was found.</returns>
            <remarks>
            It's not recommended to run this method in parallel with anything,
            since the global reflection context change may have side effects in other threads.
            </remarks>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.GetService(System.Type)">
            <summary>
            Get an underlying service object from PSES by type name.
            </summary>
            <param name="serviceType">The type of the service to fetch.</param>
            <returns>The service object requested, or null if no service of that type name exists.</returns>
            <remarks>
            This method is intended as a trapdoor and should not be used in the first instance.
            Consider using the public extension services if possible.
            Also note that services in PSES may live in a separate assembly load context,
            meaning that a type of the seemingly correct name may fail to fetch to a service
            that is known under a type of the same name but loaded in a different context.
            </remarks>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.GetPsesAssemblyLoadContext">
            <summary>
            Get the assembly load context the PSES loads its dependencies into.
            In .NET Framework, this returns null.
            </summary>
            <returns>The assembly load context used for loading PSES, or null in .NET Framework.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.EditorExtensionServiceProvider.LoadAssemblyInPsesLoadContext(System.String)">
            <summary>
            Load the given assembly in the PSES assembly load context.
            In .NET Framework, this simple loads the assembly in the LoadFrom context.
            </summary>
            <param name="assemblyPath">The absolute path of the assembly to load.</param>
            <returns>The loaded assembly object.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.Services.PromptChoiceDetails">
            <summary>
            Object specifying a UI prompt option to display to the user.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.PromptChoiceDetails.#ctor(System.String,System.String)">
            <summary>
            Construct a prompt choice object for display in a prompt to the user.
            </summary>
            <param name="label">The label to identify this prompt choice. May not contain commas (',').</param>
            <param name="helpMessage">The message to display to users.</param>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.PromptChoiceDetails.Label">
            <summary>
            The label to identify this prompt message.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.PromptChoiceDetails.HelpMessage">
            <summary>
            The message to display to users in the UI for this prompt choice.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorUIService">
            <summary>
            A service to manipulate the editor user interface.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorUIService.PromptInputAsync(System.String)">
            <summary>
            Prompt input after displaying the given message.
            </summary>
            <param name="message">The message to display with the prompt.</param>
            <returns>The input entered by the user, or null if the prompt was canceled.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorUIService.PromptSelectionAsync(System.String,System.Collections.Generic.IReadOnlyList{Microsoft.PowerShell.EditorServices.Extensions.Services.PromptChoiceDetails})">
            <summary>
            Prompt a single selection from a set of choices.
            </summary>
            <param name="message">The message to display for the prompt.</param>
            <param name="choices">The choices to give the user.</param>
            <returns>The label of the selected choice, or null if the prompt was canceled.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorUIService.PromptSelectionAsync(System.String,System.Collections.Generic.IReadOnlyList{Microsoft.PowerShell.EditorServices.Extensions.Services.PromptChoiceDetails},System.Int32)">
            <summary>
            Prompt a single selection from a set of choices.
            </summary>
            <param name="message">The message to display for the prompt.</param>
            <param name="choices">The choices to give the user.</param>
            <param name="defaultChoiceIndex">The index in the choice list of the default choice.</param>
            <returns>The label of the selected choice, or null if the prompt was canceled.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorUIService.PromptMultipleSelectionAsync(System.String,System.Collections.Generic.IReadOnlyList{Microsoft.PowerShell.EditorServices.Extensions.Services.PromptChoiceDetails})">
            <summary>
            Prompt a set of selections from a list of choices.
            </summary>
            <param name="message">The message to display for the prompt.</param>
            <param name="choices">The choices to give the user.</param>
            <returns>A list of the labels of selected choices, or null if the prompt was canceled.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorUIService.PromptMultipleSelectionAsync(System.String,System.Collections.Generic.IReadOnlyList{Microsoft.PowerShell.EditorServices.Extensions.Services.PromptChoiceDetails},System.Collections.Generic.IReadOnlyList{System.Int32})">
            <summary>
            Prompt a set of selections from a list of choices.
            </summary>
            <param name="message">The message to display for the prompt.</param>
            <param name="choices">The choices to give the user.</param>
            <param name="defaultChoiceIndexes">A list of the indexes of choices to mark as default.</param>
            <returns>A list of the labels of selected choices, or null if the prompt was canceled.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.Services.IExtensionCommandService">
            <summary>
            Service for registration and invocation of extension commands.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IExtensionCommandService.InvokeCommandAsync(System.String,Microsoft.PowerShell.EditorServices.Extensions.EditorContext)">
            <summary>
            Invoke an extension command asynchronously.
            </summary>
            <param name="commandName">The name of the extension command to invoke.</param>
            <param name="editorContext">The editor context in which to invoke the command.</param>
            <returns>A task that resolves when the command has been run.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IExtensionCommandService.RegisterCommand(Microsoft.PowerShell.EditorServices.Extensions.EditorCommand)">
            <summary>
            Registers a new EditorCommand with the ExtensionService and
            causes its details to be sent to the host editor.
            </summary>
            <param name="editorCommand">The details about the editor command to be registered.</param>
            <returns>True if the command is newly registered, false if the command already exists.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IExtensionCommandService.UnregisterCommand(System.String)">
            <summary>
            Unregisters an existing EditorCommand based on its registered name.
            </summary>
            <param name="commandName">The name of the command to be unregistered.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IExtensionCommandService.GetCommands">
            <summary>
            Returns all registered EditorCommands.
            </summary>
            <returns>A list of all registered EditorCommands.</returns>
        </member>
        <member name="E:Microsoft.PowerShell.EditorServices.Extensions.Services.IExtensionCommandService.CommandAdded">
            <summary>
            Raised when a new editor command is added.
            </summary>
        </member>
        <member name="E:Microsoft.PowerShell.EditorServices.Extensions.Services.IExtensionCommandService.CommandUpdated">
            <summary>
            Raised when an existing editor command is updated.
            </summary>
        </member>
        <member name="E:Microsoft.PowerShell.EditorServices.Extensions.Services.IExtensionCommandService.CommandRemoved">
            <summary>
            Raised when an existing editor command is removed.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.Services.ILanguageServerService">
            <summary>
            Service allowing the sending of notifications and requests to the PowerShell LSP language client from the server.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.ILanguageServerService.SendNotification(System.String)">
            <summary>
            Send a parameterless notification.
            </summary>
            <param name="method">The method to send.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.ILanguageServerService.SendNotification``1(System.String,``0)">
            <summary>
            Send a notification with parameters.
            </summary>
            <typeparam name="T">The type of the parameter object.</typeparam>
            <param name="method">The method to send.</param>
            <param name="parameters">The parameters to send.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.ILanguageServerService.SendRequestAsync(System.String)">
            <summary>
            Send a parameterless request with no response output.
            </summary>
            <param name="method">The method to send.</param>
            <returns>A task that resolves when the request is acknowledged.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.ILanguageServerService.SendRequestAsync``1(System.String,``0)">
            <summary>
            Send a request with no response output.
            </summary>
            <typeparam name="T">The type of the request parameter object.</typeparam>
            <param name="method">The method to send.</param>
            <param name="parameters">The request parameter object/body.</param>
            <returns>A task that resolves when the request is acknowledged.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.ILanguageServerService.SendRequestAsync``1(System.String)">
            <summary>
            Send a parameterless request and get its response.
            </summary>
            <typeparam name="TResponse">The type of the response expected.</typeparam>
            <param name="method">The method to send.</param>
            <returns>A task that resolves to the response sent by the server.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.ILanguageServerService.SendRequestAsync``2(System.String,``0)">
            <summary>
            Send a request and get its response.
            </summary>
            <typeparam name="T">The type of the parameter object.</typeparam>
            <typeparam name="TResponse">The type of the response expected.</typeparam>
            <param name="method">The method to send.</param>
            <param name="parameters">The parameters to send.</param>
            <returns>A task that resolves to the response sent by the server.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorScriptFile">
            <summary>
            A script file in the current editor workspace.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorScriptFile.Uri">
            <summary>
            The URI of the script file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorScriptFile.Content">
            <summary>
            The text content of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorScriptFile.Lines">
            <summary>
            The lines of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorScriptFile.Ast">
            <summary>
            The PowerShell AST of the script in the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorScriptFile.Tokens">
            <summary>
            The PowerShell syntactic tokens of the script in the file.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.Services.IWorkspaceService">
            <summary>
            A service for querying and manipulating the editor workspace.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.IWorkspaceService.WorkspacePath">
            <summary>
            The root path of the workspace for the current editor.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.IWorkspaceService.FollowSymlinks">
            <summary>
            Indicates whether the editor is configured to follow symlinks.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.Services.IWorkspaceService.ExcludedFileGlobs">
            <summary>
            The list of file globs to exclude from workspace management.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IWorkspaceService.GetFile(System.Uri)">
            <summary>
            Get a file within the workspace.
            </summary>
            <param name="fileUri">The absolute URI of the file to get.</param>
            <returns>A representation of the file.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IWorkspaceService.TryGetFile(System.Uri,Microsoft.PowerShell.EditorServices.Extensions.Services.IEditorScriptFile@)">
            <summary>
            Attempt to get a file within the workspace.
            </summary>
            <param name="fileUri">The absolute URI of the file to get.</param>
            <param name="file">The file, if it was found.</param>
            <returns>True if the file was found, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.Services.IWorkspaceService.GetOpenedFiles">
            <summary>
            Get all the open files in the editor workspace.
            The result is not kept up to date as files are opened or closed.
            </summary>
            <returns>All open files in the editor workspace.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.EditorCommand">
            <summary>
            Provides details about a command that has been registered
            with the editor.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorCommand.Name">
            <summary>
            Gets the name which uniquely identifies the command.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorCommand.DisplayName">
            <summary>
            Gets the display name for the command.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorCommand.SuppressOutput">
            <summary>
            Gets the boolean which determines whether this command's
            output should be suppressed.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorCommand.ScriptBlock">
            <summary>
            Gets the ScriptBlock which can be used to execute the command.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorCommand.#ctor(System.String,System.String,System.Boolean,System.String)">
            <summary>
            Creates a new EditorCommand instance that invokes a cmdlet or
            function by name.
            </summary>
            <param name="commandName">The unique identifier name for the command.</param>
            <param name="displayName">The display name for the command.</param>
            <param name="suppressOutput">If true, causes output to be suppressed for this command.</param>
            <param name="cmdletName">The name of the cmdlet or function which will be invoked by this command.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorCommand.#ctor(System.String,System.String,System.Boolean,System.Management.Automation.ScriptBlock)">
            <summary>
            Creates a new EditorCommand instance that invokes a ScriptBlock.
            </summary>
            <param name="commandName">The unique identifier name for the command.</param>
            <param name="displayName">The display name for the command.</param>
            <param name="suppressOutput">If true, causes output to be suppressed for this command.</param>
            <param name="scriptBlock">The ScriptBlock which will be invoked by this command.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.EditorCommandAttribute">
            <summary>
            Provides an attribute that can be used to target PowerShell
            commands for import as editor commands.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorCommandAttribute.Name">
            <summary>
            Gets or sets the name which uniquely identifies the command.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorCommandAttribute.DisplayName">
            <summary>
            Gets or sets the display name for the command.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorCommandAttribute.SuppressOutput">
            <summary>
            Gets or sets a value indicating whether this command's output
            should be suppressed.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.EditorContext">
            <summary>
            Provides context for the host editor at the time of creation.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorContext.CurrentFile">
            <summary>
            Gets the FileContext for the active file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorContext.SelectedRange">
            <summary>
            Gets the BufferRange representing the current selection in the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorContext.CursorPosition">
            <summary>
            Gets the FilePosition representing the current cursor position.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorContext.#ctor(Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition,Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange,System.String)">
            <summary>
            Creates a new instance of the EditorContext class.
            </summary>
            <param name="editorOperations">An IEditorOperations implementation which performs operations in the editor.</param>
            <param name="currentFile">The ScriptFile that is in the active editor buffer.</param>
            <param name="cursorPosition">The position of the user's cursor in the active editor buffer.</param>
            <param name="selectedRange">The range of the user's selection in the active editor buffer.</param>
            <param name="language">Determines the language of the file.false If it is not specified, then it defaults to "Unknown"</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorContext.SetSelection(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Sets a selection in the host editor's active buffer.
            </summary>
            <param name="startLine">The 1-based starting line of the selection.</param>
            <param name="startColumn">The 1-based starting column of the selection.</param>
            <param name="endLine">The 1-based ending line of the selection.</param>
            <param name="endColumn">The 1-based ending column of the selection.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorContext.SetSelection(Microsoft.PowerShell.EditorServices.Extensions.FilePosition,Microsoft.PowerShell.EditorServices.Extensions.FilePosition)">
            <summary>
            Sets a selection in the host editor's active buffer.
            </summary>
            <param name="startPosition">The starting position of the selection.</param>
            <param name="endPosition">The ending position of the selection.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorContext.SetSelection(Microsoft.PowerShell.EditorServices.Extensions.FileRange)">
            <summary>
            Sets a selection in the host editor's active buffer.
            </summary>
            <param name="selectionRange">The range of the selection.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.IFilePosition">
            <summary>
            A 1-based file position, referring to a point in a file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.IFilePosition.Line">
            <summary>
            The line number of the file position.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.IFilePosition.Column">
            <summary>
            The column number of the file position.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.IFileRange">
            <summary>
            A 1-based file range, referring to a range within a file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.IFileRange.Start">
            <summary>
            The start position of the range.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.IFileRange.End">
            <summary>
            The end position of the range.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.IFileContext">
            <summary>
            A snapshot of a file, including the URI of the file
            and its textual contents when accessed.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.IFileContext.Uri">
            <summary>
            The URI of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.IFileContext.Content">
            <summary>
            The content of the file when it was accessed.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.ILspFilePosition">
            <summary>
            0-based position within a file, conformant with the Language Server Protocol.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.ILspFilePosition.Line">
            <summary>
            The line index of the position within the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.ILspFilePosition.Character">
            <summary>
            The character offset from the line of the position.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.ILspFileRange">
            <summary>
            0-based range within a file, conformant with the Language Server Protocol.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.ILspFileRange.Start">
            <summary>
            The start position of the range.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.ILspFileRange.End">
            <summary>
            The end position of the range.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.ILspCurrentFileContext">
            <summary>
            Snapshot of a file in focus in the editor.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.ILspCurrentFileContext.Language">
            <summary>
            The language the editor associates with this file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.ILspCurrentFileContext.CursorPosition">
            <summary>
            The position of the cursor within the file when it was accessed.
            If the cursor is not in the file, values may be negative.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.ILspCurrentFileContext.SelectionRange">
            <summary>
            The currently selected range when the file was accessed.
            If no selection is made, values may be negative.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.FilePosition">
            <summary>
            A 1-based file position.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.LspFilePosition">
            <summary>
            A 0-based file position.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.FileRange">
            <summary>
            A 1-based file range.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.LspFileRange">
            <summary>
            A 0-based file range.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.FileObjectExtensionMethods">
            <summary>
            Extension methods to conveniently convert between file position and range types.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileObjectExtensionMethods.ToLspPosition(Microsoft.PowerShell.EditorServices.Extensions.IFilePosition)">
            <summary>
            Convert a 1-based file position to a 0-based file position.
            </summary>
            <param name="position">The 1-based file position to convert.</param>
            <returns>An equivalent 0-based file position.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileObjectExtensionMethods.ToLspRange(Microsoft.PowerShell.EditorServices.Extensions.IFileRange)">
            <summary>
            Convert a 1-based file range to a 0-based file range.
            </summary>
            <param name="range">The 1-based file range to convert.</param>
            <returns>An equivalent 0-based file range.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileObjectExtensionMethods.ToFilePosition(Microsoft.PowerShell.EditorServices.Extensions.ILspFilePosition)">
            <summary>
            Convert a 0-based file position to a 1-based file position.
            </summary>
            <param name="position">The 0-based file position to convert.</param>
            <returns>An equivalent 1-based file position.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileObjectExtensionMethods.ToFileRange(Microsoft.PowerShell.EditorServices.Extensions.ILspFileRange)">
            <summary>
            Convert a 0-based file range to a 1-based file range.
            </summary>
            <param name="range">The 0-based file range to convert.</param>
            <returns>An equivalent 1-based file range.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.EditorObjectExtensions">
            <summary>
            Extension class to access the editor API with.
            This is done so that the async/ALC APIs aren't exposed to PowerShell, where they're likely only to cause problems.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorObjectExtensions.GetExtensionServiceProvider(Microsoft.PowerShell.EditorServices.Extensions.EditorObject)">
            <summary>
            Get the provider of extension services for .NET extension tooling.
            </summary>
            <param name="editorObject">The editor object ($psEditor).</param>
            <returns>The extension services provider.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.EditorObject">
            <summary>
            Provides the entry point of the extensibility API, inserted into
            the PowerShell session as the "$psEditor" variable.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.Instance">
            <summary>
            A reference to the editor object instance. Only valid when <see cref="P:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.EditorObjectReady"/> completes.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.EditorObjectReady">
            <summary>
            A task that completes when the editor object static instance has been set.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.EditorServicesVersion">
            <summary>
            Gets the version of PowerShell Editor Services.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.Workspace">
            <summary>
            Gets the workspace interface for the editor API.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.Window">
            <summary>
            Gets the window interface for the editor API.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.#ctor(System.IServiceProvider,Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService,Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations)">
            <summary>
            Creates a new instance of the EditorObject class.
            </summary>
            <param name="serviceProvider">The service provider?</param>
            <param name="extensionService">An ExtensionService which handles command registration.</param>
            <param name="editorOperations">An IEditorOperations implementation which handles operations in the host editor.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.RegisterCommand(Microsoft.PowerShell.EditorServices.Extensions.EditorCommand)">
            <summary>
            Registers a new command in the editor.
            </summary>
            <param name="editorCommand">The EditorCommand to be registered.</param>
            <returns>True if the command is newly registered, false if the command already exists.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.UnregisterCommand(System.String)">
            <summary>
            Unregisters an existing EditorCommand based on its registered name.
            </summary>
            <param name="commandName">The name of the command to be unregistered.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.GetCommands">
            <summary>
            Returns all registered EditorCommands.
            </summary>
            <returns>An Array of all registered EditorCommands.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorObject.GetEditorContext">
            <summary>
            Gets the EditorContext which contains the state of the editor
            at the time this method is invoked.
            </summary>
            <returns>A instance of the EditorContext class.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.EditorTerminal">
            <summary>
            Provides a PowerShell-facing API which allows scripts to
            interact with the editor's terminal.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorTerminal.#ctor(Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations)">
            <summary>
            Creates a new instance of the EditorTerminal class.
            </summary>
            <param name="editorOperations">An IEditorOperations implementation which handles operations in the host editor.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorTerminal.Clear">
            <summary>
            Triggers to the editor to clear the terminal.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.EditorWindow">
            <summary>
            Provides a PowerShell-facing API which allows scripts to
            interact with the editor's window.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorWindow.Terminal">
            <summary>
            Gets the terminal interface for the editor API.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWindow.#ctor(Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations)">
            <summary>
            Creates a new instance of the EditorWindow class.
            </summary>
            <param name="editorOperations">An IEditorOperations implementation which handles operations in the host editor.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWindow.ShowInformationMessage(System.String)">
            <summary>
            Shows an informational message to the user.
            </summary>
            <param name="message">The message to be shown.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWindow.ShowErrorMessage(System.String)">
            <summary>
            Shows an error message to the user.
            </summary>
            <param name="message">The message to be shown.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWindow.ShowWarningMessage(System.String)">
            <summary>
            Shows a warning message to the user.
            </summary>
            <param name="message">The message to be shown.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWindow.SetStatusBarMessage(System.String)">
            <summary>
            Sets the status bar message in the editor UI (if applicable).
            </summary>
            <param name="message">The message to be shown.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWindow.SetStatusBarMessage(System.String,System.Int32)">
            <summary>
            Sets the status bar message in the editor UI (if applicable).
            </summary>
            <param name="message">The message to be shown.</param>
            <param name="timeout">A timeout in milliseconds for how long the message should remain visible.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace">
            <summary>
            Provides a PowerShell-facing API which allows scripts to
            interact with the editor's workspace.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace.Path">
            <summary>
            Gets the server's initial working directory, since the extension API doesn't have a
            multi-root workspace concept.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace.Paths">
            <summary>
            Get all the workspace folders' paths.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace.NewFile">
            <summary>
            Creates a new file in the editor.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace.NewFile(System.String)">
            <summary>
            Creates a new file in the editor.
            </summary>
            <param name="content">The content to place in the new file.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace.OpenFile(System.String)">
            <summary>
            Opens a file in the workspace. If the file is already open
            its buffer will be made active.
            </summary>
            <param name="filePath">The path to the file to be opened.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace.OpenFile(System.String,System.Boolean)">
            <summary>
            Opens a file in the workspace. If the file is already open
            its buffer will be made active.
            You can specify whether the file opens as a preview or as a durable editor.
            </summary>
            <param name="filePath">The path to the file to be opened.</param>
            <param name="preview">Determines wether the file is opened as a preview or as a durable editor.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace.CloseFile(System.String)">
            <summary>
            Closes a file in the workspace.
            </summary>
            <param name="filePath">The path to the file to be closed.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace.SaveFile(System.String)">
            <summary>
            Saves an open file in the workspace.
            </summary>
            <param name="filePath">The path to the file to be saved.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.EditorWorkspace.SaveFile(System.String,System.String)">
            <summary>
            Saves a file with a new name AKA a copy.
            </summary>
            <param name="oldFilePath">The file to copy.</param>
            <param name="newFilePath">The file to create.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.FileContext">
            <summary>
            Provides context for a file that is open in the editor.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.FileContext.Ast">
            <summary>
            Gets the parsed abstract syntax tree for the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.FileContext.FileRange">
            <summary>
            Gets a BufferRange which represents the entire content
            range of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.FileContext.Language">
            <summary>
            Gets the language of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.FileContext.Path">
            <summary>
            Gets the filesystem path of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.FileContext.Uri">
            <summary>
            Gets the URI of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.FileContext.Tokens">
            <summary>
            Gets the parsed token list for the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Extensions.FileContext.WorkspacePath">
            <summary>
            Gets the workspace-relative path of the file.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.#ctor(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,Microsoft.PowerShell.EditorServices.Extensions.EditorContext,Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations,System.String)">
            <summary>
            Creates a new instance of the FileContext class.
            </summary>
            <param name="scriptFile">The ScriptFile to which this file refers.</param>
            <param name="editorContext">The EditorContext to which this file relates.</param>
            <param name="editorOperations">An IEditorOperations implementation which performs operations in the editor.</param>
            <param name="language">Determines the language of the file.false If it is not specified, then it defaults to "Unknown"</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.GetText">
            <summary>
            Gets the complete file content as a string.
            </summary>
            <returns>A string containing the complete file content.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.GetText(Microsoft.PowerShell.EditorServices.Extensions.FileRange)">
            <summary>
            Gets the file content in the specified range as a string.
            </summary>
            <param name="bufferRange">The buffer range for which content will be extracted.</param>
            <returns>A string with the specified range of content.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.GetTextLines">
            <summary>
            Gets the complete file content as an array of strings.
            </summary>
            <returns>An array of strings, each representing a line in the file.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.GetTextLines(Microsoft.PowerShell.EditorServices.Extensions.FileRange)">
            <summary>
            Gets the file content in the specified range as an array of strings.
            </summary>
            <param name="fileRange">The buffer range for which content will be extracted.</param>
            <returns>An array of strings, each representing a line in the file within the specified range.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.InsertText(System.String)">
            <summary>
            Inserts a text string at the current cursor position represented by
            the parent EditorContext's CursorPosition property.
            </summary>
            <param name="textToInsert">The text string to insert.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.InsertText(System.String,Microsoft.PowerShell.EditorServices.Extensions.IFilePosition)">
            <summary>
            Inserts a text string at the specified buffer position.
            </summary>
            <param name="textToInsert">The text string to insert.</param>
            <param name="insertPosition">The position at which the text will be inserted.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.InsertText(System.String,System.Int32,System.Int32)">
            <summary>
            Inserts a text string at the specified line and column numbers.
            </summary>
            <param name="textToInsert">The text string to insert.</param>
            <param name="insertLine">The 1-based line number at which the text will be inserted.</param>
            <param name="insertColumn">The 1-based column number at which the text will be inserted.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.InsertText(System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Inserts a text string to replace the specified range, represented
            by starting and ending line and column numbers.  Can be used to
            insert, replace, or delete text depending on the specified range
            and text to insert.
            </summary>
            <param name="textToInsert">The text string to insert.</param>
            <param name="startLine">The 1-based starting line number where text will be replaced.</param>
            <param name="startColumn">The 1-based starting column number where text will be replaced.</param>
            <param name="endLine">The 1-based ending line number where text will be replaced.</param>
            <param name="endColumn">The 1-based ending column number where text will be replaced.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.InsertText(System.String,Microsoft.PowerShell.EditorServices.Extensions.IFileRange)">
            <summary>
            Inserts a text string to replace the specified range. Can be
            used to insert, replace, or delete text depending on the specified
            range and text to insert.
            </summary>
            <param name="textToInsert">The text string to insert.</param>
            <param name="insertRange">The buffer range which will be replaced by the string.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.Save">
            <summary>
            Saves this file.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.FileContext.SaveAs(System.String)">
            <summary>
            Save this file under a new path and open a new editor window on that file.
            </summary>
            <param name="newFilePath">
            the path where the file should be saved,
            including the file name with extension as the leaf
            </param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations">
            <summary>
            Provides an interface that must be implemented by an editor
            host to perform operations invoked by extensions written in
            PowerShell.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.GetEditorContextAsync">
            <summary>
            Gets the EditorContext for the editor's current state.
            </summary>
            <returns>A new EditorContext object.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.GetWorkspacePath">
            <summary>
            Gets the server's initial working directory, since the extension API doesn't have a
            multi-root workspace concept.
            </summary>
            <returns>The server's initial working directory.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.GetWorkspacePaths">
            <summary>
            Get all the workspace folders' paths.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.GetWorkspaceRelativePath(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Resolves the given file path relative to the current workspace path.
            </summary>
            <returns>The resolved file path.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.NewFileAsync">
            <summary>
            Causes a new untitled file to be created in the editor.
            </summary>
            <returns>A task that can be awaited for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.NewFileAsync(System.String)">
            <summary>
            Causes a new untitled file to be created in the editor.
            </summary>
            <param name="content">The content to insert into the new file.</param>
            <returns>A task that can be awaited for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.OpenFileAsync(System.String)">
            <summary>
            Causes a file to be opened in the editor.  If the file is
            already open, the editor must switch to the file.
            </summary>
            <param name="filePath">The path of the file to be opened.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.OpenFileAsync(System.String,System.Boolean)">
            <summary>
            Causes a file to be opened in the editor.  If the file is
            already open, the editor must switch to the file.
            You can specify whether the file opens as a preview or as a durable editor.
            </summary>
            <param name="filePath">The path of the file to be opened.</param>
            <param name="preview">Determines wether the file is opened as a preview or as a durable editor.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.CloseFileAsync(System.String)">
            <summary>
            Causes a file to be closed in the editor.
            </summary>
            <param name="filePath">The path of the file to be closed.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.SaveFileAsync(System.String)">
            <summary>
            Causes a file to be saved in the editor.
            </summary>
            <param name="filePath">The path of the file to be saved.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.SaveFileAsync(System.String,System.String)">
            <summary>
            Causes a file to be saved as a new file in a new editor window.
            </summary>
            <param name="oldFilePath">the path of the current file being saved</param>
            <param name="newFilePath">the path of the new file where the current window content will be saved</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.InsertTextAsync(System.String,System.String,Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange)">
            <summary>
            Inserts text into the specified range for the file at the specified path.
            </summary>
            <param name="filePath">The path of the file which will have text inserted.</param>
            <param name="insertText">The text to insert into the file.</param>
            <param name="insertRange">The range in the file to be replaced.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.SetSelectionAsync(Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange)">
            <summary>
            Causes the selection to be changed in the editor's active file buffer.
            </summary>
            <param name="selectionRange">The range over which the selection will be made.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.ShowInformationMessageAsync(System.String)">
            <summary>
            Shows an informational message to the user.
            </summary>
            <param name="message">The message to be shown.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.ShowErrorMessageAsync(System.String)">
            <summary>
            Shows an error message to the user.
            </summary>
            <param name="message">The message to be shown.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.ShowWarningMessageAsync(System.String)">
            <summary>
            Shows a warning message to the user.
            </summary>
            <param name="message">The message to be shown.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.SetStatusBarMessageAsync(System.String,System.Nullable{System.Int32})">
            <summary>
            Sets the status bar message in the editor UI (if applicable).
            </summary>
            <param name="message">The message to be shown.</param>
            <param name="timeout">If non-null, a timeout in milliseconds for how long the message should remain visible.</param>
            <returns>A Task that can be tracked for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations.ClearTerminal">
            <summary>
            Triggers to the editor to clear the terminal.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Hosting.EditorServicesLoading">
            <summary>
            Implementation-free class designed to safely allow PowerShell Editor Services to be loaded in an obvious way.
            Referencing this class will force looking for and loading the PSES assembly if it's not already loaded.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Hosting.EditorServicesServerFactory">
            <summary>
            Factory for creating the LSP server and debug server instances.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Hosting.EditorServicesServerFactory.#ctor(System.IObservable{System.ValueTuple{System.Int32,System.String}})">
            <summary>
            Creates a loggerfactory for this instance
            </summary>
            <param name="hostLogger">The hostLogger that will be provided to the language services for logging handoff</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Hosting.EditorServicesServerFactory.CreateLanguageServer(System.IO.Stream,System.IO.Stream,Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo)">
            <summary>
            Create the LSP server.
            </summary>
            <remarks>
            This is only called once and that's in <see cref="!:Hosting.EditorServicesRunner"/>.
            </remarks>
            <param name="inputStream">The protocol transport input stream.</param>
            <param name="outputStream">The protocol transport output stream.</param>
            <param name="hostStartupInfo">The host details configuration for Editor Services
            instantiation.</param>
            <returns>A new, unstarted language server instance.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Hosting.EditorServicesServerFactory.CreateDebugServerWithLanguageServer(System.IO.Stream,System.IO.Stream,Microsoft.PowerShell.EditorServices.Server.PsesLanguageServer)">
            <summary>
            Create the debug server given a language server instance.
            </summary>
            <remarks>
            This is only called once and that's in <see cref="!:Hosting.EditorServicesRunner"/>.
            </remarks>
            <param name="inputStream">The protocol transport input stream.</param>
            <param name="outputStream">The protocol transport output stream.</param>
            <param name="languageServer"></param>
            <returns>A new, unstarted debug server instance.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Hosting.EditorServicesServerFactory.RecreateDebugServer(System.IO.Stream,System.IO.Stream,Microsoft.PowerShell.EditorServices.Server.PsesDebugServer)">
            <summary>
            Create a new debug server based on an old one in an ended session.
            </summary>
            <remarks>
            This is only called once and that's in <see cref="!:Hosting.EditorServicesRunner"/>.
            </remarks>
            <param name="inputStream">The protocol transport input stream.</param>
            <param name="outputStream">The protocol transport output stream.</param>
            <param name="debugServer">The old debug server to recreate.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Hosting.EditorServicesServerFactory.CreateDebugServerForTempSession(System.IO.Stream,System.IO.Stream,Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo)">
            <summary>
            Create a standalone debug server for temp sessions.
            </summary>
            <param name="inputStream">The protocol transport input stream.</param>
            <param name="outputStream">The protocol transport output stream.</param>
            <param name="hostStartupInfo">The host startup configuration to create the server session with.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo">
            <summary>
            Contains details about the host as well as any other information needed by Editor Services
            at startup time.
            </summary>
            <remarks>
            TODO: Simplify this as a <see langword="record"/>.
            </remarks>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.DefaultHostName">
            <summary>
            The default host name for PowerShell Editor Services.  Used
            if no host name is specified by the host application.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.DefaultHostProfileId">
            <summary>
            The default host ID for PowerShell Editor Services.  Used
            for the host-specific profile path if no host ID is specified.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.s_defaultHostVersion">
            <summary>
            The default host version for PowerShell Editor Services.  If
            no version is specified by the host application, we use 0.0.0
            to indicate a lack of version.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.Name">
            <summary>
            Gets the name of the host.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.ProfileId">
            <summary>
            Gets the profile ID of the host, used to determine the
            host-specific profile path.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.Version">
            <summary>
            Gets the version of the host.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.FeatureFlags">
            <summary>
            Any feature flags enabled at startup.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.AdditionalModules">
            <summary>
            Names or paths of any additional modules to import on startup.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.ConsoleReplEnabled">
            <summary>
            True if the Extension Terminal is to be enabled.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.UseNullPSHostUI">
            <summary>
            True if we want to suppress messages to PSHost (to prevent Stdio clobbering)
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.UsesLegacyReadLine">
            <summary>
            If true, the legacy PSES readline implementation will be used. Otherwise PSReadLine will be used.
            If the console REPL is not enabled, this setting will be ignored.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.PSHost">
            <summary>
            The PowerShell host to use with Editor Services.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.LogPath">
            <summary>
            The path of the log file Editor Services should log to.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.InitialSessionState">
            <summary>
            The InitialSessionState will be inherited from the original PowerShell process. This will
            be used when creating runspaces so that we honor the same InitialSessionState.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.LogLevel">
            <summary>
            The minimum log level of log events to be logged.
            </summary>
            <remarks>
            This primitive maps to <see cref="!:Hosting.PsesLogLevel"/> and <see
            cref="T:Microsoft.Extensions.Logging.LogLevel"/>
            </remarks>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.BundledModulePath">
            <summary>
            The path to find the bundled modules. User configurable for advanced usage.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo.#ctor(System.String,System.String,System.Version,System.Management.Automation.Host.PSHost,Microsoft.PowerShell.EditorServices.Hosting.ProfilePathInfo,System.Collections.Generic.IReadOnlyList{System.String},System.Collections.Generic.IReadOnlyList{System.String},System.Management.Automation.Runspaces.InitialSessionState,System.String,System.Int32,System.Boolean,System.Boolean,System.Boolean,System.String)">
            <summary>
            Creates an instance of the HostDetails class.
            </summary>
            <param name="name">
            The display name for the host, typically in the form of
            "[Application Name] Host".
            </param>
            <param name="profileId">
            The identifier of the PowerShell host to use for its profile path.
            loaded. Used to resolve a profile path of the form 'X_profile.ps1'
            where 'X' represents the value of hostProfileId.  If null, a default
            will be used.
            </param>
            <param name="version">The host application's version.</param>
            <param name="psHost">The PowerShell host to use.</param>
            <param name="profilePaths">The set of profile paths.</param>
            <param name="featureFlags">Flags of features to enable.</param>
            <param name="additionalModules">Names or paths of additional modules to import.</param>
            <param name="initialSessionState">The language mode inherited from the orginal PowerShell process. This will be used when creating runspaces so that we honor the same initialSessionState including allowed modules, cmdlets and language mode.</param>
            <param name="logPath">The path to log to.</param>
            <param name="logLevel">The minimum log event level.</param>
            <param name="consoleReplEnabled">Enable console if true.</param>
            <param name="useNullPSHostUI">Whether or not to use the Null UI.</param>
            <param name="usesLegacyReadLine">Use PSReadLine if false, otherwise use the legacy readline implementation.</param>
            <param name="bundledModulePath">A custom path to the expected bundled modules.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Hosting.ProfilePathInfo">
            <summary>
            This is a strange class that is generally <c>null</c> or otherwise just has a single path
            set. It is eventually parsed one-by-one when setting up the PowerShell runspace.
            </summary>
            <remarks>
            TODO: Simplify this as a <see langword="record"/>.
            </remarks>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Logging.HostLoggerAdapter">
            <summary>
            Adapter class to allow logging events sent by the host to be recorded by PSES' logging infrastructure.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Logging.HostLoggerAdapter.#ctor(Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Adapter class to allow logging events sent by the host to be recorded by PSES' logging infrastructure.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Logging.HostLoggerAdapter.OnNext(System.ValueTuple{System.Int32,System.String})">
            <summary>
            Log the message received from the host into MEL.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Logging.LanguageServerLogger.FormatState``1(``0,System.Exception)">
            <summary>
            Formats the state object into a string for logging.
            </summary>
            <remarks>
            This is copied from Omnisharp, we can probably do better.
            </remarks>
            <typeparam name="TState"></typeparam>
            <param name="state"></param>
            <param name="exception"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Logging.LanguageServerLogger.GetMessageInfo(Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Maps MEL log levels to LSP message types
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Logging.LanguageServerLoggerExtensions.AddPsesLanguageServerLogging(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Adds a custom logger provider for PSES LSP, that provides more granular categorization than the default Omnisharp logger, such as separating Omnisharp and PSES messages to different channels.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Server.PsesDebugServer">
            <summary>
            Server for hosting debug sessions.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Server.PsesDebugServer.StartAsync">
            <summary>
            Start the debug server listening.
            </summary>
            <returns>A task that completes when the server is ready.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Server.PsesLanguageServer">
            <summary>
            Server runner class for handling LSP messages for Editor Services.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Server.PsesLanguageServer.#ctor(System.IObservable{System.ValueTuple{System.Int32,System.String}},System.IO.Stream,System.IO.Stream,Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo)">
            <summary>
            Create a new language server instance.
            </summary>
            <remarks>
            This class is only ever instantiated via <see
            cref="M:Microsoft.PowerShell.EditorServices.Hosting.EditorServicesServerFactory.CreateLanguageServer(System.IO.Stream,System.IO.Stream,Microsoft.PowerShell.EditorServices.Hosting.HostStartupInfo)"/>. It is essentially a
            singleton. The factory hides the logger.
            </remarks>
            <param name="hostLogger">The host logger to hand off for monitoring.</param>
            <param name="inputStream">Protocol transport input stream.</param>
            <param name="outputStream">Protocol transport output stream.</param>
            <param name="hostStartupInfo">Host configuration to instantiate the server and services
            with.</param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Microsoft.PowerShell.EditorServices.Server.PsesLanguageServer.StartAsync" -->
        <member name="M:Microsoft.PowerShell.EditorServices.Server.PsesLanguageServer.WaitForShutdown">
            <summary>
            Get a task that completes when the server is shut down.
            </summary>
            <returns>A task that completes when the server is shut down.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.AnalysisService">
            <summary>
            Provides a high-level service for performing semantic analysis
            of PowerShell scripts.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.AnalysisService.GetUniqueIdFromDiagnostic(OmniSharp.Extensions.LanguageServer.Protocol.Models.Diagnostic)">
            <summary>
            Reliably generate an ID for a diagnostic record to track it.
            </summary>
            <param name="diagnostic">The diagnostic to generate an ID for.</param>
            <returns>A string unique to this diagnostic given where and what kind it is.</returns>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.AnalysisService.s_defaultRules">
            <summary>
            Defines the list of Script Analyzer rules to include by default if
            no settings file is specified.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.AnalysisService.AnalysisEngine">
            <summary>
            The analysis engine to use for running script analysis.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.AnalysisService.StartScriptDiagnostics(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile[])">
            <summary>
            Sets up a script analysis run, eventually returning the result.
            </summary>
            <param name="filesToAnalyze">The files to run script analysis on.</param>
            <returns>A task that finishes when script diagnostics have been published.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.AnalysisService.FormatAsync(System.String,System.Collections.Hashtable,System.Int32[])">
            <summary>
            Formats a PowerShell script with the given settings.
            </summary>
            <param name="scriptFileContents">The script to format.</param>
            <param name="formatSettings">The settings to use with the formatter.</param>
            <param name="formatRange">Optionally, the range that should be formatted.</param>
            <returns>The text of the formatted PowerShell script.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.AnalysisService.GetCommentHelpText(System.String,System.String,System.Boolean)">
            <summary>
            Get comment help text for a PowerShell function definition.
            </summary>
            <param name="functionText">The text of the function to get comment help for.</param>
            <param name="helpLocation">A string referring to which location comment help should be placed around the function.</param>
            <param name="forBlockComment">If true, block comment help will be supplied.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.AnalysisService.GetMostRecentCodeActionsForFileAsync(OmniSharp.Extensions.LanguageServer.Protocol.DocumentUri)">
            <summary>
            Get the most recent corrections computed for a given script file.
            </summary>
            <param name="uri">The URI string of the file to get code actions for.</param>
            <returns>A thread-safe readonly dictionary of the code actions of the particular file.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.AnalysisService.ClearMarkers(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Clear all diagnostic markers for a given file.
            </summary>
            <param name="file">The file to clear markers in.</param>
            <returns>A task that ends when all markers in the file have been cleared.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.AnalysisService.OnConfigurationUpdated(System.Object,Microsoft.PowerShell.EditorServices.Services.Configuration.LanguageServerSettings)">
            <summary>
            Event subscription method to be run when PSES configuration has been updated.
            </summary>
            <param name="_">The sender of the configuration update event.</param>
            <param name="settings">The new language server settings.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.AnalysisService.CorrectionTableEntry">
            <summary>
            Tracks corrections suggested by PSSA for a given file,
            so that after a diagnostics request has fired,
            a code action request can look up that file,
            wait for analysis to finish if needed,
            and then fetch the corrections set in the table entry by PSSA.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine">
            <summary>
            PowerShell script analysis engine that uses PSScriptAnalyzer
            cmdlets run through a PowerShell API to drive analysis.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.Builder">
            <summary>
            Builder for the PssaCmdletAnalysisEngine allowing settings configuration.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.Builder.#ctor(Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Create a builder for PssaCmdletAnalysisEngine construction.
            </summary>
            <param name="loggerFactory">The logger to use.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.Builder.WithSettingsFile(System.String)">
            <summary>
            Uses a settings file for PSSA rule configuration.
            </summary>
            <param name="settingsPath">The absolute path to the settings file.</param>
            <returns>The builder for chaining.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.Builder.WithIncludedRules(System.String[])">
            <summary>
            Uses a set of unconfigured rules for PSSA configuration.
            </summary>
            <param name="rules">The rules for PSSA to run.</param>
            <returns>The builder for chaining.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.Builder.Build(System.String)">
            <summary>
            Attempts to build a PssaCmdletAnalysisEngine with the given configuration.
            If PSScriptAnalyzer cannot be found, this will return null.
            </summary>
            <returns>A newly configured PssaCmdletAnalysisEngine, or null if PSScriptAnalyzer cannot be found.</returns>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.s_indentJoin">
            <summary>
            The indentation to add when the logger lists errors.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.FormatAsync(System.String,System.Collections.Hashtable,System.Int32[])">
            <summary>
            Format a script given its contents.
            TODO: This needs to be cancellable.
            </summary>
            <param name="scriptDefinition">The full text of a script.</param>
            <param name="formatSettings">The formatter settings to use.</param>
            <param name="rangeList">A possible range over which to run the formatter.</param>
            <returns>Formatted script as string</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.AnalyzeScriptAsync(System.String)">
            <summary>
            Analyze a given script using PSScriptAnalyzer.
            </summary>
            <param name="scriptContent">The contents of the script to analyze.</param>
            <returns>An array of markers indicating script analysis diagnostics.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.AnalyzeScriptAsync(System.String,System.Collections.Hashtable)">
            <summary>
            Analyze a given script using PSScriptAnalyzer.
            </summary>
            <param name="scriptContent">The contents of the script to analyze.</param>
            <param name="settings">The settings file to use in this instance of analysis.</param>
            <returns>An array of markers indicating script analysis diagnostics.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.LogAvailablePssaFeatures">
            <summary>
            Log the features available from the PSScriptAnalyzer module that has been imported
            for use with the AnalysisService.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.GetPSScriptAnalyzerRules">
            <summary>
            Returns a list of builtin-in PSScriptAnalyzer rules
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.CreatePssaRunspacePool(System.String)">
            <summary>
            Create a new runspace pool around a PSScriptAnalyzer module for asynchronous script analysis tasks.
            This looks for the latest version of PSScriptAnalyzer on the path and loads that.
            </summary>
            <returns>A runspace pool with PSScriptAnalyzer loaded for running script analysis tasks.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Analysis.PssaCmdletAnalysisEngine.PowerShellResult">
            <summary>
            Wraps the result of an execution of PowerShell to send back through
            asynchronous calls.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.BreakpointService.RemoveAllBreakpointsAsync(System.String)">
            <summary>
            Clears all breakpoints in the current session.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointApiUtils.GetBreakpointActionScriptBlock(System.String,System.String,System.String,System.String@)">
            <summary>
            Inspects the condition, putting in the appropriate scriptblock template
            "if (expression) { break }".  If errors are found in the condition, the
            breakpoint passed in is updated to set Verified to false and an error
            message is put into the breakpoint.Message property.
            </summary>
            <param name="condition">The expression that needs to be true for the breakpoint to be triggered.</param>
            <param name="hitCondition">The amount of times this line should be hit til the breakpoint is triggered.</param>
            <param name="logMessage">The log message to write instead of calling 'break'. In VS Code, this is called a 'logPoint'.</param>
            <param name="errorMessage">The error message we might return.</param>
            <returns>ScriptBlock</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetails">
            <summary>
            Provides details about a breakpoint that is set in the
            PowerShell debugger.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetails.Id">
            <summary>
            Gets the unique ID of the breakpoint.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetails.Source">
            <summary>
            Gets the source where the breakpoint is located.  Used only for debug purposes.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetails.LineNumber">
            <summary>
            Gets the line number at which the breakpoint is set.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetails.ColumnNumber">
            <summary>
            Gets the column number at which the breakpoint is set.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetails.Create(System.String,System.Int32,System.Nullable{System.Int32},System.String,System.String,System.String)">
            <summary>
            Creates an instance of the BreakpointDetails class from the individual
            pieces of breakpoint information provided by the client.
            </summary>
            <param name="source"></param>
            <param name="line"></param>
            <param name="column"></param>
            <param name="condition"></param>
            <param name="hitCondition"></param>
            <param name="logMessage"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetails.Create(System.Management.Automation.Breakpoint,System.Management.Automation.BreakpointUpdateType)">
            <summary>
            Creates an instance of the BreakpointDetails class from a
            PowerShell Breakpoint object.
            </summary>
            <param name="breakpoint">The Breakpoint instance from which details will be taken.</param>
            <param name="updateType">The BreakpointUpdateType to determine if the breakpoint is verified.</param>
            <returns>A new instance of the BreakpointDetails class.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetailsBase">
            <summary>
            Provides details about a breakpoint that is set in the
            PowerShell debugger.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetailsBase.Verified">
            <summary>
            Gets or sets a boolean indicator that if true, breakpoint could be set
            (but not necessarily at the desired location).
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetailsBase.Message">
            <summary>
            Gets or set an optional message about the state of the breakpoint. This is shown to the user
            and can be used to explain why a breakpoint could not be verified.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetailsBase.Condition">
            <summary>
            Gets the breakpoint condition string.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetailsBase.HitCondition">
            <summary>
            Gets the breakpoint hit condition string.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.CommandBreakpointDetails">
            <summary>
            Provides details about a command breakpoint that is set in the PowerShell debugger.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.CommandBreakpointDetails.Name">
            <summary>
            Gets the name of the command on which the command breakpoint has been set.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.CommandBreakpointDetails.Create(System.String,System.String)">
            <summary>
            Creates an instance of the <see cref="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.CommandBreakpointDetails"/> class from the individual
            pieces of breakpoint information provided by the client.
            </summary>
            <param name="name">The name of the command to break on.</param>
            <param name="condition">Condition string that would be applied to the breakpoint Action parameter.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.CommandBreakpointDetails.Create(System.Management.Automation.Breakpoint)">
            <summary>
            Creates an instance of the <see cref="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.CommandBreakpointDetails"/> class from a
            PowerShell CommandBreakpoint object.
            </summary>
            <param name="breakpoint">The Breakpoint instance from which details will be taken.</param>
            <returns>A new instance of the BreakpointDetails class.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs">
            <summary>
            Provides event arguments for the DebugService.DebuggerStopped event.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs.ScriptPath">
            <summary>
            Gets the path of the script where the debugger has stopped execution.
            If 'IsRemoteSession' returns true, this path will be a local filesystem
            path containing the contents of the script that is executing remotely.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs.IsRemoteSession">
            <summary>
            Returns true if the breakpoint was raised from a remote debugging session.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs.RemoteScriptPath">
            <summary>
            Gets the original script path if 'IsRemoteSession' returns true.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs.RunspaceInfo">
            <summary>
            Gets the RunspaceDetails for the current runspace.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs.LineNumber">
            <summary>
            Gets the line number at which the debugger stopped execution.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs.ColumnNumber">
            <summary>
            Gets the column number at which the debugger stopped execution.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs.OriginalEvent">
            <summary>
            Gets the original DebuggerStopEventArgs from the PowerShell engine.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs.#ctor(System.Management.Automation.DebuggerStopEventArgs,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceInfo)">
            <summary>
            Creates a new instance of the DebuggerStoppedEventArgs class.
            </summary>
            <param name="originalEvent">The original DebuggerStopEventArgs instance from which this instance is based.</param>
            <param name="runspaceInfo">The RunspaceDetails of the runspace which raised this event.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.DebuggerStoppedEventArgs.#ctor(System.Management.Automation.DebuggerStopEventArgs,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceInfo,System.String)">
            <summary>
            Creates a new instance of the DebuggerStoppedEventArgs class.
            </summary>
            <param name="originalEvent">The original DebuggerStopEventArgs instance from which this instance is based.</param>
            <param name="runspaceInfo">The RunspaceDetails of the runspace which raised this event.</param>
            <param name="localScriptPath">The local path of the remote script being debugged.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.InvalidPowerShellExpressionException">
            <summary>
            Represents the exception that is thrown when an invalid expression is provided to the DebugService's SetVariable method.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.InvalidPowerShellExpressionException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SetVariableExpressionException class.
            </summary>
            <param name="message">Message indicating why the expression is invalid.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails">
            <summary>
            Contains details pertaining to a single stack frame in
            the current debugging session.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.NoFileScriptPath">
            <summary>
            A constant string used in the ScriptPath field to represent a
            stack frame with no associated script file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.ScriptPath">
            <summary>
            Gets the path to the script where the stack frame occurred.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.FunctionName">
            <summary>
            Gets the name of the function where the stack frame occurred.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.StartLineNumber">
            <summary>
            Gets the start line number of the script where the stack frame occurred.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.EndLineNumber">
            <summary>
            Gets the line number of the script where the stack frame occurred.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.StartColumnNumber">
            <summary>
            Gets the start column number of the line where the stack frame occurred.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.EndColumnNumber">
            <summary>
            Gets the end column number of the line where the stack frame occurred.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.IsExternalCode">
            <summary>
            Gets a boolean value indicating whether or not the stack frame is executing
            in script external to the current workspace root.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.AutoVariables">
            <summary>
            Gets or sets the VariableContainerDetails that contains the auto variables.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.CommandVariables">
            <summary>
            Gets or sets the VariableContainerDetails that contains the call stack frame variables.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.StackFrameDetails.Create(System.Management.Automation.PSObject,Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails,Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails)">
            <summary>
            Creates an instance of the StackFrameDetails class from a
            CallStackFrame instance provided by the PowerShell engine.
            </summary>
            <param name="callStackFrameObject">
            A PSObject representing the CallStackFrame instance from which details will be obtained.
            </param>
            <param name="autoVariables">
            A variable container with all the filtered, auto variables for this stack frame.
            </param>
            <param name="commandVariables">
            A variable container with all the command variables for this stack frame.
            </param>
            <returns>A new instance of the StackFrameDetails class.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails">
            <summary>
            Container for variables that is not itself a variable per se.  However given how
            VSCode uses an integer variable reference id for every node under the "Variables" tool
            window, it is useful to treat containers, typically scope containers, as a variable.
            Note that these containers are not necessarily always a scope container. Consider a
            container such as "Auto" or "My".  These aren't scope related but serve as just another
            way to organize variables into a useful UI structure.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails.AutoVariablesName">
            <summary>
            Provides a constant for the name of the filtered auto variables.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails.CommandVariablesName">
            <summary>
            Provides a constant for the name of the current stack frame variables.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails.GlobalScopeName">
            <summary>
            Provides a constant for the name of the global scope variables.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails.LocalScopeName">
            <summary>
            Provides a constant for the name of the local scope variables.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails.ScriptScopeName">
            <summary>
            Provides a constant for the name of the Script scope.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails.#ctor(System.Int32,System.String)">
            <summary>
            Instantiates an instance of VariableScopeDetails.
            </summary>
            <param name="id">The variable reference id for this scope.</param>
            <param name="name">The name of the variable scope.</param>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails.Children">
            <summary>
            Gets the collection of child variables.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails.GetChildren(Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Returns the details of the variable container's children.  If empty, returns an empty array.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableContainerDetails.ContainsVariable(System.Int32)">
            <summary>
            Determines whether this variable container contains the specified variable by its referenceId.
            </summary>
            <param name="variableReferenceId">The variableReferenceId to search for.</param>
            <returns>Returns true if this variable container directly contains the specified variableReferenceId, false otherwise.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetails">
            <summary>
            Contains details pertaining to a variable in the current
            debugging session.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetails.DollarPrefix">
            <summary>
            Provides a constant for the dollar sign variable prefix string.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetails.#ctor(System.Management.Automation.PSVariable)">
            <summary>
            Initializes an instance of the VariableDetails class from
            the details contained in a PSVariable instance.
            </summary>
            <param name="psVariable">
            The PSVariable instance from which variable details will be obtained.
            </param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetails.#ctor(System.Management.Automation.PSObject)">
            <summary>
            Initializes an instance of the VariableDetails class from
            the name and value pair stored inside of a PSObject which
            represents a PSVariable.
            </summary>
            <param name="psVariableObject">
            The PSObject which represents a PSVariable.
            </param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetails.#ctor(System.Management.Automation.PSPropertyInfo)">
            <summary>
            Initializes an instance of the VariableDetails class from
            the details contained in a PSPropertyInfo instance.
            </summary>
            <param name="psProperty">
            The PSPropertyInfo instance from which variable details will be obtained.
            </param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetails.#ctor(System.String,System.Object)">
            <summary>
            Initializes an instance of the VariableDetails class from
            a given name/value pair.
            </summary>
            <param name="name">The variable's name.</param>
            <param name="value">The variable's value.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetails.GetChildren(Microsoft.Extensions.Logging.ILogger)">
            <summary>
            If this variable instance is expandable, this method returns the
            details of its children.  Otherwise it returns an empty array.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetailsRawView">
            <summary>
            A VariableDetails that only returns the raw view properties of the object, rather than its values.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetailsBase">
            <summary>
            Defines the common details between a variable and a variable container such as a scope
            in the current debugging session.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetailsBase.FirstVariableId">
            <summary>
            Provides a constant that is used as the starting variable ID for all.
            Avoid 0 as it indicates a variable node with no children.
            variables.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetailsBase.Id">
            <summary>
            Gets the numeric ID of the variable which can be used to refer
            to it in future requests.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetailsBase.Name">
            <summary>
            Gets the variable's name.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetailsBase.ValueString">
            <summary>
            Gets the string representation of the variable's value.
            If the variable is an expandable object, this string
            will be empty.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetailsBase.Type">
            <summary>
            Gets the type of the variable's value.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetailsBase.IsExpandable">
            <summary>
            Returns true if the variable's value is expandable, meaning
            that it has child properties or its contents can be enumerated.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableDetailsBase.GetChildren(Microsoft.Extensions.Logging.ILogger)">
            <summary>
            If this variable instance is expandable, this method returns the
            details of its children.  Otherwise it returns an empty array.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableScope">
            <summary>
            Contains details pertaining to a variable scope in the current
            debugging session.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableScope.Id">
            <summary>
            Gets a numeric ID that can be used in future operations
            relating to this scope.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableScope.Name">
            <summary>
            Gets a name that describes the variable scope.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.VariableScope.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the VariableScope class with
            the given ID and name.
            </summary>
            <param name="id">The variable scope's ID.</param>
            <param name="name">The variable scope's name.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.DebugService">
            <summary>
            Provides a high-level service for interacting with the
            PowerShell debugger in the runspace managed by a PowerShellContext.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugService.IsClientAttached">
            <summary>
            Gets or sets a boolean that indicates whether a debugger client is
            currently attached to the debugger.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugService.IsDebuggerStopped">
            <summary>
            Gets a boolean that indicates whether the debugger is currently
            stopped at a breakpoint.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugService.CurrentDebuggerStoppedEventArgs">
            <summary>
            Gets the current DebuggerStoppedEventArgs when the debugger
            is stopped.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugService.StackFramesAndVariablesFetched">
            <summary>
            Returns a task that completes when script frames and variables have completed population
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.DebugService.IsDebuggingRemoteRunspace">
            <summary>
            Tracks whether we are running <c>Debug-Runspace</c> in an out-of-process runspace.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.#ctor(Microsoft.PowerShell.EditorServices.Services.PowerShell.IInternalPowerShellExecutionService,Microsoft.PowerShell.EditorServices.Services.PowerShell.Debugging.IPowerShellDebugContext,Microsoft.PowerShell.EditorServices.Services.RemoteFileManagerService,Microsoft.PowerShell.EditorServices.Services.BreakpointService,Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.PsesInternalHost,Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initializes a new instance of the DebugService class and uses
            the given execution service for all future operations.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.SetLineBreakpointsAsync(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Collections.Generic.IReadOnlyList{Microsoft.PowerShell.EditorServices.Services.DebugAdapter.BreakpointDetails},System.Boolean)">
            <summary>
            Sets the list of line breakpoints for the current debugging session.
            </summary>
            <param name="scriptFile">The ScriptFile in which breakpoints will be set.</param>
            <param name="breakpoints">BreakpointDetails for each breakpoint that will be set.</param>
            <param name="clearExisting">If true, causes all existing breakpoints to be cleared before setting new ones.</param>
            <returns>An awaitable Task that will provide details about the breakpoints that were set.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.SetCommandBreakpointsAsync(System.Collections.Generic.IReadOnlyList{Microsoft.PowerShell.EditorServices.Services.DebugAdapter.CommandBreakpointDetails},System.Boolean)">
            <summary>
            Sets the list of command breakpoints for the current debugging session.
            </summary>
            <param name="breakpoints">CommandBreakpointDetails for each command breakpoint that will be set.</param>
            <param name="clearExisting">If true, causes all existing function breakpoints to be cleared before setting new ones.</param>
            <returns>An awaitable Task that will provide details about the breakpoints that were set.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.Continue">
            <summary>
            Sends a "continue" action to the debugger when stopped.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.StepOver">
            <summary>
            Sends a "step over" action to the debugger when stopped.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.StepIn">
            <summary>
            Sends a "step in" action to the debugger when stopped.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.StepOut">
            <summary>
            Sends a "step out" action to the debugger when stopped.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.Break">
            <summary>
            Causes the debugger to break execution wherever it currently
            is at the time. This is equivalent to clicking "Pause" in a
            debugger UI.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.Abort">
            <summary>
            Aborts execution of the debugger while it is running, even while
            it is stopped.  Equivalent to calling PowerShellContext.AbortExecution.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.GetVariables(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets the list of variables that are children of the scope or variable
            that is identified by the given referenced ID.
            </summary>
            <param name="variableReferenceId"></param>
            <param name="cancellationToken"></param>
            <returns>An array of VariableDetails instances which describe the requested variables.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.GetVariableFromExpression(System.String,System.Threading.CancellationToken)">
            <summary>
            Evaluates a variable expression in the context of the stopped
            debugger. This method decomposes the variable expression to
            walk the cached variable data for the specified stack frame.
            </summary>
            <param name="variableExpression">The variable expression string to evaluate.</param>
            <param name="cancellationToken"></param>
            <returns>A VariableDetailsBase object containing the result.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.SetVariableAsync(System.Int32,System.String,System.String)">
            <summary>
            Sets the specified variable by container variableReferenceId and variable name to the
            specified new value. If the variable cannot be set or converted to that value this
            method will throw InvalidPowerShellExpressionException, ArgumentTransformationMetadataException, or
            SessionStateUnauthorizedAccessException.
            </summary>
            <param name="variableContainerReferenceId">The container (Autos, Local, Script, Global) that holds the variable.</param>
            <param name="name">The name of the variable prefixed with $.</param>
            <param name="value">The new string value.  This value must not be null.  If you want to set the variable to $null
            pass in the string "$null".</param>
            <returns>The string representation of the value the variable was set to.</returns>
            <exception cref="T:Microsoft.PowerShell.EditorServices.Services.DebugAdapter.InvalidPowerShellExpressionException"></exception>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.EvaluateExpressionAsync(System.String,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Evaluates an expression in the context of the stopped
            debugger.  This method will execute the specified expression
            PowerShellContext.
            </summary>
            <param name="expressionString">The expression string to execute.</param>
            <param name="writeResultAsOutput">
            If true, writes the expression result as host output rather than returning the results.
            In this case, the return value of this function will be null.</param>
            <param name="cancellationToken"></param>
            <returns>A VariableDetails object containing the result.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.GetStackFrames">
            <summary>
            Gets the list of stack frames at the point where the
            debugger sf stopped.
            </summary>
            <returns>
            An array of StackFrameDetails instances that contain the stack trace.
            </returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.GetVariableScopes(System.Int32)">
            <summary>
            Gets the list of variable scopes for the stack frame that
            is identified by the given ID.
            </summary>
            <param name="stackFrameId">The ID of the stack frame at which variable scopes should be retrieved.</param>
            <returns>The list of VariableScope instances which describe the available variable scopes.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.DebugService.ShouldAddAsVariable(Microsoft.PowerShell.EditorServices.Services.DebugService.VariableInfo)">
            <summary>
            Filters out variables we don't care about such as built-ins
            </summary>
        </member>
        <member name="E:Microsoft.PowerShell.EditorServices.Services.DebugService.DebuggerStopped">
            <summary>
            Raised when the debugger stops execution at a breakpoint or when paused.
            </summary>
        </member>
        <member name="E:Microsoft.PowerShell.EditorServices.Services.DebugService.BreakpointUpdated">
            <summary>
            Raised when a breakpoint is added/removed/updated in the debugger.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Extension.ChoiceDetails">
            <summary>
            Contains the details about a choice that should be displayed
            to the user.  This class is meant to be serializable to the
            user's UI.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Extension.ChoiceDetails.Label">
            <summary>
            Gets the label for the choice.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Extension.ChoiceDetails.HotKeyIndex">
            <summary>
            Gets the index of the hot key character for the choice.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Extension.ChoiceDetails.HotKeyCharacter">
            <summary>
            Gets the hot key character.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Extension.ChoiceDetails.HelpMessage">
            <summary>
            Gets the help string that describes the choice.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ChoiceDetails.#ctor">
            <summary>
            Creates an instance of the ChoiceDetails class with
            the provided details.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ChoiceDetails.#ctor(System.String,System.String)">
            <summary>
            Creates an instance of the ChoiceDetails class with
            the provided details.
            </summary>
            <param name="label">
            The label of the choice.  An ampersand '&amp;' may be inserted
            before the character that will used as a hot key for the
            choice.
            </param>
            <param name="helpMessage">
            A help message that describes the purpose of the choice.
            </param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ChoiceDetails.Create(System.Management.Automation.Host.ChoiceDescription)">
            <summary>
            Creates a new instance of the ChoicePromptDetails class
            based on a ChoiceDescription from the PowerShell layer.
            </summary>
            <param name="choiceDescription">
            A ChoiceDescription on which this instance will be based.
            </param>
            <returns>A new ChoicePromptDetails instance.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ChoiceDetails.MatchesInput(System.String)">
            <summary>
            Compares an input string to this choice to determine
            whether the input string is a match.
            </summary>
            <param name="inputString">
            The input string to compare to the choice.
            </param>
            <returns>True if the input string is a match for the choice.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService">
            <summary>
            Provides a high-level service which enables PowerShell scripts
            and modules to extend the behavior of the host editor.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.EditorOperations">
            <summary>
            Gets the IEditorOperations implementation used to invoke operations
            in the host editor.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.EditorObject">
            <summary>
            Gets the EditorObject which exists in the PowerShell session as the
            '$psEditor' variable.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.ExecutionService">
            <summary>
            Gets the PowerShellContext in which extension code will be executed.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.#ctor(OmniSharp.Extensions.LanguageServer.Protocol.Server.ILanguageServerFacade,System.IServiceProvider,Microsoft.PowerShell.EditorServices.Extensions.IEditorOperations,Microsoft.PowerShell.EditorServices.Services.PowerShell.IInternalPowerShellExecutionService)">
            <summary>
            Creates a new instance of the ExtensionService which uses the provided
            PowerShellContext for loading and executing extension code.
            </summary>
            <param name="languageServer">The PSES language server instance.</param>
            <param name="serviceProvider">Services for dependency injection into the editor object.</param>
            <param name="editorOperations">The interface for operating an editor.</param>
            <param name="executionService">PowerShell execution service to run PowerShell execution requests.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.InitializeAsync">
            <summary>
            Initializes this ExtensionService using the provided IEditorOperations
            implementation for future interaction with the host editor.
            </summary>
            <returns>A Task that can be awaited for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.InvokeCommandAsync(System.String,Microsoft.PowerShell.EditorServices.Extensions.EditorContext,System.Threading.CancellationToken)">
            <summary>
            Invokes the specified editor command against the provided EditorContext.
            </summary>
            <param name="commandName">The unique name of the command to be invoked.</param>
            <param name="editorContext">The context in which the command is being invoked.</param>
            <param name="cancellationToken">The token used to cancel this.</param>
            <returns>A Task that can be awaited for completion.</returns>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">The command being invoked was not registered.</exception>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.RegisterCommand(Microsoft.PowerShell.EditorServices.Extensions.EditorCommand)">
            <summary>
            Registers a new EditorCommand with the ExtensionService and
            causes its details to be sent to the host editor.
            </summary>
            <param name="editorCommand">The details about the editor command to be registered.</param>
            <returns>True if the command is newly registered, false if the command already exists.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.UnregisterCommand(System.String)">
            <summary>
            Unregisters an existing EditorCommand based on its registered name.
            </summary>
            <param name="commandName">The name of the command to be unregistered.</param>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">The command being unregistered was not registered.</exception>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.GetCommands">
            <summary>
            Returns all registered EditorCommands.
            </summary>
            <returns>An Array of all registered EditorCommands.</returns>
        </member>
        <member name="E:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.CommandAdded">
            <summary>
            Raised when a new editor command is added.
            </summary>
        </member>
        <member name="E:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.CommandUpdated">
            <summary>
            Raised when an existing editor command is updated.
            </summary>
        </member>
        <member name="E:Microsoft.PowerShell.EditorServices.Services.Extension.ExtensionService.CommandRemoved">
            <summary>
            Raised when an existing editor command is removed.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Extension.ShowInputPromptRequest.Name">
            <summary>
            Gets or sets the name of the field.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Extension.ShowInputPromptRequest.Label">
            <summary>
            Gets or sets the descriptive label for the field.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Context.PowerShellVersionDetails">
            <summary>
            Provides details about the version of the PowerShell runtime.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Context.PowerShellVersionDetails.Version">
            <summary>
            Gets the version of the PowerShell runtime.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Context.PowerShellVersionDetails.Edition">
            <summary>
            Gets the PowerShell edition (generally Desktop or Core).
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Context.PowerShellVersionDetails.#ctor(System.Version,System.String)">
            <summary>
            Creates an instance of the PowerShellVersionDetails class.
            </summary>
            <param name="version">The version of the PowerShell runtime.</param>
            <param name="editionString">The string representation of the PowerShell edition.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Context.PowerShellVersionDetails.GetVersionDetails(Microsoft.Extensions.Logging.ILogger,System.Management.Automation.PowerShell)">
            <summary>
            Gets the PowerShell version details for the given runspace. This doesn't use
            VersionUtils because we may be remoting, and therefore want the remote runspace's
            version, not the local process.
            </summary>
            <param name="logger">An ILogger implementation used for writing log messages.</param>
            <param name="pwsh">The PowerShell instance for which to get the version.</param>
            <returns>A new PowerShellVersionDetails instance.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Debugging.PowerShellDebugContext">
            <summary>
            Handles the state of the PowerShell debugger.
            </summary>
            <remarks>
            <para>
            Debugging through a PowerShell Host is implemented by registering a handler for the <see
            cref="E:System.Management.Automation.Debugger.DebuggerStop"/> event. Registering that handler causes debug actions in
            PowerShell like Set-PSBreakpoint and Wait-Debugger to drop into the debugger and trigger the
            handler. The handler is passed a mutable <see cref="T:System.Management.Automation.DebuggerStopEventArgs"/> object and the
            debugger stop lasts for the duration of the handler call. The handler sets the <see
            cref="P:System.Management.Automation.DebuggerStopEventArgs.ResumeAction"/> property when after it returns, the PowerShell
            debugger uses that as the direction on how to proceed.
            </para>
            <para>
            When we handle the <see cref="E:System.Management.Automation.Debugger.DebuggerStop"/> event, we drop into a nested debug
            prompt and execute things in the debugger with <see cref="M:System.Management.Automation.Debugger.ProcessCommand(System.Management.Automation.PSCommand,System.Management.Automation.PSDataCollection{System.Management.Automation.PSObject})"/>, which enables debugger commands like <c>l</c>, <c>c</c>,
            <c>s</c>, etc. <see cref="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Debugging.PowerShellDebugContext"/> saves the event args object in its
            state, and when one of the debugger commands is used, the result returned is used to set
            <see cref="P:System.Management.Automation.DebuggerStopEventArgs.ResumeAction"/> on the saved event args object so that when
            the event handler returns, the PowerShell debugger takes the correct action.
            </para>
            </remarks>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Debugging.PowerShellDebugContext.IsStopped">
            <summary>
            Tracks if the debugger is currently stopped at a breakpoint.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Debugging.PowerShellDebugContext.IsActive" -->
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Debugging.PowerShellDebugContext.IsDebugServerActive">
            <summary>
            Tracks the state of the LSP debug server (not the PowerShell debugger).
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Debugging.PowerShellDebugContext.IsDebuggingRemoteRunspace">
            <summary>
            Tracks whether we are running <c>Debug-Runspace</c> in an out-of-process runspace.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Execution.BlockingConcurrentDeque`1">
            <summary>
            Implements a concurrent deque that supplies:
              - Non-blocking prepend and append operations
              - Blocking and non-blocking take calls
              - The ability to block consumers, so that <see cref="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Execution.BlockingConcurrentDeque`1.Prepend(`0)"/> can also guarantee the state of the consumer
            </summary>
            <typeparam name="T">The type of item held by this collection.</typeparam>
            <remarks>
            The prepend/append semantics of this class depend on the implementation semantics of <see cref="M:System.Collections.Concurrent.BlockingCollection`1.TryTakeFromAny(System.Collections.Concurrent.BlockingCollection{`0}[],`0@)"/>
            and its overloads checking the supplied array in order.
            This behavior is unlikely to change and ensuring its correctness at our layer is likely to be costly.
            See https://stackoverflow.com/q/26472251.
            </remarks>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.#ctor(Microsoft.Extensions.Logging.ILoggerFactory,System.Management.Automation.Host.PSHostRawUserInterface)">
            <summary>
            Creates a new instance of the TerminalPSHostRawUserInterface
            class with the given IConsoleHost implementation.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.BackgroundColor">
            <summary>
            Gets or sets the background color of the console.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.ForegroundColor">
            <summary>
            Gets or sets the foreground color of the console.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.BufferSize">
            <summary>
            Gets or sets the size of the console buffer.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.CursorPosition">
            <summary>
            Gets or sets the cursor's position in the console buffer.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.CursorSize">
            <summary>
            Gets or sets the size of the cursor in the console buffer.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.WindowPosition">
            <summary>
            Gets or sets the position of the console's window.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.WindowSize">
            <summary>
            Gets or sets the size of the console's window.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.WindowTitle">
            <summary>
            Gets or sets the console window's title.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.KeyAvailable">
            <summary>
            Gets a boolean that determines whether a keypress is available.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.MaxPhysicalWindowSize">
            <summary>
            Gets the maximum physical size of the console window.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.MaxWindowSize">
            <summary>
            Gets the maximum size of the console window.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.ReadKey(System.Management.Automation.Host.ReadKeyOptions)">
            <summary>
            Reads the current key pressed in the console.
            </summary>
            <param name="options">Options for reading the current keypress.</param>
            <returns>A KeyInfo struct with details about the current keypress.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.FlushInputBuffer">
            <summary>
            Flushes the current input buffer.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.GetBufferContents(System.Management.Automation.Host.Rectangle)">
            <summary>
            Gets the contents of the console buffer in a rectangular area.
            </summary>
            <param name="rectangle">The rectangle inside which buffer contents will be accessed.</param>
            <returns>A BufferCell array with the requested buffer contents.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.ScrollBufferContents(System.Management.Automation.Host.Rectangle,System.Management.Automation.Host.Coordinates,System.Management.Automation.Host.Rectangle,System.Management.Automation.Host.BufferCell)">
            <summary>
            Scrolls the contents of the console buffer.
            </summary>
            <param name="source">The source rectangle to scroll.</param>
            <param name="destination">The destination coordinates by which to scroll.</param>
            <param name="clip">The rectangle inside which the scrolling will be clipped.</param>
            <param name="fill">The cell with which the buffer will be filled.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.SetBufferContents(System.Management.Automation.Host.Rectangle,System.Management.Automation.Host.BufferCell)">
            <summary>
            Sets the contents of the buffer inside the specified rectangle.
            </summary>
            <param name="rectangle">The rectangle inside which buffer contents will be filled.</param>
            <param name="fill">The BufferCell which will be used to fill the requested space.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.SetBufferContents(System.Management.Automation.Host.Coordinates,System.Management.Automation.Host.BufferCell[0:,0:])">
            <summary>
            Sets the contents of the buffer at the given coordinate.
            </summary>
            <param name="origin">The coordinate at which the buffer will be changed.</param>
            <param name="contents">The new contents for the buffer at the given coordinate.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.LengthInBufferCells(System.Char)">
            <summary>
            Determines the number of BufferCells a character occupies.
            </summary>
            <param name="source">
            The character whose length we want to know.
            </param>
            <returns>
            The length in buffer cells according to the original host
            implementation for the process.
            </returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.LengthInBufferCells(System.String)">
            <summary>
            Determines the number of BufferCells a string occupies.
            </summary>
            <param name="source">
            The string whose length we want to know.
            </param>
            <returns>
            The length in buffer cells according to the original host
            implementation for the process.
            </returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostRawUserInterface.LengthInBufferCells(System.String,System.Int32)">
            <summary>
            Determines the number of BufferCells a substring of a string occupies.
            </summary>
            <param name="source">
            The string whose substring length we want to know.
            </param>
            <param name="offset">
            Offset where the substring begins in <paramref name="source"/>
            </param>
            <returns>
            The length in buffer cells according to the original host
            implementation for the process.
            </returns>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.EditorServicesConsolePSHostUserInterface._currentProgressRecords">
            <summary>
            We use a ConcurrentDictionary because ConcurrentHashSet does not exist, hence the value
            is never actually used, and `WriteProgress` must be thread-safe.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.PsesInternalHost.s_internalPSHostUserInterface">
            <summary>
            To workaround a horrid bug where the `TranscribeOnly` field of the PSHostUserInterface
            can accidentally remain true, we have to use a bunch of reflection so that <see
            cref="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.PsesInternalHost.DisableTranscribeOnly" /> can reset it to false. (This was fixed in PowerShell
            7.) Note that it must be the internal UI instance, not our own UI instance, otherwise
            this would be easier. Because of the amount of reflection involved, we contain it to
            only PowerShell 5.1 at compile-time, and we have to set this up in this class, not <see
            cref="!:SynchronousPowerShellTask" /> because that's templated, making statics practically
            useless. <see cref = "!:SynchronousPowerShellTask.ExecuteNormally" /> method calls <see
            cref="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.PsesInternalHost.DisableTranscribeOnly" /> when necessary.
            See: https://github.com/PowerShell/PowerShell/pull/3436
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.PsesInternalHost.DebugServer">
            <summary>
            TODO: Improve this coupling. It's assigned by <see cref="M:Microsoft.PowerShell.EditorServices.Server.PsesDebugServer.StartAsync" />
            so that the PowerShell process started when <see cref="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.CreateTemporaryIntegratedConsole" />
            is true can also receive the required 'sendKeyPress' notification to return from a
            canceled <see cref="M:System.Console.ReadKey" />.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.PsesInternalHost.TryStartAsync(Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.HostStartOptions,System.Threading.CancellationToken)">
            <summary>
            Try to start the PowerShell loop in the host.
            If the host is already started, this is idempotent.
            Returns when the host is in a valid initialized state.
            </summary>
            <param name="startOptions">Options to configure host startup.</param>
            <param name="cancellationToken">A token to cancel startup.</param>
            <returns>A task that resolves when the host has finished startup, with the value true if the caller started the host, and false otherwise.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.PsesInternalHost.WriteWithPrompt(System.Management.Automation.PSCommand,System.Threading.CancellationToken)">
            <summary>
            This is used to write the invocation text of a command with the user's prompt so that,
            for example, F8 (evaluate selection) appears as if the user typed it. Used when
            'WriteInputToHost' is true.
            </summary>
            <param name="command">The PSCommand we'll print after the prompt.</param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Host.PsesInternalHost.OnPowerShellIdle(System.Threading.CancellationToken)">
            <summary>
            This delegate is handed to PSReadLine and overrides similar logic within its `ReadKey`
            method. Essentially we're replacing PowerShell's `OnIdle` handler since the PowerShell
            engine isn't idle when we're sitting in PSReadLine's `ReadKey` loop. In our case we also
            use this idle time to process queued tasks by executing those that can run in the
            background, and canceling the foreground task if a queued tasks requires the foreground.
            Finally, if and only if we have to, we run an artificial pipeline to force PowerShell's
            own event processing.
            </summary>
            <param name="idleCancellationToken">
            This token is received from PSReadLine, and it is the ReadKey cancellation token!
            </param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangeAction">
            <summary>
            Defines the set of actions that will cause the runspace to be changed.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangeAction.Enter">
            <summary>
            The runspace change was caused by entering a new session.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangeAction.Exit">
            <summary>
            The runspace change was caused by exiting the current session.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangeAction.Shutdown">
            <summary>
            The runspace change was caused by shutting down the service.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangedEventArgs">
            <summary>
            Provides arguments for the PowerShellContext.RunspaceChanged event.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangedEventArgs.#ctor(Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangeAction,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceInfo,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceInfo)">
            <summary>
            Creates a new instance of the RunspaceChangedEventArgs class.
            </summary>
            <param name="changeAction">The action which caused the runspace to change.</param>
            <param name="previousRunspace">The previously active runspace.</param>
            <param name="newRunspace">The newly active runspace.</param>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangedEventArgs.ChangeAction">
            <summary>
            Gets the RunspaceChangeAction which caused this event.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangedEventArgs.PreviousRunspace">
            <summary>
            Gets a RunspaceDetails object describing the previous runspace.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceChangedEventArgs.NewRunspace">
            <summary>
            Gets a RunspaceDetails object describing the new runspace.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceOrigin">
            <summary>
            Specifies the context in which the runspace was encountered.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceOrigin.Local">
            <summary>
            The original runspace in a local session.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceOrigin.PSSession">
            <summary>
            A remote runspace entered through Enter-PSSession.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceOrigin.EnteredProcess">
            <summary>
            A runspace in a process that was entered with Enter-PSHostProcess.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.RunspaceOrigin.DebuggedRunspace">
            <summary>
            A runspace that is being debugged with Debug-Runspace.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.SessionDetails">
            <summary>
            Provides details about the current PowerShell session.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.SessionDetails.GetFromPowerShell(System.Management.Automation.PowerShell)">
            <summary>
            Runs a PowerShell command to gather details about the current session.
            </summary>
            <returns>A data object containing details about the PowerShell session.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.SessionDetails.#ctor(System.Int32,System.String,System.Nullable{System.Guid})">
            <summary>
            Creates an instance of SessionDetails using the information
            contained in the PSObject which was obtained using the
            PSCommand returned by GetDetailsCommand.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.SessionDetails.ProcessId">
            <summary>
            Gets the process ID of the current process.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.SessionDetails.ComputerName">
            <summary>
            Gets the name of the current computer.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.SessionDetails.InstanceId">
            <summary>
            Gets the current PSHost instance ID.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.CancellationContext">
            <summary>
            Encapsulates the scoping logic for cancellation tokens.
            As PowerShell commands nest, this class maintains a stack of cancellation scopes
            that allow each scope of logic to be cancelled at its own level.
            Implicitly handles the merging and cleanup of cancellation token sources.
            </summary>
            <example>
            The <see cref="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.CancellationContext"/> class
            and the <see cref="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.CancellationScope"/> struct
            are intended to be used with a <c>using</c> block so you can do this:
            <code>
                using (CancellationScope cancellationScope = _cancellationContext.EnterScope(_globalCancellationSource.CancellationToken, localCancellationToken))
                {
                    ExecuteCommandAsync(command, cancellationScope.CancellationToken);
                }
            </code>
            </example>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.CancellationContext.CancelIdleParentTask">
            <summary>
            Cancels the parent task of the idle task.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.CommandHelpers">
            <summary>
            Provides utility methods for working with PowerShell commands.
            TODO: Handle the `fn ` prefix better.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.CommandHelpers.StripModuleQualification(System.String,System.ReadOnlyMemory{System.Char}@)">
            <summary>
            Gets the actual command behind a fully module qualified command invocation, e.g.
            <c>Microsoft.PowerShell.Management\Get-ChildItem</c> will return <c>Get-ChildItem</c>
            </summary>
            <param name="invocationName">
            The potentially module qualified command name at the site of invocation.
            </param>
            <param name="moduleName">
            A reference that will contain the module name if the invocation is module qualified.
            </param>
            <returns>The actual command name.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.CommandHelpers.GetCommandInfoAsync(System.String,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceInfo,Microsoft.PowerShell.EditorServices.Services.PowerShell.IInternalPowerShellExecutionService,System.Threading.CancellationToken)">
            <summary>
            Gets the CommandInfo instance for a command with a particular name.
            </summary>
            <param name="commandName">The name of the command.</param>
            <param name="currentRunspace">The current runspace.</param>
            <param name="executionService">The execution service.</param>
            <param name="cancellationToken">The token used to cancel this.</param>
            <returns>A CommandInfo object with details about the specified command.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.CommandHelpers.GetCommandSynopsisAsync(System.Management.Automation.CommandInfo,Microsoft.PowerShell.EditorServices.Services.PowerShell.IInternalPowerShellExecutionService,System.Threading.CancellationToken)">
            <summary>
            Gets the command's "Synopsis" documentation section.
            </summary>
            <param name="commandInfo">The CommandInfo instance for the command.</param>
            <param name="executionService">The execution service to use for getting command documentation.</param>
            <param name="cancellationToken">The token used to cancel this.</param>
            <returns>The synopsis.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.CommandHelpers.GetAliasesAsync(Microsoft.PowerShell.EditorServices.Services.PowerShell.IInternalPowerShellExecutionService,System.Threading.CancellationToken)">
            <summary>
            Gets all aliases found in the runspace
            </summary>
            <param name="executionService"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.PowerShellExtensions.WaitForRemoteOutputIfNeeded(System.Management.Automation.PowerShell)">
            <summary>
            When running a remote session, waits for remote processing and output to complete.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.PowerShell.Utility.RunspaceExtensions.GetRemotePrompt(System.Management.Automation.Runspaces.Runspace,System.String)">
            <summary>
            Augment a given prompt string with a remote decoration.
            This is an internal method on <c>Runspace</c> in PowerShell that we reuse via reflection.
            </summary>
            <param name="runspace">The runspace the prompt is for.</param>
            <param name="basePrompt">The base prompt to decorate.</param>
            <returns>A prompt string decorated with remote connection details.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.IDocumentSymbolProvider">
            <summary>
            Specifies the contract for a document symbols provider.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.IDocumentSymbolProvider.ProvideDocumentSymbols(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Provides a list of symbols for the given document.
            </summary>
            <param name="scriptFile">
            The document for which SymbolReferences should be provided.
            </param>
            <returns>An IEnumerable collection of SymbolReferences.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterSetSignatures">
            <summary>
            A class for containing the commandName, the command's
            possible signatures, and the script extent of the command
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterSetSignatures.CommandName">
            <summary>
            Gets the name of the command
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterSetSignatures.Signatures">
            <summary>
            Gets the collection of signatures for the command
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterSetSignatures.ScriptRegion">
            <summary>
            Gets the script extent of the command
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterSetSignatures.#ctor(System.Collections.Generic.IEnumerable{System.Management.Automation.CommandParameterSetInfo},Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolReference)">
            <summary>
            Constructs an instance of a ParameterSetSignatures object
            </summary>
            <param name="commandInfoSet">Collection of parameter set info</param>
            <param name="foundSymbol"> The SymbolReference of the command</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterSetSignature">
            <summary>
            A class for containing the signature text and the collection of parameters for a signature
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterSetSignature.SignatureText">
            <summary>
            Gets the signature text
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterSetSignature.Parameters">
            <summary>
            Gets the collection of parameters for the signature
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterSetSignature.#ctor(System.Management.Automation.CommandParameterSetInfo)">
            <summary>
            Constructs an instance of a ParameterSetSignature
            </summary>
            <param name="commandParamInfoSet">Collection of parameter info</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterInfo">
            <summary>
            A class for containing the parameter info of a parameter
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterInfo.Name">
            <summary>
            Gets the name of the parameter
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterInfo.ParameterType">
            <summary>
            Gets the type of the parameter
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterInfo.Position">
            <summary>
            Gets the position of the parameter
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterInfo.IsMandatory">
            <summary>
            Gets a boolean for whetheer or not the parameter is required
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterInfo.HelpMessage">
            <summary>
            Gets the help message of the parameter
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.ParameterInfo.#ctor(System.Management.Automation.CommandParameterInfo)">
            <summary>
            Constructs an instance of a ParameterInfo object
            </summary>
            <param name="parameterInfo">Parameter info of the parameter</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterDocumentSymbolProvider">
            <summary>
            Provides an IDocumentSymbolProvider implementation for
            enumerating test symbols in Pester test (tests.ps1) files.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterDocumentSymbolProvider.IsNamedCommandWithArguments(System.Management.Automation.Language.Ast)">
            <summary>
            Test if the given Ast is a regular CommandAst with arguments
            </summary>
            <param name="ast">the PowerShell Ast to test</param>
            <returns>true if the Ast represents a PowerShell command with arguments, false otherwise</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterDocumentSymbolProvider.IsPesterCommand(System.Management.Automation.Language.CommandAst)">
            <summary>
            Test whether the given CommandAst represents a Pester command
            </summary>
            <param name="commandAst">the CommandAst to test</param>
            <returns>true if the CommandAst represents a Pester command, false otherwise</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterDocumentSymbolProvider.ConvertPesterAstToSymbolReference(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Management.Automation.Language.CommandAst)">
            <summary>
            Convert a CommandAst known to represent a Pester command and a reference to the scriptfile
            it is in into symbol representing a Pester call for code lens
            </summary>
            <param name="scriptFile">the scriptfile the Pester call occurs in</param>
            <param name="pesterCommandAst">the CommandAst representing the Pester call</param>
            <returns>a symbol representing the Pester call containing metadata for CodeLens to use</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType">
            <summary>
            Defines command types for Pester blocks.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType.Describe">
            <summary>
            Identifies a Describe block.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType.Context">
            <summary>
            Identifies a Context block.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType.It">
            <summary>
            Identifies an It block.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType.BeforeAll">
            <summary>
            Identifies an BeforeAll block.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType.BeforeEach">
            <summary>
            Identifies an BeforeEach block.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType.AfterAll">
            <summary>
            Identifies an AfterAll block.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType.AfterEach">
            <summary>
            Identifies an AfterEach block.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType.BeforeDiscovery">
            <summary>
            Identifies an BeforeDiscovery block.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterSymbolReference">
            <summary>
            Provides a specialization of SymbolReference containing
            extra information about Pester test symbols.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterSymbolReference.PesterKeywords">
            <summary>
            Lookup for Pester keywords we support. Ideally we could extract these from Pester itself
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterSymbolReference.TestName">
            <summary>
            Gets the name of the test
            TODO: We could get rid of this and use DisplayName now, but first attempt didn't work great.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterSymbolReference.Command">
            <summary>
            Gets the test's command type.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.PesterSymbolReference.IsPesterTestCommand(Microsoft.PowerShell.EditorServices.Services.Symbols.PesterCommandType)">
            <summary>
            Checks if the PesterCommandType is a block with executable tests (Describe/Context/It).
            </summary>
            <param name="pesterCommandType">the PesterCommandType representing the Pester command</param>
            <returns>True if command type is a block used to trigger test run. False if setup/teardown/support-block.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.PsdDocumentSymbolProvider">
            <summary>
            Provides an IDocumentSymbolProvider implementation for
            enumerating symbols in .psd1 files.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.PsdDocumentSymbolProvider.IsPowerShellDataFileAst(System.Management.Automation.Language.Ast)">
            <summary>
            Checks if a given ast represents the root node of a *.psd1 file.
            </summary>
            <param name="ast">The abstract syntax tree of the given script</param>
            <returns>true if the AST represents a *.psd1 file, otherwise false</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.RegionDocumentSymbolProvider">
            <summary>
            Provides an IDocumentSymbolProvider implementation for
            enumerating regions as symbols in script (.psd1, .psm1) files.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptDocumentSymbolProvider">
            <summary>
            Provides an IDocumentSymbolProvider implementation for
            enumerating symbols in script (.psd1, .psm1) files.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent">
            <summary>
            Provides a default IScriptExtent implementation
            containing details about a section of script content
            in a file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.File">
            <summary>
            Gets the file path of the script file in which this extent is contained.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.StartColumnNumber">
            <summary>
            Gets or sets the starting column number of the extent.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.StartLineNumber">
            <summary>
            Gets or sets the starting line number of the extent.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.StartOffset">
            <summary>
            Gets or sets the starting file offset of the extent.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.StartScriptPosition">
            <summary>
            Gets or sets the starting script position of the extent.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.Text">
            <summary>
            Gets or sets the text that is contained within the extent.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.EndColumnNumber">
            <summary>
            Gets or sets the ending column number of the extent.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.EndLineNumber">
            <summary>
            Gets or sets the ending line number of the extent.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.EndOffset">
            <summary>
            Gets or sets the ending file offset of the extent.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.ScriptExtent.EndScriptPosition">
            <summary>
            Gets the ending script position of the extent.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolDetails">
            <summary>
            Provides detailed information for a given symbol.
            TODO: Get rid of this and just use return documentation.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolDetails.SymbolReference">
            <summary>
            Gets the original symbol reference which was used to gather details.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolDetails.Documentation">
            <summary>
            Gets the documentation string for this symbol.  Returns an
            empty string if the symbol has no documentation.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolReference">
            <summary>
            A class that holds the type, name, script extent, and source line of a symbol
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolReference.#ctor(Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType,System.String,System.String,System.Management.Automation.Language.IScriptExtent,System.Management.Automation.Language.IScriptExtent,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Boolean)">
            <summary>
            Constructs and instance of a SymbolReference
            </summary>
            <param name="type">The higher level type of the symbol</param>
            <param name="id">The name of the symbol</param>
            <param name="name">The string used by outline, hover, etc.</param>
            <param name="nameExtent">The extent of the symbol's name</param>
            <param name="scriptExtent">The script extent of the symbol</param>
            <param name="file">The script file that has the symbol</param>
            <param name="isDeclaration">True if this reference is the definition of the symbol</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolReference.#ctor(System.String,Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType)">
            <summary>
            This is only used for unit tests!
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType">
            <summary>
            A way to define symbols on a higher level
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Unknown">
            <summary>
            The symbol type is unknown
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Variable">
            <summary>
            The symbol is a variable
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Function">
            <summary>
            The symbol is a function
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Parameter">
            <summary>
            The symbol is a parameter
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Configuration">
            <summary>
            The symbol is a DSC configuration
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Workflow">
            <summary>
            The symbol is a workflow
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.HashtableKey">
            <summary>
            The symbol is a hashtable key
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Class">
            <summary>
            The symbol is a class
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Enum">
            <summary>
            The symbol is a enum
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.EnumMember">
            <summary>
            The symbol is a enum member/value
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Property">
            <summary>
            The symbol is a class property
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Method">
            <summary>
            The symbol is a class method
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Constructor">
            <summary>
            The symbol is a class constructor
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Type">
            <summary>
            The symbol is a type reference
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolType.Region">
            <summary>
            The symbol is a region. Only used for navigation-features.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.AstOperations">
            <summary>
            Provides common operations for the syntax tree of a parsed script.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.AstOperations.GetCompletionsAsync(System.Management.Automation.Language.Ast,System.Management.Automation.Language.Token[],System.Int32,Microsoft.PowerShell.EditorServices.Services.PowerShell.IInternalPowerShellExecutionService,Microsoft.Extensions.Logging.ILogger,System.Threading.CancellationToken)">
            <summary>
            Gets completions for the symbol found in the Ast at
            the given file offset.
            </summary>
            <param name="scriptAst">
            The Ast which will be traversed to find a completable symbol.
            </param>
            <param name="currentTokens">
            The array of tokens corresponding to the scriptAst parameter.
            </param>
            <param name="fileOffset">
            The 1-based file offset at which a symbol will be located.
            </param>
            <param name="executionService">
            The PowerShellContext to use for gathering completions.
            </param>
            <param name="logger">An ILogger implementation used for writing log messages.</param>
            <param name="cancellationToken">
            A CancellationToken to cancel completion requests.
            </param>
            <returns>
            A CommandCompletion instance that contains completions for the
            symbol at the given offset.
            </returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.FindHashtableSymbolsVisitor">
            <summary>
            Visitor to find all the keys in Hashtable AST
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Symbols.FindHashtableSymbolsVisitor.SymbolReferences">
            <summary>
            List of symbols (keys) found in the hashtable
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.FindHashtableSymbolsVisitor.#ctor(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Initializes a new instance of FindHashtableSymbolsVisitor class
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Symbols.FindHashtableSymbolsVisitor.VisitHashtable(System.Management.Automation.Language.HashtableAst)">
            <summary>
            Adds keys in the input hashtable to the symbol reference
            </summary>
            <param name="hashtableAst">A HashtableAst in the script's AST</param>
            <returns>A visit action that continues the search for references</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolVisitor">
            <summary>
            The goal of this is to be our one and only visitor, which parses a file when necessary
            performing Action, which takes a SymbolReference (that this visitor creates) and returns an
            AstVisitAction. In this way, all our symbols are created with the same initialization logic.
            TODO: Visit hashtable keys, constants, arrays, namespaces, interfaces, operators, etc.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.ReferenceTable">
            <summary>
            Represents the symbols that are referenced and their locations within a single document.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.ReferenceTable.TagAsChanged">
            <summary>
            Clears the reference table causing it to re-scan the source AST when queried.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.ReferenceTable.IsInitialized">
            <summary>
            Prefer checking if the dictionary has contents to determine if initialized. The field
            `_isInited` is to guard against re-scanning files with no command references, but will
            generally be less reliable of a check.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.SymbolsService">
            <summary>
            Provides a high-level service for performing code completion and
            navigation operations on PowerShell scripts.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.#ctor(Microsoft.Extensions.Logging.ILoggerFactory,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceContext,Microsoft.PowerShell.EditorServices.Services.PowerShell.IInternalPowerShellExecutionService,Microsoft.PowerShell.EditorServices.Services.WorkspaceService,Microsoft.PowerShell.EditorServices.Services.ConfigurationService)">
            <summary>
            Constructs an instance of the SymbolsService class and uses
            the given Runspace to execute language service operations.
            </summary>
            <param name="factory">An ILoggerFactory implementation used for writing log messages.</param>
            <param name="runspaceContext"></param>
            <param name="executionService"></param>
            <param name="workspaceService"></param>
            <param name="configurationService"></param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.FindSymbolsInFile(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Finds all the symbols in a file, through all document symbol providers.
            </summary>
            <param name="scriptFile">The ScriptFile in which the symbol can be located.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.FindSymbolAtLocation(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Int32,System.Int32)">
            <summary>
            Finds the symbol in the script given a file location.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.ScanForReferencesOfSymbolAsync(Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolReference,System.Threading.CancellationToken)">
            <summary>
            Finds all the references of a symbol in the workspace, resolving aliases.
            TODO: One day use IAsyncEnumerable.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.FindOccurrencesInFile(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Int32,System.Int32)">
            <summary>
            Finds all the occurrences of a symbol in the script given a file location.
            TODO: Doesn't support aliases, is it worth it?
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.FindSymbolDefinitionAtLocation(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Int32,System.Int32)">
            <summary>
            Finds the symbol at the location and returns it if it's a declaration.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.FindSymbolDetailsAtLocationAsync(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Finds the details of the symbol at the given script file location.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.FindParameterSetsInFileAsync(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Int32,System.Int32)">
            <summary>
            Finds the parameter set hints of a specific command (determined by a given file location)
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.GetDefinitionOfSymbolAsync(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,Microsoft.PowerShell.EditorServices.Services.Symbols.SymbolReference,System.Threading.CancellationToken)">
            <summary>
            Finds the possible definitions of the symbol in the file or workspace.
            TODO: One day use IAsyncEnumerable.
            TODO: Fix searching for definition of built-in commands.
            TODO: Fix "definition" of dot-source (maybe?)
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.GetFunctionDefinitionForHelpComment(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Int32,System.String@)">
            <summary>
            Finds a function definition that follows or contains the given line number.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.SymbolsService.GetFunctionDefinitionAtLine(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Int32)">
            <summary>
            Gets the function defined on a given line.
            TODO: Remove this.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition">
            <summary>
            Provides details about a position in a file buffer.  All
            positions are expressed in 1-based positions (i.e. the
            first line and column in the file is position 1,1).
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition.None">
            <summary>
            Provides an instance that represents a position that has not been set.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition.Line">
            <summary>
            Gets the line number of the position in the buffer.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition.Column">
            <summary>
            Gets the column number of the position in the buffer.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates a new instance of the BufferPosition class.
            </summary>
            <param name="line">The line number of the position.</param>
            <param name="column">The column number of the position.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition.Equals(System.Object)">
            <summary>
            Compares two instances of the BufferPosition class.
            </summary>
            <param name="obj">The object to which this instance will be compared.</param>
            <returns>True if the positions are equal, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition.GetHashCode">
            <summary>
            Calculates a unique hash code that represents this instance.
            </summary>
            <returns>A hash code representing this instance.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition.op_GreaterThan(Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition,Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition)">
            <summary>
            Compares two positions to check if one is greater than the other.
            </summary>
            <param name="positionOne">The first position to compare.</param>
            <param name="positionTwo">The second position to compare.</param>
            <returns>True if positionOne is greater than positionTwo.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition.op_LessThan(Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition,Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition)">
            <summary>
            Compares two positions to check if one is less than the other.
            </summary>
            <param name="positionOne">The first position to compare.</param>
            <param name="positionTwo">The second position to compare.</param>
            <returns>True if positionOne is less than positionTwo.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange">
            <summary>
            Provides details about a range between two positions in
            a file buffer.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange.None">
            <summary>
            Provides an instance that represents a range that has not been set.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange.Start">
            <summary>
            Gets the start position of the range in the buffer.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange.End">
            <summary>
            Gets the end position of the range in the buffer.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange.HasRange">
            <summary>
            Returns true if the current range is non-zero, i.e.
            contains valid start and end positions.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange.#ctor(Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition,Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition)">
            <summary>
            Creates a new instance of the BufferRange class.
            </summary>
            <param name="start">The start position of the range.</param>
            <param name="end">The end position of the range.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a new instance of the BufferRange class.
            </summary>
            <param name="startLine">The 1-based starting line number of the range.</param>
            <param name="startColumn">The 1-based starting column number of the range.</param>
            <param name="endLine">The 1-based ending line number of the range.</param>
            <param name="endColumn">The 1-based ending column number of the range.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange.Equals(System.Object)">
            <summary>
            Compares two instances of the BufferRange class.
            </summary>
            <param name="obj">The object to which this instance will be compared.</param>
            <returns>True if the ranges are equal, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange.GetHashCode">
            <summary>
            Calculates a unique hash code that represents this instance.
            </summary>
            <returns>A hash code representing this instance.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.FileChange">
            <summary>
            Contains details relating to a content change in an open file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FileChange.InsertString">
            <summary>
            The string which is to be inserted in the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FileChange.Line">
            <summary>
            The 1-based line number where the change starts.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FileChange.Offset">
            <summary>
            The 1-based column offset where the change starts.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FileChange.EndLine">
            <summary>
            The 1-based line number where the change ends.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FileChange.EndOffset">
            <summary>
            The 1-based column offset where the change ends.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FileChange.IsReload">
            <summary>
            Indicates that the InsertString is an overwrite
            of the content, and all stale content and metadata
            should be discarded.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.FilePosition">
            <summary>
            Provides details and operations for a buffer position in a
            specific file.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.FilePosition.#ctor(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Int32,System.Int32)">
            <summary>
            Creates a new FilePosition instance for the 1-based line and
            column numbers in the specified file.
            </summary>
            <param name="scriptFile">The ScriptFile in which the position is located.</param>
            <param name="line">The 1-based line number in the file.</param>
            <param name="column">The 1-based column number in the file.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.FilePosition.#ctor(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition)">
            <summary>
            Creates a new FilePosition instance for the specified file by
            copying the specified BufferPosition
            </summary>
            <param name="scriptFile">The ScriptFile in which the position is located.</param>
            <param name="copiedPosition">The original BufferPosition from which the line and column will be copied.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.FilePosition.AddOffset(System.Int32,System.Int32)">
            <summary>
            Gets a FilePosition relative to this position by adding the
            provided line and column offset relative to the contents of
            the current file.
            </summary>
            <param name="lineOffset">The line offset to add to this position.</param>
            <param name="columnOffset">The column offset to add to this position.</param>
            <returns>A new FilePosition instance for the calculated position.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.FilePosition.GetLineStart">
            <summary>
            Gets a FilePosition for the line and column position
            of the beginning of the current line after any initial
            whitespace for indentation.
            </summary>
            <returns>A new FilePosition instance for the calculated position.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.FilePosition.GetLineEnd">
            <summary>
            Gets a FilePosition for the line and column position
            of the end of the current line.
            </summary>
            <returns>A new FilePosition instance for the calculated position.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReference">
            <summary>
            A class that holds the information for a foldable region of text in a document
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReference.StartLine">
            <summary>
            The zero-based line number from where the folded range starts.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReference.StartCharacter">
            <summary>
            The zero-based character offset from where the folded range starts. If not defined, defaults to the length of the start line.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReference.EndLine">
            <summary>
            The zero-based line number where the folded range ends.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReference.EndCharacter">
            <summary>
            The zero-based character offset before the folded range ends. If not defined, defaults to the length of the end line.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReference.Kind">
            <summary>
            Describes the kind of the folding range such as `comment' or 'region'.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReference.CompareTo(Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReference)">
            <summary>
            A custom comparable method which can properly sort FoldingReference objects
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReferenceList">
            <summary>
            A class that holds a list of FoldingReferences and ensures that when adding a reference that the
            folding rules are obeyed, e.g. Only one fold per start line
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReferenceList.References">
            <summary>
            Return all references in the list
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReferenceList.SafeAdd(Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReference)">
            <summary>
            Adds a FoldingReference to the list and enforces ordering rules e.g. Only one fold per start line
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.FoldingReferenceList.ToArray">
            <summary>
            Helper method to easily convert the Dictionary Values into an array
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile">
            <summary>
            Contains the details and contents of an open script file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.FilePath">
            <summary>
            Gets the path at which this file resides.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.DocumentUri">
            <summary>
            Gets the file path in LSP DocumentUri form.  The ClientPath property must not be null.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.IsAnalysisEnabled">
            <summary>
            Gets or sets a boolean that determines whether
            semantic analysis should be enabled for this file.
            For internal use only.
            TODO: Actually use and respect this property to avoid built-in files from being analyzed.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.IsInMemory">
            <summary>
            Gets a boolean that determines whether this file is
            in-memory or not (either unsaved or non-file content).
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.Contents">
            <summary>
            Gets a string containing the full contents of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.FileRange">
            <summary>
            Gets a BufferRange that represents the entire content
            range of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.DiagnosticMarkers">
            <summary>
            Gets the list of syntax markers found by parsing this
            file's contents.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.FileLines">
            <summary>
            Gets the list of strings for each line of the file.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.ScriptAst">
            <summary>
            Gets the ScriptBlockAst representing the parsed script contents.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.ScriptTokens">
            <summary>
            Gets the array of Tokens representing the parsed script contents.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.#ctor(OmniSharp.Extensions.LanguageServer.Protocol.DocumentUri,System.IO.TextReader,System.Version)">
            <summary>
            Creates a new ScriptFile instance by reading file contents from
            the given TextReader.
            </summary>
            <param name="docUri">The System.Uri of the file.</param>
            <param name="textReader">The TextReader to use for reading the file's contents.</param>
            <param name="powerShellVersion">The version of PowerShell for which the script is being parsed.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.Create(OmniSharp.Extensions.LanguageServer.Protocol.DocumentUri,System.String,System.Version)">
            <summary>
            Creates a new ScriptFile instance with the specified file contents.
            </summary>
            <param name="fileUri">The System.Uri of the file.</param>
            <param name="initialBuffer">The initial contents of the script file.</param>
            <param name="powerShellVersion">The version of PowerShell for which the script is being parsed.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.GetLines(System.String)">
            <summary>
            Get the lines in a string.
            </summary>
            <param name="text">Input string to be split up into lines.</param>
            <returns>The lines in the string.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.IsUntitledPath(System.String)">
            <summary>
            Determines whether the supplied path indicates the file is an "untitled:Untitled-X"
            which has not been saved to file.
            </summary>
            <param name="path">The path to check.</param>
            <returns>True if the path is an untitled file, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.GetLine(System.Int32)">
            <summary>
            Gets a line from the file's contents.
            </summary>
            <param name="lineNumber">The 1-based line number in the file.</param>
            <returns>The complete line at the given line number.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.GetLinesInRange(Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferRange)">
            <summary>
            Gets a range of lines from the file's contents.
            </summary>
            <param name="bufferRange">The buffer range from which lines will be extracted.</param>
            <returns>An array of strings from the specified range of the file.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.ValidatePosition(Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition)">
            <summary>
            Throws ArgumentOutOfRangeException if the given position is outside
            of the file's buffer extents.
            </summary>
            <param name="bufferPosition">The position in the buffer to be validated.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.ValidatePosition(System.Int32,System.Int32)">
            <summary>
            Throws ArgumentOutOfRangeException if the given position is outside
            of the file's buffer extents.
            </summary>
            <param name="line">The 1-based line to be validated.</param>
            <param name="column">The 1-based column to be validated.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.ApplyChange(Microsoft.PowerShell.EditorServices.Services.TextDocument.FileChange)">
            <summary>
            Applies the provided FileChange to the file's contents
            </summary>
            <param name="fileChange">The FileChange to apply to the file's contents.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.GetOffsetAtPosition(System.Int32,System.Int32)">
            <summary>
            Calculates the zero-based character offset of a given
            line and column position in the file.
            </summary>
            <param name="lineNumber">The 1-based line number from which the offset is calculated.</param>
            <param name="columnNumber">The 1-based column number from which the offset is calculated.</param>
            <returns>The zero-based offset for the given file position.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.CalculatePosition(Microsoft.PowerShell.EditorServices.Services.TextDocument.BufferPosition,System.Int32,System.Int32)">
            <summary>
            Calculates a FilePosition relative to a starting BufferPosition
            using the given 1-based line and column offset.
            </summary>
            <param name="originalPosition">The original BufferPosition from which an new position should be calculated.</param>
            <param name="lineOffset">The 1-based line offset added to the original position in this file.</param>
            <param name="columnOffset">The 1-based column offset added to the original position in this file.</param>
            <returns>A new FilePosition instance with the resulting line and column number.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.GetPositionAtOffset(System.Int32)">
            <summary>
            Calculates the 1-based line and column number position based
            on the given buffer offset.
            </summary>
            <param name="bufferOffset">The buffer offset to convert.</param>
            <returns>A new BufferPosition containing the position of the offset.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.GetRangeBetweenOffsets(System.Int32,System.Int32)">
            <summary>
            Calculates the 1-based line and column number range based on
            the given start and end buffer offsets.
            </summary>
            <param name="startOffset">The start offset of the range.</param>
            <param name="endOffset">The end offset of the range.</param>
            <returns>A new BufferRange containing the positions in the offset range.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile.ParseFileContents">
            <summary>
            Parses the current file contents to get the AST, tokens,
            and parse errors.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.MarkerCorrection">
            <summary>
            Contains details for a code correction which can be applied from a ScriptFileMarker.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.MarkerCorrection.Name">
            <summary>
            Gets or sets the display name of the code correction.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.MarkerCorrection.Edit">
            <summary>
            Gets or sets the ScriptRegion that define the edit to be made by the correction.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarkerLevel">
            <summary>
            Defines the message level of a script file marker.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarkerLevel.Information">
            <summary>
            Information: This warning is trivial, but may be useful. They are recommended by PowerShell best practice.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarkerLevel.Warning">
            <summary>
            WARNING: This warning may cause a problem or does not follow PowerShell's recommended guidelines.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarkerLevel.Error">
            <summary>
            ERROR: This warning is likely to cause a problem or does not follow PowerShell's required guidelines.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarkerLevel.ParseError">
            <summary>
            ERROR: This diagnostic is caused by an actual parsing error, and is generated only by the engine.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarker">
            <summary>
            Contains details about a marker that should be displayed
            for the a script file.  The marker information could come
            from syntax parsing or semantic analysis of the script.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarker.Message">
            <summary>
            Gets or sets the marker's message string.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarker.RuleName">
            <summary>
            Gets or sets the ruleName associated with this marker.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarker.Level">
            <summary>
            Gets or sets the marker's message level.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarker.ScriptRegion">
            <summary>
            Gets or sets the ScriptRegion where the marker should appear.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarker.Corrections">
            <summary>
            Gets or sets a optional code corrections that can be applied based on its marker.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFileMarker.Source">
            <summary>
            Gets or sets the name of the marker's source like "PowerShell"
            or "PSScriptAnalyzer".
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion">
            <summary>
            Contains details about a specific region of text in script file.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.Create(System.Management.Automation.Language.IScriptExtent)">
            <summary>
            NOTE: While unused, we kept this as it was previously exposed on a public class.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.File">
            <summary>
            Gets the file path of the script file in which this region is contained.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.Text">
            <summary>
            Gets or sets the text that is contained within the region.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.StartLineNumber">
            <summary>
            Gets or sets the starting line number of the region.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.StartColumnNumber">
            <summary>
            Gets or sets the starting column number of the region.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.StartOffset">
            <summary>
            Gets or sets the starting file offset of the region.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.EndLineNumber">
            <summary>
            Gets or sets the ending line number of the region.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.EndColumnNumber">
            <summary>
            Gets or sets the ending column number of the region.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.EndOffset">
            <summary>
            Gets or sets the ending file offset of the region.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.System#Management#Automation#Language#IScriptExtent#StartScriptPosition">
            <summary>
            Gets the starting IScriptPosition in the script.
            (Currently unimplemented.)
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptRegion.System#Management#Automation#Language#IScriptExtent#EndScriptPosition">
            <summary>
            Gets the ending IScriptPosition in the script.
            (Currently unimplemented.)
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.TextDocument.TokenOperations">
            <summary>
            Provides common operations for the tokens of a parsed script.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.TokenOperations.FoldableReferences(System.Management.Automation.Language.Token[])">
            <summary>
            Extracts all of the unique foldable regions in a script given the list tokens
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.TokenOperations.CreateFoldingReference(System.Management.Automation.Language.Token,System.Management.Automation.Language.Token,System.Nullable{OmniSharp.Extensions.LanguageServer.Protocol.Models.FoldingRangeKind})">
            <summary>
            Creates an instance of a FoldingReference object from a start and end langauge Token
            Returns null if the line range is invalid
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.TokenOperations.CreateFoldingReference(System.Management.Automation.Language.Token,System.Int32,System.Nullable{OmniSharp.Extensions.LanguageServer.Protocol.Models.FoldingRangeKind})">
            <summary>
            Creates an instance of a FoldingReference object from a start token and an end line
            Returns null if the line range is invalid
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.TextDocument.TokenOperations.IsBlockComment(System.Int32,System.Management.Automation.Language.Token[])">
            <summary>
            Returns true if a Token is a block comment;
            - Must be a TokenKind.comment
            - Must be preceded by TokenKind.NewLine
            - Token text must start with a '#'.false  This is because comment regions
              start with '&lt;#' but have the same TokenKind
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFormattingPreset">
            <summary>
            Code formatting presets.
            See https://en.wikipedia.org/wiki/Indent_style for details on indent and brace styles.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFormattingPreset.Custom">
            <summary>
            Use the formatting settings as-is.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFormattingPreset.Allman">
            <summary>
            Configure the formatting settings to resemble the Allman indent/brace style.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFormattingPreset.OTBS" -->
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFormattingPreset.Stroustrup">
            <summary>
            Configure the formatting settings to resemble the Stroustrup brace style variant of K&amp;R indent/brace style.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Configuration.PipelineIndentationStyle">
            <summary>
            Multi-line pipeline style settings.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Configuration.PipelineIndentationStyle.IncreaseIndentationForFirstPipeline">
            <summary>
            After the indentation level only once after the first pipeline and keep this level for the following pipelines.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Configuration.PipelineIndentationStyle.IncreaseIndentationAfterEveryPipeline">
            <summary>
            After every pipeline, keep increasing the indentation.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Configuration.PipelineIndentationStyle.NoIndentation">
            <summary>
            Do not increase indentation level at all after pipeline.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Services.Configuration.PipelineIndentationStyle.None">
            <summary>
            Do not change pipeline indentation level at all.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFormattingSettings.#ctor">
            <summary>
            Default constructor.
            </summary>>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFormattingSettings.#ctor(Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFormattingSettings)">
            <summary>
            Copy constructor.
            </summary>
            <param name="codeFormattingSettings">An instance of type CodeFormattingSettings.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFormattingSettings.GetPSSASettingsHashtable(System.Int32,System.Boolean,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Get the settings hashtable that will be consumed by PSScriptAnalyzer.
            </summary>
            <param name="tabSize">The tab size in the number spaces.</param>
            <param name="insertSpaces">If true, insert spaces otherwise insert tabs for indentation.</param>
            <param name="logger">The logger instance.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFoldingSettings">
            <summary>
            Code folding settings
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFoldingSettings.Enable">
            <summary>
            Whether the folding is enabled. Default is true as per VSCode
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFoldingSettings.ShowLastLine">
            <summary>
            Whether to show or hide the last line of a folding region. Default is true as per VSCode
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFoldingSettings.Update(Microsoft.PowerShell.EditorServices.Services.Configuration.CodeFoldingSettings,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Update these settings from another settings object
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Configuration.PesterSettings">
            <summary>
            Pester settings
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Configuration.PesterSettings.CodeLens">
            <summary>
            If specified, the lenses "run tests" and "debug tests" will appear above all Pester tests
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Configuration.PesterSettings.UseLegacyCodeLens">
            <summary>
            Whether integration features specific to Pester v5 are enabled
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Configuration.PesterSettings.Update(Microsoft.PowerShell.EditorServices.Services.Configuration.PesterSettings,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Update these settings from another settings object
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Configuration.EditorFileSettings">
            <summary>
            Additional settings from the Language Client that affect Language Server operations but
            do not exist under the 'powershell' section
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Configuration.EditorFileSettings.Exclude">
            <summary>
            Exclude files globs consists of hashtable with the key as the glob and a boolean value
            OR object with a predicate clause to indicate if the glob is in effect.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Configuration.EditorSearchSettings">
            <summary>
            Additional settings from the Language Client that affect Language Server operations but
            do not exist under the 'powershell' section
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Configuration.EditorSearchSettings.Exclude">
            <summary>
            Exclude files globs consists of hashtable with the key as the glob and a boolean value
            OR object with a predicate clause to indicate if the glob is in effect.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Configuration.EditorSearchSettings.FollowSymlinks">
            <summary>
            Whether to follow symlinks when searching
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.RemoteFileManagerService">
            <summary>
            Manages files that are accessed from a remote PowerShell session.
            Also manages the registration and handling of the 'psedit' function.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.RemoteFileManagerService.#ctor(Microsoft.Extensions.Logging.ILoggerFactory,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceContext,Microsoft.PowerShell.EditorServices.Services.PowerShell.IInternalPowerShellExecutionService,Microsoft.PowerShell.EditorServices.Services.Extension.EditorOperationsService)">
            <summary>
            Creates a new instance of the RemoteFileManagerService class.
            </summary>
            <param name="factory">An ILoggerFactory implementation used for writing log messages.</param>
            <param name="runspaceContext">The runspace we're using.</param>
            <param name="executionService">
            The PowerShellContext to use for file loading operations.
            </param>
            <param name="editorOperations">
            The IEditorOperations instance to use for opening/closing files in the editor.
            </param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.RemoteFileManagerService.FetchRemoteFileAsync(System.String,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceInfo)">
            <summary>
            Opens a remote file, fetching its contents if necessary.
            </summary>
            <param name="remoteFilePath">
            The remote file path to be opened.
            </param>
            <param name="runspaceInfo">
            The runspace from which where the remote file will be fetched.
            </param>
            <returns>
            The local file path where the remote file's contents have been stored.
            </returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.RemoteFileManagerService.SaveRemoteFileAsync(System.String)">
            <summary>
            Saves the contents of the file under the temporary local
            file cache to its corresponding remote file.
            </summary>
            <param name="localFilePath">
            The local file whose contents will be saved.  It is assumed
            that the editor has saved the contents of the local cache
            file to disk before this method is called.
            </param>
            <returns>A Task to be awaited for completion.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.RemoteFileManagerService.CreateTemporaryFile(System.String,System.String,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceInfo)">
            <summary>
            Creates a temporary file with the given name and contents
            corresponding to the specified runspace.
            </summary>
            <param name="fileName">
            The name of the file to be created under the session path.
            </param>
            <param name="fileContents">
            The contents of the file to be created.
            </param>
            <param name="runspaceInfo">
            The runspace for which the temporary file relates.
            </param>
            <returns>The full temporary path of the file if successful, null otherwise.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.RemoteFileManagerService.GetMappedPath(System.String,Microsoft.PowerShell.EditorServices.Services.PowerShell.Runspace.IRunspaceInfo)">
            <summary>
            For a remote or local cache path, get the corresponding local or
            remote file path.
            </summary>
            <param name="filePath">
            The remote or local file path.
            </param>
            <param name="runspaceDetails">
            The runspace from which the remote file was fetched.
            </param>
            <returns>The mapped file path.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.RemoteFileManagerService.IsUnderRemoteTempPath(System.String)">
            <summary>
            Returns true if the given file path is under the remote files
            path in the temporary folder.
            </summary>
            <param name="filePath">The local file path to check.</param>
            <returns>
            True if the file path is under the temporary remote files path.
            </returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory">
            <summary>
            A FileSystem wrapper class which only returns files and directories that the consumer is interested in,
            with a maximum recursion depth and silently ignores most file system errors. Typically this is used by the
            Microsoft.Extensions.FileSystemGlobbing library.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory.MaxRecursionDepth">
            <summary>
            Gets the maximum depth of the directories that will be searched
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory.Logger">
            <summary>
            Gets the logging facility
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory.RootDirectory">
            <summary>
            Gets the directory where the factory is rooted. Only files and directories at this level, or deeper, will be visible
            by the wrapper
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory.#ctor(System.String,System.Int32,System.String[],System.Boolean,Microsoft.Extensions.Logging.ILogger)">
            <summary>
            Creates a new FileWrapper Factory
            </summary>
            <param name="rootPath">The path to the root directory for the factory.</param>
            <param name="recursionDepthLimit">The maximum directory depth.</param>
            <param name="allowedExtensions">An array of file extensions that will be visible from the factory. For example [".ps1", ".psm1"]</param>
            <param name="ignoreReparsePoints">Whether objects which are Reparse Points should be ignored. https://docs.microsoft.com/en-us/windows/desktop/fileio/reparse-points</param>
            <param name="logger">An ILogger implementation used for writing log messages.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory.CreateDirectoryInfoWrapper(System.IO.DirectoryInfo,System.Int32)">
            <summary>
            Creates a wrapped <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase" /> object from <see cref="T:System.IO.DirectoryInfo" />.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory.CreateFileInfoWrapper(System.IO.FileInfo,System.Int32)">
            <summary>
            Creates a wrapped <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoBase" /> object from <see cref="T:System.IO.FileInfo" />.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory.SafeEnumerateFileSystemInfos(System.IO.DirectoryInfo)">
            <summary>
            Enumerates all objects in the specified directory and ignores most errors
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper">
            <summary>
            Wraps an instance of <see cref="T:System.IO.DirectoryInfo" /> and provides implementation of
            <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase" />.
            Based on https://github.com/aspnet/Extensions/blob/c087cadf1dfdbd2b8785ef764e5ef58a1a7e5ed0/src/FileSystemGlobbing/src/Abstractions/DirectoryInfoWrapper.cs
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper.#ctor(Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory,System.IO.DirectoryInfo,System.Int32)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper" />.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper.EnumerateFileSystemInfos">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper.GetDirectory(System.String)">
            <summary>
            Returns an instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.DirectoryInfoBase" /> that represents a subdirectory.
            </summary>
            <remarks>
            If <paramref name="name" /> equals '..', this returns the parent directory.
            </remarks>
            <param name="name">The directory name.</param>
            <returns>The directory</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper.GetFile(System.String)">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper.Name">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper.FullName">
            <summary>
            Returns the full path to the directory.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper.SafeParentDirectory">
            <summary>
            Safely calculates the parent of this directory, swallowing most errors.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemDirectoryWrapper.ParentDirectory">
            <summary>
            Returns the parent directory. (Overrides <see cref="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.ParentDirectory" />).
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemFileInfoWrapper">
            <summary>
            Wraps an instance of <see cref="T:System.IO.FileInfo" /> to provide implementation of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoBase" />.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemFileInfoWrapper.#ctor(Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemWrapperFactory,System.IO.FileInfo,System.Int32)">
            <summary>
            Initializes instance of <see cref="T:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileInfoWrapper" /> to wrap the specified object <see cref="T:System.IO.FileInfo" />.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemFileInfoWrapper.Name">
            <summary>
            The file name. (Overrides <see cref="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.Name" />).
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemFileInfoWrapper.FullName">
            <summary>
            The full path of the file. (Overrides <see cref="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.FullName" />).
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemFileInfoWrapper.SafeParentDirectory">
            <summary>
            Safely calculates the parent of this file, swallowing most errors.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.Workspace.WorkspaceFileSystemFileInfoWrapper.ParentDirectory">
            <summary>
            The directory containing the file. (Overrides <see cref="P:Microsoft.Extensions.FileSystemGlobbing.Abstractions.FileSystemInfoBase.ParentDirectory" />).
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Services.WorkspaceService">
            <summary>
            Manages a "workspace" of script files that are open for a particular
            editing session.  Also helps to navigate references between ScriptFiles.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.InitialWorkingDirectory">
            <summary>
            <para>Gets or sets the initial working directory.</para>
            <para>
            This is settable by the same key in the initialization options, and likely corresponds
            to the root of the workspace if only one workspace folder is being used. However, in
            multi-root workspaces this may be any workspace folder's root (or none if overridden).
            </para>
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.WorkspaceFolders">
            <summary>
            Gets or sets the folders of the workspace.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.ExcludeFilesGlob">
            <summary>
            Gets or sets the default list of file globs to exclude during workspace searches.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.FollowSymlinks">
            <summary>
            Gets or sets whether the workspace should follow symlinks in search operations.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.#ctor(Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Creates a new instance of the Workspace class.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetFile(System.String)">
            <summary>
            Gets an open file in the workspace. If the file isn't open but exists on the filesystem, load and return it.
            <para>IMPORTANT: Not all documents have a backing file e.g. untitled: scheme documents.  Consider using
            <see cref="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.TryGetFile(System.String,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile@)"/> instead.</para>
            </summary>
            <param name="filePath">The file path at which the script resides.</param>
            <exception cref="T:System.IO.FileNotFoundException">
            <paramref name="filePath"/> is not found.
            </exception>
            <exception cref="T:System.ArgumentException">
            <paramref name="filePath"/> contains a null or empty string.
            </exception>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetFile(OmniSharp.Extensions.LanguageServer.Protocol.DocumentUri)">
            <summary>
            Gets an open file in the workspace. If the file isn't open but exists on the filesystem, load and return it.
            <para>IMPORTANT: Not all documents have a backing file e.g. untitled: scheme documents.  Consider using
            <see cref="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.TryGetFile(System.String,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile@)"/> instead.</para>
            </summary>
            <param name="documentUri">The document URI at which the script resides.</param>
            <exception cref="T:System.IO.FileNotFoundException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.TryGetFile(System.String,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile@)">
            <summary>
            Tries to get an open file in the workspace. Returns true if it succeeds, false otherwise.
            </summary>
            <param name="filePath">The file path at which the script resides.</param>
            <param name="scriptFile">The out parameter that will contain the ScriptFile object.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.TryGetFile(System.Uri,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile@)">
            <summary>
            Tries to get an open file in the workspace. Returns true if it succeeds, false otherwise.
            </summary>
            <param name="fileUri">The file uri at which the script resides.</param>
            <param name="scriptFile">The out parameter that will contain the ScriptFile object.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.TryGetFile(OmniSharp.Extensions.LanguageServer.Protocol.DocumentUri,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile@)">
            <summary>
            Tries to get an open file in the workspace. Returns true if it succeeds, false otherwise.
            </summary>
            <param name="documentUri">The file uri at which the script resides.</param>
            <param name="scriptFile">The out parameter that will contain the ScriptFile object.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetFileBuffer(System.String)">
            <summary>
            Gets a new ScriptFile instance which is identified by the given file path.
            </summary>
            <param name="filePath">The file path for which a buffer will be retrieved.</param>
            <returns>A ScriptFile instance if there is a buffer for the path, null otherwise.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetFileBuffer(System.String,System.String)">
            <summary>
            Gets a new ScriptFile instance which is identified by the given file
            path and initially contains the given buffer contents.
            </summary>
            <param name="filePath">The file path for which a buffer will be retrieved.</param>
            <param name="initialBuffer">The initial buffer contents if there is not an existing ScriptFile for this path.</param>
            <returns>A ScriptFile instance for the specified path.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetFileBuffer(System.Uri)">
            <summary>
            Gets a new ScriptFile instance which is identified by the given file path.
            </summary>
            <param name="fileUri">The file Uri for which a buffer will be retrieved.</param>
            <returns>A ScriptFile instance if there is a buffer for the path, null otherwise.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetFileBuffer(System.Uri,System.String)">
            <summary>
            Gets a new ScriptFile instance which is identified by the given file
            path and initially contains the given buffer contents.
            </summary>
            <param name="fileUri">The file Uri for which a buffer will be retrieved.</param>
            <param name="initialBuffer">The initial buffer contents if there is not an existing ScriptFile for this path.</param>
            <returns>A ScriptFile instance for the specified path.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetFileBuffer(OmniSharp.Extensions.LanguageServer.Protocol.DocumentUri,System.String)">
            <summary>
            Gets a new ScriptFile instance which is identified by the given file
            path and initially contains the given buffer contents.
            </summary>
            <param name="documentUri">The file Uri for which a buffer will be retrieved.</param>
            <param name="initialBuffer">The initial buffer contents if there is not an existing ScriptFile for this path.</param>
            <returns>A ScriptFile instance for the specified path.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetOpenedFiles">
            <summary>
            Gets an IEnumerable of all opened ScriptFiles in the workspace.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.CloseFile(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Closes a currently open script file with the given file path.
            </summary>
            <param name="scriptFile">The file path at which the script resides.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetRelativePath(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Gets the workspace-relative path of the given file path.
            </summary>
            <returns>A relative file path</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.FindFileInWorkspace(System.String)">
            <summary>
            Finds a file in the first workspace folder where it exists, if possible.
            Used as a backwards-compatible way to find files in the workspace.
            </summary>
            <param name="filePath"></param>
            <returns>Best possible path.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.EnumeratePSFiles">
            <summary>
            Enumerate all the PowerShell (ps1, psm1, psd1) files in the workspace in a recursive manner, using default values.
            </summary>
            <returns>An enumerator over the PowerShell files found in the workspace.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.EnumeratePSFiles(System.String[],System.String[],System.Int32,System.Boolean)">
            <summary>
            Enumerate all the PowerShell (ps1, psm1, psd1) files in the workspace folders in a
            recursive manner. Falls back to initial working directory if there are no workspace folders.
            </summary>
            <returns>An enumerator over the PowerShell files found in the workspace.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Services.WorkspaceService.GetFileKey(OmniSharp.Extensions.LanguageServer.Protocol.DocumentUri)">
            <summary>
            Returns a normalized string for a given documentUri to be used as key name.
            Case-sensitive uri on Linux and lowercase for other platforms.
            </summary>
            <param name="documentUri">A DocumentUri object to get a normalized key name from</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.CodeLenses.CodeLensData">
            <summary>
            Represents data expected back in an LSP CodeLens response.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.CodeLenses.ICodeLensProvider">
            <summary>
            Specifies the contract for a Code Lens provider.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.CodeLenses.ICodeLensProvider.ProviderId">
            <summary>
            Specifies a unique identifier for the feature provider, typically a
            fully-qualified name like "Microsoft.PowerShell.EditorServices.MyProvider"
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.ICodeLensProvider.ProvideCodeLenses(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Provides a collection of CodeLenses for the given
            document.
            </summary>
            <param name="scriptFile">
            The document for which CodeLenses should be provided.
            </param>
            <returns>An IEnumerable of CodeLenses.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.ICodeLensProvider.ResolveCodeLens(OmniSharp.Extensions.LanguageServer.Protocol.Models.CodeLens,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Threading.CancellationToken)">
            <summary>
            Resolves a CodeLens that was created without a Command.
            </summary>
            <param name="codeLens">
            The CodeLens to resolve.
            </param>
            <param name="scriptFile">
            The ScriptFile to resolve it in (sometimes unused).
            </param>
            <param name="cancellationToken"></param>
            <returns>
            A Task which returns the resolved CodeLens when completed.
            </returns>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.CodeLenses.PesterCodeLensProvider._symbolProvider">
            <summary>
            The symbol provider to get symbols from to build code lenses with.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.CodeLenses.PesterCodeLensProvider.ProviderId">
            <summary>
            Specifies a unique identifier for the feature provider, typically a
            fully-qualified name like "Microsoft.PowerShell.EditorServices.MyProvider"
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.PesterCodeLensProvider.#ctor(Microsoft.PowerShell.EditorServices.Services.ConfigurationService)">
            <summary>
            Create a new Pester CodeLens provider for a given editor session.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.PesterCodeLensProvider.GetPesterLens(Microsoft.PowerShell.EditorServices.Services.Symbols.PesterSymbolReference,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Get the Pester CodeLenses for a given Pester symbol.
            </summary>
            <param name="pesterSymbol">The Pester symbol to get CodeLenses for.</param>
            <param name="scriptFile">The script file the Pester symbol comes from.</param>
            <returns>All CodeLenses for the given Pester symbol.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.PesterCodeLensProvider.ProvideCodeLenses(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Get all Pester CodeLenses for a given script file.
            </summary>
            <param name="scriptFile">The script file to get Pester CodeLenses for.</param>
            <returns>All Pester CodeLenses for the given script file.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.PesterCodeLensProvider.ResolveCodeLens(OmniSharp.Extensions.LanguageServer.Protocol.Models.CodeLens,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Threading.CancellationToken)">
            <summary>
            Resolve the CodeLens provision asynchronously -- just wraps the CodeLens argument in a task.
            </summary>
            <param name="codeLens">The code lens to resolve.</param>
            <param name="scriptFile">The script file.</param>
            <param name="cancellationToken"></param>
            <returns>The given CodeLens, wrapped in a task.</returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.CodeLenses.ReferencesCodeLensProvider">
            <summary>
            Provides the "reference" code lens by extracting document symbols.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.CodeLenses.ReferencesCodeLensProvider._symbolProvider">
            <summary>
            The document symbol provider to supply symbols to generate the code lenses.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.CodeLenses.ReferencesCodeLensProvider.ProviderId">
            <summary>
            Specifies a unique identifier for the feature provider, typically a
            fully-qualified name like "Microsoft.PowerShell.EditorServices.MyProvider"
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.ReferencesCodeLensProvider.#ctor(Microsoft.PowerShell.EditorServices.Services.WorkspaceService,Microsoft.PowerShell.EditorServices.Services.SymbolsService)">
            <summary>
            Construct a new ReferencesCodeLensProvider for a given EditorSession.
            </summary>
            <param name="workspaceService"></param>
            <param name="symbolsService"></param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.ReferencesCodeLensProvider.ProvideCodeLenses(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Get all reference code lenses for a given script file.
            </summary>
            <param name="scriptFile">The PowerShell script file to get code lenses for.</param>
            <returns>An IEnumerable of CodeLenses describing all functions, classes and enums in the given script file.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.ReferencesCodeLensProvider.ResolveCodeLens(OmniSharp.Extensions.LanguageServer.Protocol.Models.CodeLens,Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Threading.CancellationToken)">
            <summary>
            Take a CodeLens and create a new CodeLens object with updated references.
            </summary>
            <param name="codeLens">The old code lens to get updated references for.</param>
            <param name="scriptFile"></param>
            <param name="cancellationToken"></param>
            <returns>A new CodeLens object describing the same data as the old one but with updated references.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.CodeLenses.ReferencesCodeLensProvider.GetReferenceCountHeader(System.Int32)">
            <summary>
            Get the code lens header for the number of references on a definition,
            given the number of references.
            </summary>
            <param name="referenceCount">The number of references found for a given definition.</param>
            <returns>The header string for the reference code lens.</returns>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.Script">
            <summary>
            Gets or sets the absolute path to the script to debug.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.StopOnEntry">
            <summary>
            Gets or sets a boolean value that determines whether to automatically stop
            target after launch. If not specified, target does not stop.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.Args">
            <summary>
            Gets or sets optional arguments passed to the debuggee.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.Cwd">
            <summary>
            Gets or sets the working directory of the launched debuggee (specified as an absolute path).
            If omitted the debuggee is launched in its own directory.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.CreateTemporaryIntegratedConsole">
            <summary>
            Gets or sets a boolean value that determines whether to create a temporary
            Extension Terminal for the debug session. Default is false.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.RuntimeExecutable">
            <summary>
            Gets or sets the absolute path to the runtime executable to be used.
            Default is the runtime executable on the PATH.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.RuntimeArgs">
            <summary>
            Gets or sets the optional arguments passed to the runtime executable.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.ExecuteMode">
            <summary>
            Gets or sets the script execution mode, either "DotSource" or "Call".
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.PsesLaunchRequestArguments.Env">
            <summary>
            Gets or sets optional environment variables to pass to the debuggee. The string valued
            properties of the 'environmentVariables' are used as key/value pairs.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Handlers.ScopesHandler.Handle(OmniSharp.Extensions.DebugAdapter.Protocol.Requests.ScopesArguments,System.Threading.CancellationToken)">
            <summary>
            Retrieves the variable scopes (containers) for the currently selected stack frame. Variables details are fetched via a separate request.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Handlers.StackTraceHandler.INITIAL_PAGE_SIZE">
            <summary>
            Because we don't know the size of the stacktrace beforehand, we will tell the client that there are more frames available, this is effectively a paging size, as the client should request this many frames after the first one.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Handlers.EvaluateHandler">
            <summary>
            Handler for a custom request type for evaluating PowerShell.
            This is generally for F8 support, to allow execution of a highlighted code snippet in the console as if it were copy-pasted.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Handlers.PSCommandMessage">
            <summary>
            Describes the message to get the details for a single PowerShell Command
            from the current session
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.EvaluateRequestArguments.Expression">
            <summary>
            The expression to evaluate.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.EvaluateRequestArguments.Context">
            <summary>
            The context in which the evaluate request is run. Possible
            values are 'watch' if evaluate is run in a watch or 'repl'
            if run from the REPL console.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.EvaluateRequestArguments.FrameId">
            <summary>
            Evaluate the expression in the context of this stack frame.
            If not specified, the top most frame is used.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.EvaluateResponseBody.Result">
            <summary>
            The evaluation result.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Handlers.EvaluateResponseBody.VariablesReference">
            <summary>
            If variablesReference is > 0, the evaluate result is
            structured and its children can be retrieved by passing
            variablesReference to the VariablesRequest
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Handlers.PsesCodeLensHandlers.ProvideCodeLenses(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile)">
            <summary>
            Get all the CodeLenses for a given script file.
            </summary>
            <param name="scriptFile">The PowerShell script file to get CodeLenses for.</param>
            <returns>All generated CodeLenses for the given script file.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Handlers.PsesCompletionHandler.GetCompletionsInFileAsync(Microsoft.PowerShell.EditorServices.Services.TextDocument.ScriptFile,System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets completions for a statement contained in the given
            script file at the specified line and column position.
            </summary>
            <param name="scriptFile">
            The script file in which completions will be gathered.
            </param>
            <param name="lineNumber">
            The 1-based line number at which completions will be gathered.
            </param>
            <param name="columnNumber">
            The 1-based column number at which completions will be gathered.
            </param>
            <param name="cancellationToken">The token used to cancel this.</param>
            <returns>
            A CommandCompletion instance completions for the identified statement.
            </returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Handlers.PsesCompletionHandler.TryExtractType(System.String,System.String,System.String@,System.String@)">
            <summary>
            Look for type encoded in the tooltip for parameters and variables. Display PowerShell
            type names in [] to be consistent with PowerShell syntax and how the debugger displays
            type names.
            </summary>
            <param name="toolTipText">The tooltip text to parse</param>
            <param name="type">The extracted type string, if found</param>
            <param name="documentation">The remaining text after the type, if any</param>
            <param name="label">The label to check for in the documentation prefix</param>
            <returns>Whether or not the type was found.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Handlers.PsesCompletionHandler.TryBuildSnippet(System.String,System.String@)">
            <summary>
            Insert a final "tab stop" as identified by $0 in the snippet provided for completion.
            For folder paths, we take the path returned by PowerShell e.g. 'C:\Program Files' and
            insert the tab stop marker before the closing quote char e.g. 'C:\Program Files$0'. This
            causes the editing cursor to be placed *before* the final quote after completion, which
            makes subsequent path completions work. See this part of the LSP spec for details:
            https://microsoft.github.io/language-server-protocol/specification#textDocument_completion
            </summary>
            <param name="completionText"></param>
            <param name="snippet"></param>
            <returns>
            Whether or not the completion ended with a quote and so was a snippet.
            </returns>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Handlers.DidChangeWatchedFilesHandler">
            <summary>
            Receives file change notifications from the client for any file in the workspace, including those
            that are not considered opened by the client. This handler is used to allow us to scan the
            workspace only once when the language server starts.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Utility.AsyncUtils">
            <summary>
            Provides utility methods for common asynchronous operations.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.AsyncUtils.CreateSimpleLockingSemaphore">
            <summary>
            Creates a <see cref="T:System.Threading.SemaphoreSlim" /> with an handle initial and
            max count of one.
            </summary>
            <returns>A simple single handle <see cref="T:System.Threading.SemaphoreSlim" />.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.ObjectExtensions.SafeToString(System.Object)">
            <summary>
            Extension to evaluate an object's ToString() method in an exception safe way. This will
            extension method will not throw.
            </summary>
            <param name="obj">The object on which to call ToString()</param>
            <returns>The ToString() return value or a suitable error message is that throws.</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.ObjectExtensions.AppendLineLF(System.Text.StringBuilder)">
            <summary>
            Same as <see cref="M:System.Text.StringBuilder.AppendLine" /> but never CRLF. Use this when building
            formatting for clients that may not render CRLF correctly.
            </summary>
            <param name="self"></param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.ObjectExtensions.AppendLineLF(System.Text.StringBuilder,System.String)">
            <summary>
            Same as <see cref="M:System.Text.StringBuilder.AppendLine(System.String)" /> but never CRLF. Use this when building
            formatting for clients that may not render CRLF correctly.
            </summary>
            <param name="self"></param>
            <param name="value"></param>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Utility.FormatUtils.s_whiteSpace">
            <summary>
            Space, new line, carriage return and tab.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Utility.FormatUtils.s_commaSquareBracketOrDot">
            <summary>
            A period, comma, and both open and square brackets.
            </summary>
        </member>
        <member name="F:Microsoft.PowerShell.EditorServices.Utility.PathUtils.PathComparison">
            <summary>
            The <see cref="T:System.StringComparison" /> value to be used when comparing paths. Will be
            <see cref="F:System.StringComparison.Ordinal" /> for case sensitive file systems and <see cref="F:System.StringComparison.OrdinalIgnoreCase" />
            in case insensitive file systems.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.PathUtils.IsPathEqual(System.String,System.String)">
            <summary>
            Determines whether two specified strings represent the same path.
            </summary>
            <param name="left">The first path to compare, or <see langword="null" />.</param>
            <param name="right">The second path to compare, or <see langword="null" />.</param>
            <returns>
            <see langword="true" /> if the value of <paramref name="left" /> represents the same
            path as the value of <paramref name="right" />; otherwise, <see langword="false" />.
            </returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.PathUtils.WildcardEscapePath(System.String,System.Boolean)">
            <summary>
            Return the given path with all PowerShell globbing characters escaped,
            plus optionally the whitespace.
            </summary>
            <param name="path">The path to process.</param>
            <param name="escapeSpaces">Specify True to escape spaces in the path, otherwise False.</param>
            <returns>The path with *, ?, [, and ] escaped, including spaces if required</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.PSCommandHelpers.GetInvocationText(System.Management.Automation.PSCommand)">
            <summary>
            Get a representation of the PSCommand, for logging purposes.
            </summary>
            <param name="command"></param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Utility.Validate">
            <summary>
            Provides common validation methods to simplify method
            parameter checks.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.Validate.IsNotNull(System.String,System.Object)">
            <summary>
            Throws ArgumentNullException if value is null.
            </summary>
            <param name="parameterName">The name of the parameter being validated.</param>
            <param name="valueToCheck">The value of the parameter being validated.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.Validate.IsWithinRange(System.String,System.Int32,System.Int32,System.Int32)">
            <summary>
            Throws ArgumentOutOfRangeException if the value is outside
            of the given lower and upper limits.
            </summary>
            <param name="parameterName">The name of the parameter being validated.</param>
            <param name="valueToCheck">The value of the parameter being validated.</param>
            <param name="lowerLimit">The lower limit which the value should not be less than.</param>
            <param name="upperLimit">The upper limit which the value should not be greater than.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.Validate.IsLessThan(System.String,System.Int32,System.Int32)">
            <summary>
            Throws ArgumentOutOfRangeException if the value is greater than or equal
            to the given upper limit.
            </summary>
            <param name="parameterName">The name of the parameter being validated.</param>
            <param name="valueToCheck">The value of the parameter being validated.</param>
            <param name="upperLimit">The upper limit which the value should be less than.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.Validate.IsGreaterThan(System.String,System.Int32,System.Int32)">
            <summary>
            Throws ArgumentOutOfRangeException if the value is less than or equal
            to the given lower limit.
            </summary>
            <param name="parameterName">The name of the parameter being validated.</param>
            <param name="valueToCheck">The value of the parameter being validated.</param>
            <param name="lowerLimit">The lower limit which the value should be greater than.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.Validate.IsNotEqual``1(System.String,``0,``0)">
            <summary>
            Throws ArgumentException if the value is equal to the undesired value.
            </summary>
            <typeparam name="TValue">The type of value to be validated.</typeparam>
            <param name="parameterName">The name of the parameter being validated.</param>
            <param name="valueToCheck">The value of the parameter being validated.</param>
            <param name="undesiredValue">The value that valueToCheck should not equal.</param>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.Validate.IsNotNullOrEmptyString(System.String,System.String)">
            <summary>
            Throws ArgumentException if the value is null, an empty string,
            or a string containing only whitespace.
            </summary>
            <param name="parameterName">The name of the parameter being validated.</param>
            <param name="valueToCheck">The value of the parameter being validated.</param>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Utility.VersionUtils">
            <summary>
            General purpose common utilities to prevent reimplementation.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.IsNetCore">
            <summary>
            True if we are running on .NET Core, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.PSVersion">
            <summary>
            Gets the Version of PowerShell being used.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.PSEdition">
            <summary>
            Gets the Edition of PowerShell being used.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.GitCommitId">
            <summary>
            Gets the GitCommitId of PowerShell being used.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.PSVersionString">
            <summary>
            Gets the string of the PSVersion including prerelease tags if it applies.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.IsPS5">
            <summary>
            True if we are running in Windows PowerShell, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.IsPS7OrGreater">
            <summary>
            True if we are running in PowerShell 7 or greater, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.IsPS74">
            <summary>
            True if we are running in PowerShell 7.4, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.IsWindows">
            <summary>
            True if we are running on Windows, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.IsMacOS">
            <summary>
            True if we are running on macOS, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.IsLinux">
            <summary>
            True if we are running on Linux, false otherwise.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.VersionUtils.Architecture">
            <summary>
            The .NET Architecture as a string.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.PowerShellReflectionUtils.PSVersion">
            <summary>
            Gets the Version of PowerShell being used. NOTE: this will get rid of the SemVer 2.0 suffix because apparently
            that property is added as a note property and it is not there when we reflect.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.PowerShellReflectionUtils.PSEdition">
            <summary>
            Get's the Edition of PowerShell being used.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.PowerShellReflectionUtils.GitCommitId">
            <summary>
            Gets the GitCommitId or at most x.y.z from the PSVersion, making Windows PowerShell conform to SemVer.
            </summary>
        </member>
        <member name="P:Microsoft.PowerShell.EditorServices.Utility.PowerShellReflectionUtils.PSVersionString">
            <summary>
            Gets the stringified version of PowerShell including prerelease tags if it applies.
            </summary>
        </member>
        <member name="T:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils">
            <summary>
            General common utilities for AST visitors to prevent reimplementation.
            </summary>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameStartColumnAndLineFromAst(System.Management.Automation.Language.Ast,System.Int32)">
            <summary>
            Calculates the start line and column of the actual symbol name in a AST.
            </summary>
            <param name="ast">An Ast object in the script's AST</param>
            <param name="nameStartIndex">An int specifying start index of name in the AST's extent text</param>
            <returns>A tuple with start column and line of the symbol name</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameStartColumnAndLineFromAst(System.Management.Automation.Language.FunctionDefinitionAst)">
            <summary>
            Calculates the start line and column of the actual function name in a function definition AST.
            </summary>
            <param name="functionDefinitionAst">A FunctionDefinitionAst object in the script's AST</param>
            <returns>A tuple with start column and line for the function name</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameStartColumnAndLineFromAst(System.Management.Automation.Language.TypeDefinitionAst)">
            <summary>
            Calculates the start line and column of the actual class/enum name in a type definition AST.
            </summary>
            <param name="typeDefinitionAst">A TypeDefinitionAst object in the script's AST</param>
            <returns>A tuple with start column and line for the type name</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameStartColumnAndLineFromAst(System.Management.Automation.Language.FunctionMemberAst)">
            <summary>
            Calculates the start line and column of the actual method/constructor name in a function member AST.
            </summary>
            <param name="functionMemberAst">A FunctionMemberAst object in the script's AST</param>
            <returns>A tuple with start column and line for the method/constructor name</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameStartColumnAndLineFromAst(System.Management.Automation.Language.PropertyMemberAst,System.Boolean)">
            <summary>
            Calculates the start line and column of the actual property name in a property member AST.
            </summary>
            <param name="propertyMemberAst">A PropertyMemberAst object in the script's AST</param>
            <param name="isEnumMember">A bool indicating this is a enum member</param>
            <returns>A tuple with start column and line for the property name</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameStartColumnAndLineFromAst(System.Management.Automation.Language.ConfigurationDefinitionAst)">
            <summary>
            Calculates the start line and column of the actual configuration name in a configuration definition AST.
            </summary>
            <param name="configurationDefinitionAst">A ConfigurationDefinitionAst object in the script's AST</param>
            <returns>A tuple with start column and line for the configuration name</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameExtent(System.Management.Automation.Language.FunctionDefinitionAst)">
            <summary>
            Gets a new ScriptExtent for a given Ast for the symbol name only (variable)
            </summary>
            <param name="functionDefinitionAst">A FunctionDefinitionAst in the script's AST</param>
            <returns>A ScriptExtent with for the symbol name only</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameExtent(System.Management.Automation.Language.TypeDefinitionAst)">
            <summary>
            Gets a new ScriptExtent for a given Ast for the symbol name only (variable)
            </summary>
            <param name="typeDefinitionAst">A TypeDefinitionAst in the script's AST</param>
            <returns>A ScriptExtent with for the symbol name only</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameExtent(System.Management.Automation.Language.FunctionMemberAst)">
            <summary>
            Gets a new ScriptExtent for a given Ast for the symbol name only (variable)
            </summary>
            <param name="functionMemberAst">A FunctionMemberAst in the script's AST</param>
            <returns>A ScriptExtent with for the symbol name only</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameExtent(System.Management.Automation.Language.PropertyMemberAst)">
            <summary>
            Gets a new ScriptExtent for a given Ast for the property name only
            </summary>
            <param name="propertyMemberAst">A PropertyMemberAst in the script's AST</param>
            <returns>A ScriptExtent with for the symbol name only</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetNameExtent(System.Management.Automation.Language.ConfigurationDefinitionAst)">
            <summary>
            Gets a new ScriptExtent for a given Ast for the configuration instance name only
            </summary>
            <param name="configurationDefinitionAst">A ConfigurationDefinitionAst in the script's AST</param>
            <returns>A ScriptExtent with for the symbol name only</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetFunctionDisplayName(System.Management.Automation.Language.FunctionDefinitionAst)">
            <summary>
            Gets the function name with parameters and return type.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetParamDisplayName(System.Management.Automation.Language.ParameterAst)" -->
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetMemberOverloadName(System.Management.Automation.Language.FunctionMemberAst)">
            <summary>
            Gets the method or constructor name with parameters for current overload.
            </summary>
            <param name="functionMemberAst">A FunctionMemberAst object in the script's AST</param>
            <returns>Function member name with return type (optional) and parameters</returns>
        </member>
        <member name="M:Microsoft.PowerShell.EditorServices.Utility.VisitorUtils.GetMemberOverloadName(System.Management.Automation.Language.PropertyMemberAst)">
            <summary>
            Gets the property name with type and class/enum.
            </summary>
            <param name="propertyMemberAst">A PropertyMemberAst object in the script's AST</param>
            <returns>Property name with type (optional) and class/enum</returns>
        </member>
        <member name="T:System.Runtime.CompilerServices.IsExternalInit">
            <summary>
            Reserved to be used by the compiler for tracking metadata.
            This class should not be used by developers in source code.
            </summary>
        </member>
    </members>
</doc>
