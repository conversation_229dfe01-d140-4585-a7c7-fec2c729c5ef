var rt=Object.defineProperty;var at=(c,t,e)=>t in c?rt(c,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):c[t]=e;var l=(c,t,e)=>at(c,typeof t!="symbol"?t+"":t,e);import{A as ot,ap as W,S as K,i as Q,s as Z,a as T,n as F,d as R,b as G,g as gt,u as ht,v as ct,w as dt,x as lt,f as ut,H as _t,j as z,t as b,q as S,o as pt,p as mt,c as tt,N as ft,I as vt,y as At,al as St,D as et,h as B,E as st,F as wt,G as nt,J as yt,K as bt,L as Ct,M as Et}from"./SpinnerAugment-VfHtkDdv.js";import{g as It}from"./chat-context-DhGlDJgc.js";import{b as J,A as x,s as y,c as C,R as m,a as M}from"./index-B528snJk.js";import{W as E}from"./IconButtonAugment-BlRCK7lJ.js";import{a as j}from"./message-broker-DxXjuHCW.js";import{E as Rt,b as w,c as V,S as xt,d as f,e as Mt}from"./index-C5qylk65.js";import{t as Ot}from"./index-6WVCg-U8.js";import{S as I,i as Tt,a as Ft,R as Pt,b as Dt}from"./remote-agents-client-zf3VV9pT.js";import{R as Lt}from"./ra-diff-ops-model-DMR40nRt.js";import{S as Ut}from"./TextAreaAugment-BnS2cUNC.js";function kt(c,t){if(c.length===0)return t;if(t.length===0)return c;const e=[];let s=0,n=0;for(;s<c.length&&n<t.length;){const i=c[s].sequence_id,r=t[n].sequence_id;r!==void 0?i!==void 0?i<r?(e.push(c[s]),s++):i>r?(e.push(t[n]),n++):(e.push(t[n]),s++,n++):(console.warn("Existing history has an exchange with an undefined sequence ID"),s++):(console.warn("New history has an exchange with an undefined sequence ID"),n++)}for(;s<c.length;)e.push(c[s]),s++;for(;n<t.length;)e.push(t[n]),n++;return e}class Ht{constructor(t){l(this,"_pollingTimers",new Map);l(this,"_pollingInterval");l(this,"_failedAttempts",0);l(this,"_lastSuccessfulFetch",0);this._config=t,this._pollingInterval=t.defaultInterval}start(t){t&&this._pollingTimers.has(t)?this.stop(t):!t&&this._pollingTimers.has("global")&&this.stop("global"),this.refresh(t);const e=setInterval(()=>{this.refresh(t)},this._pollingInterval);t?this._pollingTimers.set(t,e):this._pollingTimers.set("global",e)}stop(t){if(t){const e=this._pollingTimers.get(t);e&&(clearInterval(e),this._pollingTimers.delete(t))}else for(const[e,s]of this._pollingTimers.entries())clearInterval(s),this._pollingTimers.delete(e)}async refresh(t){try{const e=await this._config.refreshFn(t);return this._failedAttempts=0,this._lastSuccessfulFetch=Date.now(),this._pollingInterval=this._config.defaultInterval,this._config.stopCondition&&t&&this._config.stopCondition(e,t)&&this.stop(t),e}catch{return this._failedAttempts++,this._failedAttempts>3?this._pollingInterval=1e4:this._pollingInterval=Math.min(1e3*Math.pow(2,this._failedAttempts),1e4),null}}isPolling(t){return t?this._pollingTimers.has(t):this._pollingTimers.size>0}get timeSinceLastSuccessfulFetch(){return Date.now()-this._lastSuccessfulFetch}get failedAttempts(){return this._failedAttempts}resetFailedAttempts(){this._failedAttempts=0}}class $t{constructor(t,e){l(this,"_state",{agentOverviews:[],agentConversations:new Map,agentLogs:new Map,maxRemoteAgents:0,maxActiveRemoteAgents:0,overviewError:void 0,conversationError:void 0,logsError:void 0,isOverviewsLoading:!1,isConversationLoading:!1,isLogsLoading:!1,logPollFailedCount:0});l(this,"_loggingMaxRetries",8);l(this,"_logsPollingManager");l(this,"_isInitialOverviewFetch",!0);l(this,"_lastOverviewUpdateTimestamp");l(this,"_stateUpdateSubscribers",new Set);l(this,"_pendingStateUpdateOpts");this._flagsModel=t,this._remoteAgentsClient=e,this._logsPollingManager=new Ht({defaultInterval:1e3,refreshFn:async s=>{if(!s)throw new Error("Agent ID is required for logs polling");return this.refreshAgentLogs(s)},stopCondition:(s,n)=>{if(!n)return!0;if(!this._state.agentOverviews.find(a=>a.remote_agent_id===n))return this._state.logPollFailedCount++,this._state.logPollFailedCount>this._loggingMaxRetries&&(this._state.logPollFailedCount=0,!0);const i=this.state.agentLogs.get(n),r=i==null?void 0:i.steps.at(-1);return(r==null?void 0:r.step_description)==="Indexing"&&r.status===J.success}}),this._flagsModel.subscribe(s=>{const n=this._remoteAgentsClient.hasActiveOverviewsStream()||this._remoteAgentsClient.activeHistoryStreams.size>0||this._logsPollingManager.isPolling(),i=s.enableBackgroundAgents;i&&!n?this.startStateUpdates(this._pendingStateUpdateOpts):!i&&n&&this.stopStateUpdates()})}get state(){return this._state}startStateUpdates(t){var e,s;if(!this._flagsModel.enableBackgroundAgents)return this._pendingStateUpdateOpts={...this._pendingStateUpdateOpts,...t},void(t||(this._pendingStateUpdateOpts.overviews=!0));t?(t.overviews&&this.startOverviewsStream(),(e=t.conversation)!=null&&e.agentId&&this.startConversationStream(t.conversation.agentId),(s=t.logs)!=null&&s.agentId&&(this._state.logPollFailedCount=0,this._logsPollingManager.start(t.logs.agentId))):this.startOverviewsStream()}_removePendingStateUpdates(t){var e,s,n,i;this._pendingStateUpdateOpts&&(t?(t.overviews&&(this._pendingStateUpdateOpts.overviews=!1),((e=t.conversation)==null?void 0:e.agentId)===((s=this._pendingStateUpdateOpts.conversation)==null?void 0:s.agentId)&&(this._pendingStateUpdateOpts.conversation=void 0),((n=t.logs)==null?void 0:n.agentId)===((i=this._pendingStateUpdateOpts.logs)==null?void 0:i.agentId)&&(this._pendingStateUpdateOpts.logs=void 0)):this._pendingStateUpdateOpts=void 0)}stopStateUpdates(t){var e,s;if(this._removePendingStateUpdates(t),!t)return this.stopOverviewsStream(),this._logsPollingManager.stop(),void this.stopAllConversationStreams();t.overviews&&this.stopOverviewsStream(),(e=t.conversation)!=null&&e.agentId&&this.stopConversationStream(t.conversation.agentId),(s=t.logs)!=null&&s.agentId&&this._logsPollingManager.stop(t.logs.agentId)}async refreshCurrentAgent(t){this.startConversationStream(t)}async refreshAgentOverviews(){return this.startOverviewsStream(),this._state.agentOverviews}async refreshAgentLogs(t){try{const e=this.state.agentLogs.get(t);let s,n;const i=e==null?void 0:e.steps.at(-1);i?(s=i.step_number,n=i.step_number===0?0:i.sequence_id+1):(s=0,n=0);const r=await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(t,s,n);if(!r.data.workspaceSetupStatus)return;const a=r.data.workspaceSetupStatus;if(a.steps.length===0)return e;const o=function(h,u){return{steps:[...h.steps,...u.steps].sort((_,p)=>_.step_number!==p.step_number?_.step_number-p.step_number:_.sequence_id-p.sequence_id)}}(e??{steps:[]},a),g={steps:o.steps.reduce((h,u)=>{const _=h[h.length-1];return _&&_.step_number===u.step_number?(_.status!==J.success&&(_.status=u.status),_.step_number===0?_.logs=u.logs:_.sequence_id<u.sequence_id&&(_.logs+=`
${u.logs}`,_.sequence_id=u.sequence_id)):h.push(u),h},[])};return this._state.agentLogs.set(t,g),this._state.logsError=void 0,this.notifySubscribers({type:"logs",agentId:t,data:g}),g}catch(e){const s=e instanceof Error?e.message:String(e);return this._state.logsError={errorMessage:s},this.notifySubscribers({type:"logs",agentId:t,data:this._state.agentLogs.get(t)||{steps:[]},error:this._state.logsError}),this._state.agentLogs.get(t)}}onStateUpdate(t){return this._stateUpdateSubscribers.add(t),t({type:"all",data:this._state}),()=>{this._stateUpdateSubscribers.delete(t)}}dispose(){this.stopStateUpdates(),this._stateUpdateSubscribers.clear()}notifySubscribers(t){this._stateUpdateSubscribers.forEach(e=>e(t))}async startConversationStream(t){this._remoteAgentsClient.hasActiveHistoryStream(t)&&this.stopConversationStream(t);const e=this._state.agentConversations.get(t)||[];let s=0;e.length>0&&(s=Math.max(...e.filter(n=>n.sequence_id!==void 0).map(n=>n.sequence_id||0))-1,s<0&&(s=0)),this._state.isConversationLoading=!0,this._state.conversationError=void 0,this.notifySubscribers({type:"conversation",agentId:t,data:e,error:this._state.conversationError});try{const n=this._remoteAgentsClient.startRemoteAgentHistoryStreamWithRetry(t,s);(async()=>{var i;try{for await(const r of n){if(!this._remoteAgentsClient.hasActiveHistoryStream(t)||(i=this._remoteAgentsClient.getActiveHistoryStream(t))!=null&&i.isCancelled)break;this.processHistoryStreamUpdate(t,r)}}catch(r){if(this._remoteAgentsClient.hasActiveHistoryStream(t)){let a;r instanceof I?(a=`Failed to connect: ${r.message}`,console.error(`Stream retry exhausted for agent ${t}: ${r.message}`)):(a=r instanceof Error?r.message:String(r),console.error(`Stream error for agent ${t}: ${a}`)),this._state.conversationError={errorMessage:a},this._state.isConversationLoading=!1;const o=this._state.agentConversations.get(t)||[];this.notifySubscribers({type:"conversation",agentId:t,data:o,error:this._state.conversationError})}}finally{this._state.isConversationLoading=!1}})()}catch(n){let i;i=n instanceof I?`Failed to connect: ${n.message}`:n instanceof Error?n.message:String(n),this._state.conversationError={errorMessage:i},this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:t,data:e,error:this._state.conversationError})}}stopConversationStream(t){this._remoteAgentsClient.cancelRemoteAgentHistoryStream(t)}stopAllConversationStreams(){this._remoteAgentsClient.cancelAllRemoteAgentHistoryStreams()}processHistoryStreamUpdate(t,e){var s;if(Tt(e)){this._state.conversationError=void 0;for(const n of e.updates){const i=this._state.agentConversations.get(t)||[];switch(n.type){case x.AGENT_HISTORY_EXCHANGE:if(n.exchange){const r=kt(i,[n.exchange]);this._state.agentConversations.set(t,r)}break;case x.AGENT_HISTORY_EXCHANGE_UPDATE:if(n.exchange_update){const r=n.exchange_update.sequence_id,a=i.findIndex(o=>o.sequence_id===r);if(a>=0){const o=i[a],g=((s=o.exchange)==null?void 0:s.response_text)||"";o.exchange.response_text=g+n.exchange_update.appended_text;const h=n.exchange_update.appended_nodes;if(h&&h.length>0){const _=o.exchange.response_nodes??[];o.exchange.response_nodes=[..._,...h]}const u=n.exchange_update.appended_changed_files;if(u&&u.length>0){const _=o.changed_files??[];o.changed_files=[..._,...u]}}}break;case x.AGENT_HISTORY_AGENT_STATUS:if(n.agent){const r=this._state.agentOverviews.findIndex(a=>a.remote_agent_id===t);r>=0?this._state.agentOverviews[r]=n.agent:(this._state.agentOverviews.push(n.agent),this._state.agentOverviews=y(this._state.agentOverviews)),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}}this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:t,data:this._state.agentConversations.get(t)||[],error:this._state.conversationError})}else{this.state.conversationError=e;const n=this._state.agentConversations.get(t)||[];this.notifySubscribers({type:"conversation",agentId:t,data:n,error:this._state.conversationError})}}async startOverviewsStream(){this._state.isOverviewsLoading=!0,this._state.overviewError=void 0,this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError});try{const t=this._remoteAgentsClient.startRemoteAgentsListStreamWithRetry(this._lastOverviewUpdateTimestamp);(async()=>{try{for await(const e of t){if(!this._remoteAgentsClient.hasActiveOverviewsStream())break;this.processOverviewsStreamUpdate(e)}}catch(e){if(this._remoteAgentsClient.hasActiveOverviewsStream()){let s;e instanceof I?(s=`Failed to connect: ${e.message}`,console.error(`Overview stream retry exhausted: ${e.message}`)):(s=e instanceof Error?e.message:String(e),console.error(`Overview stream error: ${s}`)),this._state.overviewError={errorMessage:s},this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}finally{this._state.isOverviewsLoading=!1}})()}catch(t){let e;e=t instanceof I?`Failed to connect: ${t.message}`:t instanceof Error?t.message:String(t),this._state.overviewError={errorMessage:e},this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}stopOverviewsStream(){this._remoteAgentsClient.cancelRemoteAgentOverviewsStream()}processOverviewsStreamUpdate(t){if(!Ft(t))return this._state.overviewError=t,void this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError});this._state.overviewError=void 0;for(const e of t.updates)switch(e.update_timestamp&&(this._lastOverviewUpdateTimestamp=e.update_timestamp),e.max_agents!==void 0&&(this._state.maxRemoteAgents=e.max_agents),e.max_active_agents!==void 0&&(this._state.maxActiveRemoteAgents=e.max_active_agents),e.type){case C.AGENT_LIST_ALL_AGENTS:e.all_agents&&(this._state.agentOverviews=y(e.all_agents));break;case C.AGENT_LIST_AGENT_ADDED:e.agent&&(this._state.agentOverviews.push(e.agent),this._state.agentOverviews=y(this._state.agentOverviews));break;case C.AGENT_LIST_AGENT_UPDATED:if(e.agent){const s=this._state.agentOverviews.findIndex(n=>n.remote_agent_id===e.agent.remote_agent_id);s>=0&&(this._state.agentOverviews[s]=e.agent,this._state.agentOverviews=y(this._state.agentOverviews))}break;case C.AGENT_LIST_AGENT_DELETED:e.deleted_agent_id&&(this._state.agentOverviews=this._state.agentOverviews.filter(s=>s.remote_agent_id!==e.deleted_agent_id))}this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:structuredClone(this._state.agentOverviews),error:this._state.overviewError})}}var v=(c=>(c.chatRequestFailed="chat_request_failed",c.messageTimeout="message_timeout",c.agentFailed="agent_failed",c))(v||{});const O="This agent is in a failed state and can no longer accept messages",re=!1;class it{constructor({msgBroker:t,isActive:e,flagsModel:s,host:n,gitRefModel:i,stateModel:r}){l(this,"_state",{isActive:!1,isPanelFocused:!1,currentAgentId:void 0,currentConversation:void 0,currentAgent:void 0,agentOverviews:[],chatConversations:[],localAgentConversations:[],isLoading:!1,isCurrentAgentDetailsLoading:!1,lastSuccessfulOverviewFetch:0,failedRefreshAttempts:0,maxRemoteAgents:0,maxActiveRemoteAgents:0,isDiffPanelOpen:!1,diffPanelAgentId:void 0,focusedFilePath:null,isCreatingAgent:!1,error:void 0,agentThreadsError:void 0,agentLogsError:void 0,agentChatHistoryError:void 0,remoteAgentCreationError:null,newAgentDraft:null,notificationSettings:{},pinnedAgents:{},setCurrentAgent:this.setCurrentAgent.bind(this),clearCurrentAgent:this.clearCurrentAgent.bind(this),sendMessage:this.sendMessage.bind(this),interruptAgent:this.interruptAgent.bind(this),createRemoteAgent:this.createRemoteAgent.bind(this),createRemoteAgentFromDraft:this.createRemoteAgentFromDraft.bind(this),deleteAgent:this.deleteAgent.bind(this),setNewAgentDraft:this.setNewAgentDraft.bind(this),setRemoteAgentCreationError:this.setRemoteAgentCreationError.bind(this),setGenerateSetupScriptSelected:this.setGenerateSetupScriptSelected.bind(this),hasFetchedOnce:!1,showRemoteAgentDiffPanel:this.showRemoteAgentDiffPanel.bind(this),closeRemoteAgentDiffPanel:this.closeRemoteAgentDiffPanel.bind(this),setIsCreatingAgent:this.setIsCreatingAgent.bind(this),toggleAgentPinned:this.toggleAgentPinned.bind(this),setPinnedAgents:this.setPinnedAgents.bind(this),pauseRemoteAgentWorkspace:this.pauseRemoteAgentWorkspace.bind(this),resumeRemoteAgentWorkspace:this.resumeRemoteAgentWorkspace.bind(this),isRemoteAgentSshWindow:!1,remoteAgentSshWindowId:void 0,generateSetupScriptSelected:!1});l(this,"_agentConversations",new Map);l(this,"_initialPrompts",new Map);l(this,"_agentSetupLogsCache",new Map);l(this,"_creationMetrics");l(this,"_preloadedDiffExplanations",new Map);l(this,"maxCacheEntries",10);l(this,"maxCacheSizeBytes",10485760);l(this,"_diffOpsModel");l(this,"subscribers",new Set);l(this,"agentSetupLogs");l(this,"_remoteAgentsClient");l(this,"_stateModel");l(this,"_extensionClient");l(this,"_flagsModel");l(this,"_gitRefModel");l(this,"_cachedUrls",new Map);l(this,"_pendingMessageTracking",new Map);l(this,"_agentSendMessageErrors",new Map);l(this,"sendMessageTimeoutMs",9e4);l(this,"_hasEverUsedRemoteAgent",ot(void 0));l(this,"dispose",()=>{this._stateModel.dispose(),this._remoteAgentsClient.dispose(),this._agentConversations.clear(),this._agentSetupLogsCache.clear(),this._preloadedDiffExplanations.clear(),this._cachedUrls.clear(),this._pendingMessageTracking.forEach(t=>{t.forEach(e=>clearTimeout(e.timeout))}),this._pendingMessageTracking.clear(),this._agentSendMessageErrors.clear(),this.subscribers.clear()});l(this,"setHasEverUsedRemoteAgent",t=>{this._extensionClient.setHasEverUsedRemoteAgent(t),this._hasEverUsedRemoteAgent.set(t)});l(this,"refreshHasEverUsedRemoteAgent",async()=>{if(W(this.hasEverUsedRemoteAgent)!==void 0)return;const t=await this._extensionClient.checkHasEverUsedRemoteAgent();W(this.hasEverUsedRemoteAgent)===void 0&&this._hasEverUsedRemoteAgent.set(t)});l(this,"throttledGetDiffExplanation",Ot(async t=>await this._diffOpsModel.getDiffExplanation(t,void 0,6e4),1e3));this._state.isActive=e,this._flagsModel=s,this._gitRefModel=i,this._diffOpsModel=new Lt(t),this._remoteAgentsClient=new Pt(t),this._extensionClient=new Rt(n,t,s),this._stateModel=r||new $t(this._flagsModel,this._remoteAgentsClient),this._stateModel.onStateUpdate(this.handleStateUpdate.bind(this)),this._flagsModel.subscribe(a=>{a.enableBackgroundAgents?this._stateModel.startStateUpdates():(this.setIsActive(!1),this._stateModel.stopStateUpdates())}),this.loadPinnedAgentsFromStore(),this.refreshHasEverUsedRemoteAgent(),this.checkRemoteAgentStatus()}async checkRemoteAgentStatus(){const t=await this._remoteAgentsClient.getRemoteAgentStatus();this._state.isRemoteAgentSshWindow=t.data.isRemoteAgentSshWindow,this._state.remoteAgentSshWindowId=t.data.remoteAgentId,this.notifySubscribers()}handleOverviewsUpdate(t){var r,a;const e=t.data,s=this._state.agentOverviews,n=e;if(this.isActive&&n.forEach(o=>{o.remote_agent_id===this._state.currentAgentId&&(o.has_updates=!1)}),this._state.currentAgentId&&(this._state.currentAgent=this._state.agentOverviews.find(o=>o.remote_agent_id===this._state.currentAgentId)),this._state.currentAgentId){const o=this._state.currentAgentId,g=o?(r=s.find(u=>u.remote_agent_id===o))==null?void 0:r.status:void 0,h=o?(a=n.find(u=>u.remote_agent_id===o))==null?void 0:a.status:void 0;if(h!==g)if(h===m.agentFailed){const u={type:v.agentFailed,errorMessage:O,canRetry:!1};this._agentSendMessageErrors.set(o,u)}else this._agentSendMessageErrors.delete(o)}this.maybeSendNotifications(n,s);const i=y(n);this._state.agentOverviews=i,this._state.hasFetchedOnce=!0,this._state.agentThreadsError=t.error,this._state.lastSuccessfulOverviewFetch=t.error?this._state.lastSuccessfulOverviewFetch:Date.now(),i.findIndex(o=>o.remote_agent_id===this._state.currentAgentId)===-1&&this.clearCurrentAgent()}handleConversationUpdate(t){var e;if(t.agentId===this._state.currentAgentId){const s={exchanges:t.data,lastFetched:new Date};this._agentConversations.set(t.agentId,s),this._state.currentConversation=s,this._state.agentChatHistoryError=t.error;const n=this._agentSendMessageErrors.get(t.agentId);if(n!=null&&n.failedExchangeId){const i=n.failedExchangeId;((e=this._state.currentConversation)==null?void 0:e.exchanges.some(a=>a.exchange.request_id===i))||this._agentSendMessageErrors.delete(t.agentId)}this._state.isCurrentAgentDetailsLoading=!1}}handleLogsUpdate(t){t.agentId===this._state.currentAgentId&&(this.agentSetupLogs=t.data,this._agentSetupLogsCache.set(t.agentId,t.data))}handleStateUpdate(t){switch(this._state.maxRemoteAgents=this._stateModel.state.maxRemoteAgents,this._state.maxActiveRemoteAgents=this._stateModel.state.maxActiveRemoteAgents,t.type){case"overviews":this.handleOverviewsUpdate(t);break;case"conversation":this.handleConversationUpdate(t);break;case"logs":this.handleLogsUpdate(t);break;case"all":this.handleOverviewsUpdate({type:"overviews",data:t.data.agentOverviews,error:t.data.overviewError}),t.data.agentConversations.forEach((e,s)=>{this._agentConversations.set(s,{exchanges:e,lastFetched:new Date})}),t.data.agentLogs.forEach((e,s)=>{e&&(this._agentSetupLogsCache.set(s,e),s===this._state.currentAgentId&&(this.agentSetupLogs=e))}),this._state.hasFetchedOnce=!0,this._state.agentThreadsError=t.data.overviewError,this._state.agentChatHistoryError=t.data.conversationError,this._state.agentLogsError=t.data.logsError}this.currentAgentId&&this.checkForHistoryErrors(this.currentAgentId),this.notifySubscribers()}subscribe(t){return this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}}notifySubscribers(){this.subscribers.forEach(t=>t(this))}showRemoteAgentDiffPanel(t){const e=this._state.currentAgentId;if(this.reportRemoteAgentEvent({eventName:w.diffPanel,remoteAgentId:e??"",eventData:{diffPanelData:{applied:!1,loadingTimeMs:0}}}),e&&t.changedFiles.length>0&&t.turnIdx===-1&&t.isShowingAggregateChanges){const s=`${e}-${this.generateChangedFilesHash(t.changedFiles)}`,n=this._preloadedDiffExplanations.get(s);if(n)return n.lastAccessed=Date.now(),this._preloadedDiffExplanations.set(s,n),this._remoteAgentsClient.showRemoteAgentDiffPanel({...t,preloadedExplanation:n.explanation,remoteAgentId:e}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=e,void this.notifySubscribers()}this._remoteAgentsClient.showRemoteAgentDiffPanel({...t,remoteAgentId:e}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=e,this.notifySubscribers()}closeRemoteAgentDiffPanel(){this._remoteAgentsClient.closeRemoteAgentDiffPanel(),this._state.isDiffPanelOpen=!1,this._state.diffPanelAgentId=void 0,this.notifySubscribers()}_getChatHistory(t){const e=this._agentConversations.get(t);if(!e)return[];const s=this.isAgentRunning(t);return e.exchanges.map(({exchange:n},i)=>{const r=n.request_id.startsWith("pending-");return{seen_state:xt.seen,structured_request_nodes:n.request_nodes??[],status:r||i===e.exchanges.length-1&&s?V.sent:V.success,request_message:n.request_message,response_text:r?"":n.response_text,structured_output_nodes:n.response_nodes??[],request_id:n.request_id??`remote-agent-${i}`}})}getCurrentChatHistory(){const t=this.agentSetupLogs;return this.currentAgentId&&!t&&(this.agentSetupLogs={steps:[]},this._stateModel.startStateUpdates({logs:{agentId:this.currentAgentId}})),this._getChatHistory(this.currentAgentId??"")}getToolStates(){var a,o,g;const t=new Map,e=new Set,s=new Map;(a=this.currentConversation)==null||a.exchanges.forEach(h=>{var u,_;(u=h.exchange.response_nodes)==null||u.forEach(p=>{p.tool_use&&e.add(p.tool_use.tool_use_id)}),(_=h.exchange.request_nodes)==null||_.forEach(p=>{p.type===j.TOOL_RESULT&&p.tool_result_node&&s.set(p.tool_result_node.tool_use_id,p.tool_result_node)})});const n=(o=this.currentConversation)==null?void 0:o.exchanges[this.currentConversation.exchanges.length-1];let i=0,r=null;return(g=n==null?void 0:n.exchange.response_nodes)==null||g.forEach(h=>{var u;h.id>i&&(i=h.id,r=(u=h.tool_use)!=null&&u.tool_use_id?h.tool_use.tool_use_id:null)}),e.forEach(h=>{const u=s.get(h);if(u)t.set(h,{phase:u.is_error?f.error:f.completed,result:{isError:u.is_error,text:u.content},requestId:"",toolUseId:h});else{const _=this.isCurrentAgentRunning;h===r?t.set(h,{phase:_?f.running:f.cancelled,requestId:"",toolUseId:h}):t.set(h,{phase:f.cancelled,requestId:"",toolUseId:h})}}),t}getLastToolUseState(){const t=this.getToolStates(),e=[...t.keys()].pop();return t.get(e??"")??{phase:f.unknown}}getToolUseState(t){const e=t;return this.getToolStates().get(e)??{phase:f.completed,requestId:"",toolUseId:t}}async setCurrentAgent(t){if(this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=t,this._state.isCurrentAgentDetailsLoading=!!t,t&&this._agentSetupLogsCache.has(t)?this.agentSetupLogs=this._agentSetupLogsCache.get(t):this.agentSetupLogs=void 0,t&&this.checkForHistoryErrors(t),this.notifySubscribers(),t){this._stateModel.startStateUpdates({conversation:{agentId:t},logs:{agentId:t}});try{const e=this._state.agentOverviews.find(s=>s.remote_agent_id===t);!e||e.workspace_status!==M.workspacePaused&&e.workspace_status!==M.workspacePausing||await this._remoteAgentsClient.resumeHintRemoteAgent(t)}catch(e){console.warn("Failed to send resume hint to remote agent:",e)}this.preloadDiffExplanation(t)}}clearCurrentAgent(){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=void 0,this.agentSetupLogs=void 0,this.notifySubscribers()}async preloadDiffExplanation(t){const e=this._agentConversations.get(t);if(!e||e.exchanges.length===0)return;const s=Dt(e.exchanges);if(s.length===0)return;const n=`${t}-${this.generateChangedFilesHash(s)}`;if(this._preloadedDiffExplanations.get(n)||s.length>12)return;let i=0;if(s.forEach(r=>{var a,o;i+=(((a=r.old_contents)==null?void 0:a.length)||0)+(((o=r.new_contents)==null?void 0:o.length)||0)}),!(i>512e3))try{const r=await this.throttledGetDiffExplanation(s);if(r&&r.length>0){const a=this.generateChangedFilesHash(s),o=`${t}-${a}`;this._preloadedDiffExplanations.set(o,{explanation:r,changedFiles:s,userPrompt:this.getUserMessagePrecedingTurn(e.exchanges,0),timestamp:Date.now(),lastAccessed:Date.now(),changedFilesHash:a,turnIdx:-1}),this.manageCacheSize()}}catch(r){console.error("Failed to preload diff explanation:",r)}}async getDiffDescriptions(t,e){return this._diffOpsModel.getDescriptions(t,e)}getUserMessagePrecedingTurn(t,e){return t.length===0||e<0||e>=t.length?"":t[e].exchange.request_message||""}generateChangedFilesHash(t){const e=t.map(s=>{var n,i;return{oldPath:s.old_path,newPath:s.new_path,oldSize:((n=s.old_contents)==null?void 0:n.length)||0,newSize:((i=s.new_contents)==null?void 0:i.length)||0,oldHash:this.simpleHash(s.old_contents||""),newHash:this.simpleHash(s.new_contents||"")}});return this.simpleHash(JSON.stringify(e))}simpleHash(t){let e=0;for(let s=0;s<t.length;s++)e=(e<<5)-e+t.charCodeAt(s),e|=0;return e.toString(36)}manageCacheSize(){if(this._preloadedDiffExplanations.size<=this.maxCacheEntries)return;const t=Array.from(this._preloadedDiffExplanations.entries()).map(([s,n])=>({key:s,value:n,accessTime:n.lastAccessed||n.timestamp})).sort((s,n)=>s.accessTime-n.accessTime);let e=0;for(t.forEach(s=>{const n=JSON.stringify(s.value.explanation).length,i=s.value.changedFiles.reduce((r,a)=>{var o,g;return r+(((o=a.old_contents)==null?void 0:o.length)||0)+(((g=a.new_contents)==null?void 0:g.length)||0)},0);e+=n+i});t.length>0&&(t.length>this.maxCacheEntries||e>this.maxCacheSizeBytes);){const s=t.shift();if(s){this._preloadedDiffExplanations.delete(s.key);const n=JSON.stringify(s.value.explanation).length,i=s.value.changedFiles.reduce((r,a)=>{var o,g;return r+(((o=a.old_contents)==null?void 0:o.length)||0)+(((g=a.new_contents)==null?void 0:g.length)||0)},0);e-=n+i}}}async sendMessage(t,e){const s=this._state.currentAgentId;if(!s)return this._state.error="No active remote agent",this.notifySubscribers(),!1;const n=this._state.agentOverviews.find(r=>r.remote_agent_id===s);if((n==null?void 0:n.status)===m.agentFailed){const r={type:v.agentFailed,errorMessage:O,canRetry:!1};return this._agentSendMessageErrors.set(s,r),this.notifySubscribers(),!1}let i;this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const r=this._agentConversations.get(s)||{exchanges:[],lastFetched:new Date},a=(this.getfinalSequenceId(s)||0)+1;i="pending-"+Date.now();const o={exchange:{request_message:t,response_text:"",request_id:i,response_nodes:[],request_nodes:[]},changed_files:[],sequence_id:a};r.exchanges.push(o),this._agentConversations.set(s,r),this._state.currentConversation=r,this.notifySubscribers(),this.setupMessageTimeout(s,i);const g={request_nodes:[{id:1,type:j.TEXT,text_node:{content:t}}],model_id:e},h=await this._remoteAgentsClient.sendRemoteAgentChatRequest(s,g,this.sendMessageTimeoutMs);if(h.data.error)throw new Error(h.data.error);return this.clearMessageTimeout(s,i),this._state.currentAgentId&&setTimeout(()=>{this.preloadDiffExplanation(this._state.currentAgentId)},0),this.preloadDiffExplanation(s),!0}catch(r){i&&this.clearMessageTimeout(s,i);const a={type:v.chatRequestFailed,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${s}. ${r}`,canRetry:!0,failedExchangeId:i};return this._agentSendMessageErrors.set(s,a),console.error("Failed to send message:",r),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}setupMessageTimeout(t,e){const s=setTimeout(()=>{this.handleMessageTimeout(t,e)},this.sendMessageTimeoutMs);this._pendingMessageTracking.has(t)||this._pendingMessageTracking.set(t,new Map),this._pendingMessageTracking.get(t).set(e,{timeout:s,timestamp:Date.now()})}clearMessageTimeout(t,e){const s=this._pendingMessageTracking.get(t);if(s){const n=s.get(e);n&&(clearTimeout(n.timeout),s.delete(e),s.size===0&&this._pendingMessageTracking.delete(t))}}async handleMessageTimeout(t,e){const s=this._pendingMessageTracking.get(t);s&&(s.delete(e),s.size===0&&this._pendingMessageTracking.delete(t));const n=this._state.agentOverviews.find(a=>a.remote_agent_id===t);if((n==null?void 0:n.status)===m.agentRunning)return;const i=this._agentConversations.get(t);if(!i||!i.exchanges.find(a=>a.exchange.request_id===e))return;const r={type:v.messageTimeout,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${t}`,canRetry:!0,failedExchangeId:e};this._agentSendMessageErrors.set(t,r);try{await this._remoteAgentsClient.interruptRemoteAgent(t)}catch(a){console.error("Failed to interrupt agent after timeout:",a)}this.notifySubscribers()}removeOptimisticExchange(t,e){const s=this._agentConversations.get(t);s&&(s.exchanges=s.exchanges.filter(n=>n.exchange.request_id!==e),this._agentConversations.set(t,s),t===this._state.currentAgentId&&(this._state.currentConversation=s))}async retryFailedMessage(t,e){const s=this._agentConversations.get(t);if(!s)return!1;const n=s.exchanges.find(i=>i.exchange.request_id===e);return!!n&&(this.removeOptimisticExchange(t,e),this._agentSendMessageErrors.delete(t),this.notifySubscribers(),this.sendMessage(n.exchange.request_message))}async interruptAgent(){const t=this._state.currentAgentId;if(!t)return this._state.error="No active remote agent",void this.notifySubscribers();this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{await this._remoteAgentsClient.interruptRemoteAgent(t)}catch(e){this._state.error=e instanceof Error?e.message:String(e)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgent(t,e,s,n,i){var r;if(!t||!t.trim())return this._state.error="Cannot create a remote agent with an empty prompt",void this.notifySubscribers();this.agentSetupLogs=void 0,this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const a=await this._remoteAgentsClient.createRemoteAgent(t,e,s,n,i,this._creationMetrics);if(a.data.error)throw new Error(a.data.error);if(a.data.agentId&&a.data.success)return this._initialPrompts.set(a.data.agentId,t),await this.setNotificationEnabled(a.data.agentId,((r=this.newAgentDraft)==null?void 0:r.enableNotification)??!0),await this.setCurrentAgent(a.data.agentId),a.data.agentId;throw new Error("Failed to create remote agent: No agent ID returned")}catch(a){throw this._state.error=a instanceof Error?a.message:String(a),this.notifySubscribers(),a}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgentFromDraft(t,e){var i,r;if(this.setRemoteAgentCreationError(null),this.agentSetupLogs=void 0,!t||!t.trim())return void this.setRemoteAgentCreationError("Cannot create a remote agent with an empty prompt");const s=this._state.newAgentDraft;if(!s)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");if(s.isDisabled)return void this.setRemoteAgentCreationError("Cannot create agent with current workspace selection. Please resolve the issues with your workspace selection.");if(!s.commitRef||!s.selectedBranch)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");const n={starting_files:s.commitRef};this._state.isLoading=!0,this.notifySubscribers();try{const a=s.isSetupScriptAgent||((i=s.setupScript)==null?void 0:i.isGenerateOption)===!0;let o=a||(r=s.setupScript)==null?void 0:r.content;if(s.setupScript&&!a){const g=(await this.listSetupScripts()).find(h=>h.path===s.setupScript.path);g&&(o=g.content)}try{return await this.createRemoteAgent(t,n,o,a,e)}catch(g){let h="Failed to create remote agent. Please try again.";return g instanceof Error&&(g.message.includes("too large")||g.message.includes("413")?h="Repository or selected files are too large. Please select a smaller repository or branch.":g.message.includes("timeout")||g.message.includes("504")?h="Request timed out. The repository might be too large or the server is busy.":g.message.includes("rate limit")||g.message.includes("429")?h="Rate limit exceeded. Please try again later.":g.message.includes("unauthorized")||g.message.includes("401")?h="Authentication failed. Please check your GitHub credentials.":g.message.includes("not found")||g.message.includes("404")?h="Repository or branch not found. Please check your selection.":g.message.includes("bad request")||g.message.includes("400")?h="Invalid request. Please check your workspace setup and try again.":g.message.length>0&&(h=`Failed to create remote agent: ${g.message}`)),void this.setRemoteAgentCreationError(h)}}finally{this._state.isLoading=!1,this.notifySubscribers()}}async deleteAgent(t,e=!1){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{if(!await this._remoteAgentsClient.deleteRemoteAgent(t,e))return this._state.error="Failed to delete remote agent",this.notifySubscribers(),!1;this._agentConversations.delete(t),this._agentSetupLogsCache.delete(t),this._state.agentOverviews=this._state.agentOverviews.filter(s=>s.remote_agent_id!==t),this.removeNotificationEnabled(t),this._state.currentAgentId===t&&this.clearCurrentAgent()}catch(s){this._state.error=s instanceof Error?s.message:String(s)}finally{this._state.isLoading=!1,this.notifySubscribers()}return!this._state.error}async sshToRemoteAgent(t){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{return t.workspace_status!==M.workspaceRunning&&(await this._remoteAgentsClient.resumeRemoteAgentWorkspace(t.remote_agent_id),await new Promise(e=>setTimeout(e,5e3))),await this._remoteAgentsClient.sshToRemoteAgent(t.remote_agent_id)}catch(e){return this._state.error=e instanceof Error?e.message:String(e),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async maybeSendNotifications(t,e){const s=new Map(e.map(i=>[i.remote_agent_id,i])),n=await this._remoteAgentsClient.getRemoteAgentNotificationEnabled(t.map(i=>i.remote_agent_id));t.forEach(i=>{const r=s.get(i.remote_agent_id),a=n[i.remote_agent_id],o=(r==null?void 0:r.status)===m.agentRunning,g=i.status===m.agentIdle||i.status===m.agentFailed,h=i.remote_agent_id!==this._state.currentAgentId,u=this._state.isPanelFocused;a&&o&&g&&(h||!u)&&this._remoteAgentsClient.notifyRemoteAgentReady(i)})}async setNotificationEnabled(t,e){await this._remoteAgentsClient.setRemoteAgentNotificationEnabled(t,e),this._state={...this._state,notificationSettings:{...this._state.notificationSettings,[t]:e}},this.notifySubscribers()}async removeNotificationEnabled(t){await this._remoteAgentsClient.deleteRemoteAgentNotificationEnabled(t);const{[t]:e,...s}=this._state.notificationSettings;this._state={...this._state,notificationSettings:s},this.notifySubscribers()}get hasFetchedOnce(){return this._state.hasFetchedOnce}get focusedFilePath(){return this._state.focusedFilePath}setFocusedFilePath(t){this._state.focusedFilePath=t,this.notifySubscribers()}handleMessageFromExtension(t){switch(t.data.type){case E.diffViewFileFocus:return this.setFocusedFilePath(t.data.data.filePath.replace(/^\/+/,"")),!0;case E.showRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!0,!0;case E.closeRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!1,!0;case E.remoteAgentStatusChanged:{const e=t.data;return this._state.isRemoteAgentSshWindow=e.data.isRemoteAgentSshWindow,this._state.remoteAgentSshWindowId=e.data.remoteAgentId,this.notifySubscribers(),!0}default:return!1}}get currentAgentId(){return this._state.currentAgentId}get currentConversation(){return this._agentConversations.get(this._state.currentAgentId??"")??void 0}_getAgentExchanges(t){var e;return((e=this._agentConversations.get(t))==null?void 0:e.exchanges)||[]}get currentExchanges(){const t=this._state.currentAgentId;return t?this._getAgentExchanges(t):[]}get currentStatus(){var e;const t=this._state.currentAgentId;return t&&((e=this._state.agentOverviews.find(s=>s.remote_agent_id===t))==null?void 0:e.status)||m.agentIdle}get currentAgent(){const t=this._state.currentAgentId;return t?this._state.agentOverviews.find(e=>e.remote_agent_id===t):void 0}get agentOverviews(){return this._state.agentOverviews}get isLoading(){return this._state.isLoading}get isCurrentAgentDetailsLoading(){return this._state.isCurrentAgentDetailsLoading}get lastSuccessfulOverviewFetch(){return this._state.lastSuccessfulOverviewFetch}get error(){return this._state.error}get agentThreadsError(){return this._state.agentThreadsError}get agentChatHistoryError(){return this._state.agentChatHistoryError}clearSendMessageError(){this._state.currentAgentId&&(this._agentSendMessageErrors.delete(this._state.currentAgentId),this.notifySubscribers())}get sendMessageError(){return this._agentSendMessageErrors.get(this._state.currentAgentId??"")??void 0}checkForHistoryErrors(t){var i;const e=this._getAgentExchanges(t);if(((i=this._state.agentOverviews.find(r=>r.remote_agent_id===t))==null?void 0:i.status)===m.agentFailed){const r={type:v.agentFailed,errorMessage:O,canRetry:!1};return this._agentSendMessageErrors.set(t,r),void this.notifySubscribers()}const s=e.length>0&&e[e.length-1].exchange.request_id.startsWith("pending-"),n=this._agentSendMessageErrors.get(t);if(s&&!n){const r=e[e.length-1].exchange.request_id,a=this._pendingMessageTracking.get(t),o=a==null?void 0:a.get(r);if(o&&Date.now()-o.timestamp>this.sendMessageTimeoutMs){const g={type:v.messageTimeout,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${t}`,canRetry:!0,failedExchangeId:r};this._agentSendMessageErrors.set(t,g),this.clearMessageTimeout(t,r),this.notifySubscribers()}}}isAgentRunning(t){const e=this._state.agentOverviews.find(a=>a.remote_agent_id===t),s=!(!e||e.status!==m.agentRunning&&e.status!==m.agentStarting),n=this._getAgentExchanges(t),i=this._agentSendMessageErrors.get(t),r=n.length>0&&n[n.length-1].exchange.request_id.startsWith("pending-")&&!i;return s||r}get isCurrentAgentRunning(){return!!this._state.currentAgentId&&this.isAgentRunning(this._state.currentAgentId)}get maxRemoteAgents(){return this._state.maxRemoteAgents}get maxActiveRemoteAgents(){return this._state.maxActiveRemoteAgents}getInitialPrompt(t){return this._initialPrompts.get(t)}clearInitialPrompt(t){this._initialPrompts.delete(t)}get notificationSettings(){return this._state.notificationSettings}get pinnedAgents(){return this._state.pinnedAgents}getfinalSequenceId(t){var n;const e=this._agentConversations.get(t),s=e==null?void 0:e.exchanges;if(s)return((n=s[s.length-1])==null?void 0:n.sequence_id)??void 0}async listSetupScripts(){try{return(await this._remoteAgentsClient.listSetupScripts()).data.scripts}catch(t){return this._state.error=t instanceof Error?t.message:String(t),this.notifySubscribers(),[]}}async saveSetupScript(t,e,s){try{const n=await this._remoteAgentsClient.saveSetupScript(t,e,s);return n.data.success||(this._state.error=n.data.error||"Failed to save setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}async deleteSetupScript(t,e){try{const s=await this._remoteAgentsClient.deleteSetupScript(t,e);return s.data.success||(this._state.error=s.data.error||"Failed to delete setup script",this.notifySubscribers()),s.data}catch(s){return this._state.error=s instanceof Error?s.message:String(s),this.notifySubscribers(),{success:!1,error:s instanceof Error?s.message:String(s)}}}async renameSetupScript(t,e,s){try{const n=await this._remoteAgentsClient.renameSetupScript(t,e,s);return n.data.success||(this._state.error=n.data.error||"Failed to rename setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}get isActive(){return this._state.isActive}setIsActive(t){this._state.isActive=t;const e=this._state.currentAgentId,s={conversation:e?{agentId:e}:void 0,logs:e?{agentId:e}:void 0};t?this._stateModel.startStateUpdates(s):this._stateModel.stopStateUpdates(s),this.notifySubscribers()}get isPanelFocused(){return this._state.isPanelFocused}setIsPanelFocused(t){this._state.isPanelFocused=t,this.notifySubscribers()}optimisticallyClearAgentUpdates(t){var s;const e=this._state.agentOverviews.findIndex(n=>n.remote_agent_id===t);if(e!==-1&&this._state.agentOverviews[e].has_updates){const n=[...this._state.agentOverviews];n[e]={...n[e],has_updates:!1},this._state.agentOverviews=n,((s=this._state.currentAgent)==null?void 0:s.remote_agent_id)===t&&(this._state.currentAgent={...this._state.currentAgent,has_updates:!1}),this.notifySubscribers()}}async updateRemoteAgentTitle(t,e){var i,r,a;const s=this._state.agentOverviews.findIndex(o=>o.remote_agent_id===t);if(s===-1)return void console.warn(`Agent with ID ${t} not found in overviews`);const n=this._state.agentOverviews[s].title;try{const o=[...this._state.agentOverviews];o[s]={...o[s],title:e},this._state.agentOverviews=o,((i=this._state.currentAgent)==null?void 0:i.remote_agent_id)===t&&(this._state.currentAgent={...this._state.currentAgent,title:e}),this.notifySubscribers();const g=await this._remoteAgentsClient.updateRemoteAgentTitle(t,e);if(!g.data.success||!g.data.agent)throw console.error("Failed to update remote agent title:",g.data.error),this._state.error=g.data.error||"Failed to update remote agent title",new Error(this._state.error);{const h=[...this._state.agentOverviews];h[s]=g.data.agent,this._state.agentOverviews=h,((r=this._state.currentAgent)==null?void 0:r.remote_agent_id)===t&&(this._state.currentAgent=g.data.agent),this.notifySubscribers()}}catch(o){const g=[...this._state.agentOverviews];g[s]={...g[s],title:n},this._state.agentOverviews=g,((a=this._state.currentAgent)==null?void 0:a.remote_agent_id)===t&&(this._state.currentAgent={...this._state.currentAgent,title:n});const h=o instanceof Error?o.message:String(o);throw this._state.error=h,this.notifySubscribers(),o}}setRemoteAgentCreationError(t){this._state.remoteAgentCreationError=t,this.notifySubscribers()}get isDiffPanelOpen(){return this._state.isDiffPanelOpen}get diffPanelAgentId(){return this._state.diffPanelAgentId}get remoteAgentCreationError(){return this._state.remoteAgentCreationError}setNewAgentDraft(t){this._state.newAgentDraft=t,this.notifySubscribers()}get generateSetupScriptSelected(){return this._state.generateSetupScriptSelected}setGenerateSetupScriptSelected(t){this._state.generateSetupScriptSelected=t,this.notifySubscribers()}setCreationMetrics(t){this._creationMetrics=t}get creationMetrics(){return this._creationMetrics}refreshAgentChatHistory(t){this._stateModel.refreshCurrentAgent(t)}get newAgentDraft(){return this._state.newAgentDraft}setIsCreatingAgent(t){this._state.isCreatingAgent=t,this.notifySubscribers()}get isCreatingAgent(){return this._state.isCreatingAgent}get isRemoteAgentSshWindow(){return this._state.isRemoteAgentSshWindow}get remoteAgentSshWindowId(){return this._state.remoteAgentSshWindowId}async showRemoteAgentHomePanel(){await this._remoteAgentsClient.showRemoteAgentHomePanel()}async closeRemoteAgentHomePanel(){await this._remoteAgentsClient.closeRemoteAgentHomePanel()}async saveLastRemoteAgentSetup(t,e,s){try{await this._remoteAgentsClient.saveLastRemoteAgentSetup(t,e,s)}catch(n){console.error("Failed to save last remote agent setup:",n)}}async getLastRemoteAgentSetup(){try{return(await this._remoteAgentsClient.getLastRemoteAgentSetup()).data}catch(t){return console.error("Failed to get last remote agent setup:",t),{lastRemoteAgentGitRepoUrl:null,lastRemoteAgentGitBranch:null,lastRemoteAgentSetupScript:null}}}async loadPinnedAgentsFromStore(){try{const t=await this._remoteAgentsClient.getPinnedAgentsFromStore();this._state.pinnedAgents=t,this.notifySubscribers()}catch(t){console.error("Failed to load pinned agents from store:",t)}}async toggleAgentPinned(t,e){if(!t)return this._state.pinnedAgents;e=e??!1;try{if(this._state.pinnedAgents={...this._state.pinnedAgents,[t]:!e},e){const{[t]:s,...n}=this._state.pinnedAgents;this._state.pinnedAgents=n,await this._remoteAgentsClient.deletePinnedAgentFromStore(t)}else await this._remoteAgentsClient.savePinnedAgentToStore(t,!0);return this.notifySubscribers(),await this._remoteAgentsClient.getPinnedAgentsFromStore()}catch(s){return console.error("Failed to toggle pinned status for remote agent:",s),this._state.pinnedAgents}}async getConversationUrl(t){var o;const e=this._cachedUrls.get(t),s=this._agentConversations.get(t),n=(s==null?void 0:s.exchanges.length)??0;if(e&&s&&e[0]===n)return e[1];const i=this._getChatHistory(t).map(g=>({...g,request_id:g.request_id||"",request_message:g.request_message,response_text:g.response_text||""}));if(i.length===0)throw new Error("No chat history to share");const r=await this._extensionClient.saveChat(t,i,`Remote Agent ${t}`);if(!r.data)throw new Error("Failed to create URL");const a=(o=r.data)==null?void 0:o.url;return a&&this._cachedUrls.set(t,[n,a]),a}async refreshAgentThreads(){this._state.agentThreadsError=void 0,this._state.isLoading=!0,this.notifySubscribers();try{await this._stateModel.refreshAgentOverviews()}catch(t){console.error("Failed to refresh agent threads:",t)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async openDiffInBuffer(t,e,s){await this._remoteAgentsClient.openDiffInBuffer(t,e,s)}async getShouldShowSSHConfigPermissionPrompt(){return this._remoteAgentsClient.getShouldShowSSHConfigPermissionPrompt()}async setPermissionToWriteToSSHConfig(t){await this._remoteAgentsClient.setPermissionToWriteToSSHConfig(t)}async pauseRemoteAgentWorkspace(t){await this._remoteAgentsClient.pauseRemoteAgentWorkspace(t)}async resumeRemoteAgentWorkspace(t){await this._remoteAgentsClient.resumeRemoteAgentWorkspace(t)}async reportRemoteAgentEvent(t){await this._remoteAgentsClient.reportRemoteAgentEvent(t)}getSourceControlType(){return Mt.unknownSourceControl}async reportChatModeEvent(t,e){try{await this.reportRemoteAgentEvent({eventName:w.modeSelector,remoteAgentId:"",eventData:{modeSelectorData:{action:t,mode:e,sourceControl:this.getSourceControlType()}}})}catch(s){console.error("Failed to report chat mode event:",s)}}getCurrentRepoHash(){var e,s,n;const t=(n=(s=(e=this._state.newAgentDraft)==null?void 0:e.commitRef)==null?void 0:s.github_commit_ref)==null?void 0:n.repository_url;return t?this.simpleHash(t):""}getCurrentBranchHash(){var e,s;const t=(s=(e=this._state.newAgentDraft)==null?void 0:e.selectedBranch)==null?void 0:s.name;return t?this.simpleHash(t):""}getHasSetupScriptSelected(){var t;return!!((t=this._state.newAgentDraft)!=null&&t.setupScript)}async getGithubRepoInfo(){try{let t=0,e=!0,s=1;for(;e;){const{repos:n,hasNextPage:i,nextPage:r}=await this._gitRefModel.listUserRepos(s);t+=n.length,e=i,s=r}return{numReposAvailable:t,githubIntegrationEnabled:await this._gitRefModel.isGithubAuthenticated()}}catch{return{numReposAvailable:0,githubIntegrationEnabled:!1}}}async reportRemoteAgentSetupWindowEvent(t){try{const{numReposAvailable:e,githubIntegrationEnabled:s}=await this.getGithubRepoInfo(),n={action:t,repoHash:this.getCurrentRepoHash(),branchHash:this.getCurrentBranchHash(),hasSetupScriptSelected:this.getHasSetupScriptSelected(),githubIntegrationEnabled:s,numReposAvailable:e,sourceControl:this.getSourceControlType()};await this.reportRemoteAgentEvent({eventName:w.remoteAgentSetupWindow,remoteAgentId:"",eventData:{remoteAgentSetupWindowData:n}})}catch(e){console.error("Failed to report remote agent setup window event:",e)}}async reportRemoteAgentThreadListEvent(t,e,s){try{const n={action:t,numAgentsInList:e,sourceControl:this.getSourceControlType()};await this.reportRemoteAgentEvent({eventName:w.remoteAgentThreadList,remoteAgentId:s||"",eventData:{remoteAgentThreadListData:n}})}catch(n){console.error("Failed to report remote agent thread list event:",n)}}async reportRemoteAgentNewThreadButtonEvent(t,e,s){try{const n={action:t,threadType:e,sourceControl:this.getSourceControlType()};await this.reportRemoteAgentEvent({eventName:w.remoteAgentNewThreadButton,remoteAgentId:s||"",eventData:{remoteAgentNewThreadButtonData:n}})}catch(n){console.error("Failed to report remote agent new thread button event:",n)}}setPinnedAgents(t){this._state.pinnedAgents={...t},this.notifySubscribers()}get hasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}}l(it,"key","remoteAgentsModel");function qt(c){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 0 16 16"},c[0]],n={};for(let i=0;i<s.length;i+=1)n=T(n,s[i]);return{c(){t=ut("svg"),e=new _t(!0),this.h()},l(i){t=ct(i,"svg",{xmlns:!0,width:!0,height:!0,"data-ds-icon":!0,viewBox:!0});var r=dt(t);e=lt(r,!0),r.forEach(R),this.h()},h(){e.a=null,G(t,n)},m(i,r){ht(i,t,r),e.m('<path fill-opacity=".01" d="M0 0h16v16H0z"/><path fill-opacity=".71" d="M9.125 2.563c0 .311.25.562.563.562h2.393L6.85 8.352a.562.562 0 0 0 .795.795l5.227-5.227.002 2.393c0 .311.25.562.563.562a.56.56 0 0 0 .562-.562v-3.75A.56.56 0 0 0 13.438 2h-3.75a.56.56 0 0 0-.563.563m-5.437.187C2.755 2.75 2 3.505 2 4.438v7.875C2 13.245 2.755 14 3.688 14h7.874c.933 0 1.688-.755 1.688-1.687v-3a.56.56 0 0 0-.562-.563.56.56 0 0 0-.563.563v3a.56.56 0 0 1-.562.562H3.686a.56.56 0 0 1-.562-.562V4.437a.56.56 0 0 1 .563-.562h3a.561.561 0 1 0 0-1.125z"/>',t)},p(i,[r]){G(t,n=gt(s,[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 0 16 16"},1&r&&i[0]]))},i:F,o:F,d(i){i&&R(t)}}}function Nt(c,t,e){return c.$$set=s=>{e(0,t=T(T({},t),z(s)))},[t=z(t)]}class Wt extends K{constructor(t){super(),Q(this,t,Nt,qt,Z,{})}}const Gt=c=>({}),X=c=>({});function Y(c){let t,e,s,n;return e=new Ut({props:{defaultColor:c[1],stickyColor:c[3],size:c[0],variant:c[2],tooltip:c[4]||void 0,stateVariant:{success:"soft"},onClick:c[5],icon:!c[8].text,$$slots:{iconLeft:[Bt],default:[zt]},$$scope:{ctx:c}}}),{c(){t=wt("span"),nt(e.$$.fragment),B(t,"class",s="c-open-file-button-container c-open-file-button__size--"+c[0]+" svelte-pdfhuj")},m(i,r){tt(i,t,r),st(e,t,null),n=!0},p(i,r){const a={};2&r&&(a.defaultColor=i[1]),8&r&&(a.stickyColor=i[3]),1&r&&(a.size=i[0]),4&r&&(a.variant=i[2]),16&r&&(a.tooltip=i[4]||void 0),32&r&&(a.onClick=i[5]),256&r&&(a.icon=!i[8].text),131072&r&&(a.$$scope={dirty:r,ctx:i}),e.$set(a),(!n||1&r&&s!==(s="c-open-file-button-container c-open-file-button__size--"+i[0]+" svelte-pdfhuj"))&&B(t,"class",s)},i(i){n||(S(e.$$.fragment,i),n=!0)},o(i){b(e.$$.fragment,i),n=!1},d(i){i&&R(t),et(e)}}}function zt(c){let t;const e=c[16].text,s=yt(e,c,c[17],X);return{c(){s&&s.c()},m(n,i){s&&s.m(n,i),t=!0},p(n,i){s&&s.p&&(!t||131072&i)&&bt(s,e,n,n[17],t?Et(e,n[17],i,Gt):Ct(n[17]),X)},i(n){t||(S(s,n),t=!0)},o(n){b(s,n),t=!1},d(n){s&&s.d(n)}}}function Bt(c){let t,e;return t=new Wt({props:{slot:"iconLeft"}}),{c(){nt(t.$$.fragment)},m(s,n){st(t,s,n),e=!0},p:F,i(s){e||(S(t.$$.fragment,s),e=!0)},o(s){b(t.$$.fragment,s),e=!1},d(s){et(t,s)}}}function Jt(c){let t,e,s=c[6]&&Y(c);return{c(){s&&s.c(),t=ft()},m(n,i){s&&s.m(n,i),tt(n,t,i),e=!0},p(n,[i]){n[6]?s?(s.p(n,i),64&i&&S(s,1)):(s=Y(n),s.c(),S(s,1),s.m(t.parentNode,t)):s&&(pt(),b(s,1,1,()=>{s=null}),mt())},i(n){e||(S(s),e=!0)},o(n){b(s),e=!1},d(n){n&&R(t),s&&s.d(n)}}}function jt(c,t,e){let s,n,i,r,{$$slots:a={},$$scope:o}=t;const g=vt(a);let{path:h}=t,{start:u=0}=t,{stop:_=0}=t,{size:p=0}=t,{color:P="neutral"}=t,{variant:D="ghost-block"}=t,{stickyColor:L=!1}=t,{tooltip:U={neutral:"Open File In Editor",success:"Opening file..."}}=t,{onOpenLocalFile:k=async function(d){var q,N;if((q=d==null?void 0:d.stopPropagation)==null||q.call(d),(N=d==null?void 0:d.preventDefault)==null||N.call(d),h)return await H(),"success"}}=t;const H=async()=>{const d=await(A==null?void 0:A.extensionClient.resolvePath({rootPath:"",relPath:h}));return A==null?void 0:A.extensionClient.openFile({repoRoot:(d==null?void 0:d.repoRoot)??"",pathName:(d==null?void 0:d.pathName)??"",range:{start:Math.max(u,0),stop:Math.max(_,0)}})},A=It(),$=At(it.key);return St(c,$,d=>e(15,r=d)),c.$$set=d=>{"path"in d&&e(9,h=d.path),"start"in d&&e(10,u=d.start),"stop"in d&&e(11,_=d.stop),"size"in d&&e(0,p=d.size),"color"in d&&e(1,P=d.color),"variant"in d&&e(2,D=d.variant),"stickyColor"in d&&e(3,L=d.stickyColor),"tooltip"in d&&e(4,U=d.tooltip),"onOpenLocalFile"in d&&e(5,k=d.onOpenLocalFile),"$$scope"in d&&e(17,o=d.$$scope)},c.$$.update=()=>{32768&c.$$.dirty&&e(13,s=r==null?void 0:r.isRemoteAgentSshWindow),32768&c.$$.dirty&&e(14,n=!!(r!=null&&r.isActive)),24576&c.$$.dirty&&e(6,i=!n||s)},[p,P,D,L,U,k,i,$,g,h,u,_,H,s,n,r,a,o]}class ae extends K{constructor(t){super(),Q(this,t,jt,Jt,Z,{path:9,start:10,stop:11,size:0,color:1,variant:2,stickyColor:3,tooltip:4,onOpenLocalFile:5,openFile:12})}get openFile(){return this.$$.ctx[12]}}export{ae as O,it as R,v as S,re as a};
