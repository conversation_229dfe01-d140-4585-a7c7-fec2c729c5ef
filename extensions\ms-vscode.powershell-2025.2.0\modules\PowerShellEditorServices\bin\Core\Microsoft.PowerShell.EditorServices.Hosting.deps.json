{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Microsoft.PowerShell.EditorServices.Hosting/4.3.0": {"dependencies": {"Microsoft.PowerShell.EditorServices": "4.3.0", "PowerShellStandard.Library": "5.1.1", "Roslynator.Analyzers": "4.12.10", "Roslynator.CodeAnalysis.Analyzers": "4.12.10", "Roslynator.Formatting.Analyzers": "4.12.10", "System.IO.Pipes.AccessControl": "5.0.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "Microsoft.PowerShell.EditorServices.Reference": "*******"}, "runtime": {"Microsoft.PowerShell.EditorServices.Hosting.dll": {}}}, "MediatR/8.1.0": {"runtime": {"lib/netstandard2.1/MediatR.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Configuration/6.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.322.12309"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Options/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Primitives/9.0.3": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.VisualStudio.Threading/17.6.40": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.VisualStudio.Threading.Analyzers": "17.6.40", "Microsoft.VisualStudio.Validation": "17.6.11", "Microsoft.Win32.Registry": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Threading.dll": {"assemblyVersion": "17.6.0.0", "fileVersion": "17.6.40.52285"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Threading.Analyzers/17.6.40": {}, "Microsoft.VisualStudio.Validation/17.6.11": {"runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"assemblyVersion": "17.6.0.0", "fileVersion": "17.6.11.23530"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Nerdbank.Streams/2.10.69": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.VisualStudio.Threading": "17.6.40", "Microsoft.VisualStudio.Validation": "17.6.11", "System.IO.Pipelines": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.1/Nerdbank.Streams.dll": {"assemblyVersion": "********", "fileVersion": "2.10.69.57563"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "OmniSharp.Extensions.DebugAdapter/0.19.9": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.1", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.JsonRpc.Generators": "0.19.9"}, "runtime": {"lib/net6.0/OmniSharp.Extensions.DebugAdapter.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OmniSharp.Extensions.DebugAdapter.Server/0.19.9": {"dependencies": {"OmniSharp.Extensions.DebugAdapter.Shared": "0.19.9"}, "runtime": {"lib/net6.0/OmniSharp.Extensions.DebugAdapter.Server.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OmniSharp.Extensions.DebugAdapter.Shared/0.19.9": {"dependencies": {"OmniSharp.Extensions.DebugAdapter": "0.19.9", "OmniSharp.Extensions.JsonRpc": "0.19.9"}, "runtime": {"lib/net6.0/OmniSharp.Extensions.DebugAdapter.Shared.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OmniSharp.Extensions.JsonRpc/0.19.9": {"dependencies": {"MediatR": "8.1.0", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Nerdbank.Streams": "2.10.69", "Newtonsoft.Json": "13.0.3", "OmniSharp.Extensions.JsonRpc.Generators": "0.19.9", "System.Collections.Immutable": "5.0.0", "System.Reactive": "6.0.0", "System.Threading.Channels": "6.0.0"}, "runtime": {"lib/net6.0/OmniSharp.Extensions.JsonRpc.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OmniSharp.Extensions.JsonRpc.Generators/0.19.9": {}, "OmniSharp.Extensions.LanguageProtocol/0.19.9": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.1", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.JsonRpc.Generators": "0.19.9"}, "runtime": {"lib/net6.0/OmniSharp.Extensions.LanguageProtocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OmniSharp.Extensions.LanguageServer/0.19.9": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.1", "OmniSharp.Extensions.JsonRpc": "0.19.9", "OmniSharp.Extensions.LanguageProtocol": "0.19.9", "OmniSharp.Extensions.LanguageServer.Shared": "0.19.9"}, "runtime": {"lib/net6.0/OmniSharp.Extensions.LanguageServer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "OmniSharp.Extensions.LanguageServer.Shared/0.19.9": {"dependencies": {"OmniSharp.Extensions.LanguageProtocol": "0.19.9"}, "runtime": {"lib/net6.0/OmniSharp.Extensions.LanguageServer.Shared.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "PowerShellStandard.Library/5.1.1": {}, "Roslynator.Analyzers/4.12.10": {}, "Roslynator.CodeAnalysis.Analyzers/4.12.10": {}, "Roslynator.Formatting.Analyzers/4.12.10": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Collections.Immutable/5.0.0": {}, "System.Diagnostics.DiagnosticSource/9.0.3": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Pipelines/7.0.0": {"runtime": {"lib/net7.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IO.Pipes.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Reactive/6.0.0": {"runtime": {"lib/net6.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/6.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "Microsoft.PowerShell.EditorServices/4.3.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "OmniSharp.Extensions.DebugAdapter.Server": "0.19.9", "OmniSharp.Extensions.LanguageServer": "0.19.9", "PowerShellStandard.Library": "5.1.1", "Roslynator.Analyzers": "4.12.10", "Roslynator.CodeAnalysis.Analyzers": "4.12.10", "Roslynator.Formatting.Analyzers": "4.12.10", "System.IO.Pipes.AccessControl": "5.0.0", "System.Security.Principal": "4.3.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"Microsoft.PowerShell.EditorServices.dll": {"assemblyVersion": "4.3.0", "fileVersion": ""}}}, "Microsoft.PowerShell.EditorServices.Reference/*******": {"runtime": {"Microsoft.PowerShell.EditorServices.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Microsoft.PowerShell.EditorServices.Hosting/4.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "MediatR/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-KJFnA0MV83bNOhvYbjIX1iDykhwFXoQu0KV7E1SVbNA/CmO2I7SAm2Baly0eS7VJ2GwlmStLajBfeiNgTpvYzQ==", "path": "mediatr/8.1.0", "hashPath": "mediatr.8.1.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BUyFU9t+HzlSE7ri4B+AQN2BgTgHv/uM82s5ZkgU1BApyzWzIl48nDsG5wR1t0pniNuuyTBzG3qCW8152/NtSw==", "path": "microsoft.extensions.configuration/6.0.1", "hashPath": "microsoft.extensions.configuration.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3<PERSON>rKzND8LIC7o08QAVlKfaEIYEvLJbtmVbFZVBRXeu9YkKfSSzLZfR1SUfQPBIy9mKLhEtJgGYImkcMNaKE0A==", "path": "microsoft.extensions.configuration.binder/6.0.0", "hashPath": "microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-lDbxJpkl6X8KZGpkAxgrrthQ42YeiR0xjPp7KPx+sCPc3ZbpaIbjzd0QQ+9kDdK2RU2DOl3pc6tQyAgEZY3V0A==", "path": "microsoft.extensions.dependencyinjection/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-TfaHPSe39NyL2wxkisRxXK7xvHGZYBZ+dy3r+mqGvnxKgAPdHkMu3QMQZI4pquP6W5FIQBqs8FJpWV8ffCgDqQ==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Rec77KHk4iNpFznHi5/6wF3MlUDcKqg26t8gRYbUm1PSukZ4B6mrXpZsJSNOiwyhhQVkjYbaoZxi5XJgRQ5lFg==", "path": "microsoft.extensions.filesystemglobbing/9.0.3", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-utIi2R1nm+PCWkvWBf1Ou6LWqg9iLfHU23r8yyU9VCvda4dEs7xbTZSwGa5KuwbpzpgCbHCIuKaFHB3zyFmnGw==", "path": "microsoft.extensions.logging/9.0.3", "hashPath": "microsoft.extensions.logging.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-H/MBMLt9A/69Ux4OrV7oCKt3DcMT04o5SCqDolulzQA66TLFEpYYb4qedMs/uwrLtyHXGuDGWKZse/oa8W9AZw==", "path": "microsoft.extensions.logging.abstractions/9.0.3", "hashPath": "microsoft.extensions.logging.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-xE7MpY70lkw1oiid5y6FbL9dVw8oLfkx8RhSNGN8sSzBlCqGn0SyT3Fqc8tZnDaPIq7Z8R9RTKlS564DS+MV3g==", "path": "microsoft.extensions.options/9.0.3", "hashPath": "microsoft.extensions.options.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXWINbTn0vC0FYc9GaQTISbxhQLAMrvtbuvD9N6JelEaIS/Pr62wUCinrq5bf1WRBGczt1v4wDhxFtVFNcMdUQ==", "path": "microsoft.extensions.options.configurationextensions/6.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-yCCJHvBcRyqapMSNzP+kTc57Eaavq2cr5Tmuil6/XVnipQf5xmskxakSQ1enU6S4+fNg3sJ27WcInV64q24JsA==", "path": "microsoft.extensions.primitives/9.0.3", "hashPath": "microsoft.extensions.primitives.9.0.3.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Threading/17.6.40": {"type": "package", "serviceable": true, "sha512": "sha512-hLa/0xargG7p3bF7aeq2/lRYn/bVnfZXurUWVHx+MNqxxAUjIDMKi4OIOWbYQ/DTkbn9gv8TLvgso+6EtHVQQg==", "path": "microsoft.visualstudio.threading/17.6.40", "hashPath": "microsoft.visualstudio.threading.17.6.40.nupkg.sha512"}, "Microsoft.VisualStudio.Threading.Analyzers/17.6.40": {"type": "package", "serviceable": true, "sha512": "sha512-uU8vYr/Nx3ldEWcsbiHiyAX1G7od3eFK1+Aga6ZvgCvU+nQkcXYVkIMcSEkIDWkFaldx1dkoVvX3KRNQD0R7dw==", "path": "microsoft.visualstudio.threading.analyzers/17.6.40", "hashPath": "microsoft.visualstudio.threading.analyzers.17.6.40.nupkg.sha512"}, "Microsoft.VisualStudio.Validation/17.6.11": {"type": "package", "serviceable": true, "sha512": "sha512-J+9L/iac6c8cwcgVSCMuoIYOlD1Jw4mbZ8XMe1IZVj8p8+3dJ46LnnkIkTRMjK7xs9UtU9MoUp1JGhWoN6fAEw==", "path": "microsoft.visualstudio.validation/17.6.11", "hashPath": "microsoft.visualstudio.validation.17.6.11.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Nerdbank.Streams/2.10.69": {"type": "package", "serviceable": true, "sha512": "sha512-Y<PERSON>udzeVyQRJAqytjpo1jdHkh2t+vqQqyusBqb2sFSOAOGEnyOXhcHx/rQqSuCIXUDr50a3XuZnamGRfQVBOf4g==", "path": "nerdbank.streams/2.10.69", "hashPath": "nerdbank.streams.2.10.69.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "OmniSharp.Extensions.DebugAdapter/0.19.9": {"type": "package", "serviceable": true, "sha512": "sha512-Jy9RlVei7ay3LavvPH4F8BnIIMAo5th5EI8JnVe1RQlOxvu18H8hOyZ8fLFHtzbObs+oTONsJ9aynqeyMOErgA==", "path": "omnisharp.extensions.debugadapter/0.19.9", "hashPath": "omnisharp.extensions.debugadapter.0.19.9.nupkg.sha512"}, "OmniSharp.Extensions.DebugAdapter.Server/0.19.9": {"type": "package", "serviceable": true, "sha512": "sha512-XRJ6EW44DaODkzjAuN1XbpnPFkciJIM2sIx4KpsvV/2Rle1CdRJY4gA6vJn+2uNh5hRr1d0SqZSieqV9Ly0utw==", "path": "omnisharp.extensions.debugadapter.server/0.19.9", "hashPath": "omnisharp.extensions.debugadapter.server.0.19.9.nupkg.sha512"}, "OmniSharp.Extensions.DebugAdapter.Shared/0.19.9": {"type": "package", "serviceable": true, "sha512": "sha512-A4psuqk+slrs585cCkZkwUO08nW0I6SVH4u7B7d8wU9lH0LLRTvQBlo3QlxrVAMxjwljPFzXaaRHv7D7X1BXbw==", "path": "omnisharp.extensions.debugadapter.shared/0.19.9", "hashPath": "omnisharp.extensions.debugadapter.shared.0.19.9.nupkg.sha512"}, "OmniSharp.Extensions.JsonRpc/0.19.9": {"type": "package", "serviceable": true, "sha512": "sha512-utFvrx9OYXhCS5rnfWAVeedJCrucuDLAOrKXjohf/NOjG9FFVbcp+hLqj9Ng+AxoADRD+rSJYHfBOeqGl5zW0A==", "path": "omnisharp.extensions.jsonrpc/0.19.9", "hashPath": "omnisharp.extensions.jsonrpc.0.19.9.nupkg.sha512"}, "OmniSharp.Extensions.JsonRpc.Generators/0.19.9": {"type": "package", "serviceable": true, "sha512": "sha512-hiWC0yGcKM+K00fgiL7KBmlvULmkKNhm40ZSzxqT+jNV21r+YZgKzEREhQe40ufb4tjcIxdYkif++IzGl/3H/Q==", "path": "omnisharp.extensions.jsonrpc.generators/0.19.9", "hashPath": "omnisharp.extensions.jsonrpc.generators.0.19.9.nupkg.sha512"}, "OmniSharp.Extensions.LanguageProtocol/0.19.9": {"type": "package", "serviceable": true, "sha512": "sha512-d0crY6w5SyunGlERP27YeUeJnJfUjvJoALFlPMU4CHu3jovG1Y8RxLpihCPX8fKdjzgy7Ii+VjFYtIpDEEQqYQ==", "path": "omnisharp.extensions.languageprotocol/0.19.9", "hashPath": "omnisharp.extensions.languageprotocol.0.19.9.nupkg.sha512"}, "OmniSharp.Extensions.LanguageServer/0.19.9": {"type": "package", "serviceable": true, "sha512": "sha512-g09wOOCQ/oFqtZ47Q5R9E78tz2a5ODEB+V+S65wAiiRskR7xwL78Tse4/8ToBc8G/ZgQgqLtAOPo/BSPmHNlbw==", "path": "omnisharp.extensions.languageserver/0.19.9", "hashPath": "omnisharp.extensions.languageserver.0.19.9.nupkg.sha512"}, "OmniSharp.Extensions.LanguageServer.Shared/0.19.9": {"type": "package", "serviceable": true, "sha512": "sha512-+p+py79MrNG3QnqRrBp5J7Wc810HFFczMH8/WLIiUqih1bqmKPFY9l/uzBvq1Ko8+YO/8tzI7BDffHvaguISEw==", "path": "omnisharp.extensions.languageserver.shared/0.19.9", "hashPath": "omnisharp.extensions.languageserver.shared.0.19.9.nupkg.sha512"}, "PowerShellStandard.Library/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-e31xJjG+Kjbv6YF3Yq6D4Dl3or8v7LrNF41k3CXrWozW6hR1zcOe5KYuZJaGSiAgLnwP8wcW+I3+IWEzMPZKXQ==", "path": "powershellstandard.library/5.1.1", "hashPath": "powershellstandard.library.5.1.1.nupkg.sha512"}, "Roslynator.Analyzers/4.12.10": {"type": "package", "serviceable": true, "sha512": "sha512-Wecq3nfhTvnJPxX87hKbjMdX1xeCAMJf9rdBX3nQLTntQs9v0fFbUB2eQSSOArXMuFh7MxjLWaL4+b6XMi1NDA==", "path": "roslynator.analyzers/4.12.10", "hashPath": "roslynator.analyzers.4.12.10.nupkg.sha512"}, "Roslynator.CodeAnalysis.Analyzers/4.12.10": {"type": "package", "serviceable": true, "sha512": "sha512-P/8rraJ0lDO792C8VajyKsaljMR4pWheoSv6bJnCspydf8gNZe+r2JJNis0IxMO2a8vLOyB+8CMIaVLIUndSNQ==", "path": "roslynator.codeanalysis.analyzers/4.12.10", "hashPath": "roslynator.codeanalysis.analyzers.4.12.10.nupkg.sha512"}, "Roslynator.Formatting.Analyzers/4.12.10": {"type": "package", "serviceable": true, "sha512": "sha512-Yz0OhQtuECTPBMoURxcv1XKgquVpUcUOpRGac8tormJSJzbMYK8qvVSvjIvQkQOrfYPLFa8aak5200BEAq/X9Q==", "path": "roslynator.formatting.analyzers/4.12.10", "hashPath": "roslynator.formatting.analyzers.4.12.10.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cBA+28xDW33tSiGht/H8xvr8lnaCrgJ7EdO348AfSGsX4PPJUOULKxny/cc9DVNGExaCrtqagsnm5M2mkWIZ+g==", "path": "system.diagnostics.diagnosticsource/9.0.3", "hashPath": "system.diagnostics.diagnosticsource.9.0.3.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "path": "system.io.pipelines/7.0.0", "hashPath": "system.io.pipelines.7.0.0.nupkg.sha512"}, "System.IO.Pipes.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-P0FIsXSFNL1AXlHO9zpJ9atRUzVyoPZCkcbkYGZfXXMx9xlGA2H3HOGBwIhpKhB+h0eL3hry/z0UcfJZ+yb2kQ==", "path": "system.io.pipes.accesscontrol/5.0.0", "hashPath": "system.io.pipes.accesscontrol.5.0.0.nupkg.sha512"}, "System.Reactive/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-31kfaW4ZupZzPsI5PVe77VhnvFF55qgma7KZr/E0iFTs6fmdhhG8j0mgEx620iLTey1EynOkEfnyTjtNEpJzGw==", "path": "system.reactive/6.0.0", "hashPath": "system.reactive.6.0.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Microsoft.PowerShell.EditorServices/4.3.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.PowerShell.EditorServices.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}