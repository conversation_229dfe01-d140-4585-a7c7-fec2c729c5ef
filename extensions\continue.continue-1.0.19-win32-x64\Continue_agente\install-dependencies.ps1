# Script de Instalação do Continue Agent
# Instala todas as dependências necessárias

Write-Host "🚀 Instalando dependências do Continue Agent..." -ForegroundColor Green

# Verificar se está executando como administrador
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️ Este script precisa ser executado como Administrador" -ForegroundColor Yellow
    Write-Host "Reiniciando como Administrador..." -ForegroundColor Yellow
    Start-Process PowerShell -Verb RunAs -ArgumentList "-File `"$PSCommandPath`""
    exit
}

Write-Host "✅ Executando como Administrador" -ForegroundColor Green

# Função para verificar se um comando existe
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# 1. Instalar Chocolatey (gerenciador de pacotes)
if (-not (Test-Command "choco")) {
    Write-Host "📦 Instalando Chocolatey..." -ForegroundColor Cyan
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    refreshenv
} else {
    Write-Host "✅ Chocolatey já está instalado" -ForegroundColor Green
}

# 2. Instalar Node.js
if (-not (Test-Command "node")) {
    Write-Host "📦 Instalando Node.js..." -ForegroundColor Cyan
    choco install nodejs -y
    refreshenv
} else {
    Write-Host "✅ Node.js já está instalado" -ForegroundColor Green
    node --version
}

# 3. Instalar npm (geralmente vem com Node.js)
if (-not (Test-Command "npm")) {
    Write-Host "📦 Instalando npm..." -ForegroundColor Cyan
    choco install npm -y
    refreshenv
} else {
    Write-Host "✅ npm já está instalado" -ForegroundColor Green
    npm --version
}

# 4. Instalar Git (se não estiver instalado)
if (-not (Test-Command "git")) {
    Write-Host "📦 Instalando Git..." -ForegroundColor Cyan
    choco install git -y
    refreshenv
} else {
    Write-Host "✅ Git já está instalado" -ForegroundColor Green
}

# 5. Instalar Python (necessário para algumas dependências)
if (-not (Test-Command "python")) {
    Write-Host "📦 Instalando Python..." -ForegroundColor Cyan
    choco install python -y
    refreshenv
} else {
    Write-Host "✅ Python já está instalado" -ForegroundColor Green
}

# 6. Navegar para o diretório do agente
$agentPath = "C:\Users\<USER>\.vscode\extensions\continue.continue-1.0.19-win32-x64\Continue_agente"
Set-Location $agentPath

Write-Host "📁 Diretório atual: $agentPath" -ForegroundColor Cyan

# 7. Criar package.json se não existir
if (-not (Test-Path "package.json")) {
    Write-Host "📝 Criando package.json..." -ForegroundColor Cyan
    
    $packageJson = @{
        name = "continue-agent"
        version = "1.0.0"
        description = "Continue Agent - Advanced Programming Assistant"
        main = "core/agent-main.js"
        scripts = @{
            start = "node core/agent-main.js"
            install = "node integrate-agent.js"
            test = "echo 'No tests specified'"
        }
        dependencies = @{
            "axios" = "^1.6.0"
            "cheerio" = "^1.0.0-rc.12"
            "fs-extra" = "^11.1.1"
            "node-fetch" = "^3.3.2"
            "uuid" = "^9.0.1"
            "marked" = "^9.1.2"
            "highlight.js" = "^11.9.0"
            "jsdom" = "^22.1.0"
            "turndown" = "^7.1.2"
            "esprima" = "^4.0.1"
            "acorn" = "^8.11.2"
            "typescript" = "^5.2.2"
            "@types/node" = "^20.8.0"
            "prettier" = "^3.0.3"
            "eslint" = "^8.52.0"
        }
        devDependencies = @{
            "nodemon" = "^3.0.1"
            "jest" = "^29.7.0"
        }
        keywords = @("continue", "agent", "programming", "ai", "assistant")
        author = "Continue Agent"
        license = "MIT"
    }
    
    $packageJson | ConvertTo-Json -Depth 10 | Out-File -FilePath "package.json" -Encoding UTF8
    Write-Host "✅ package.json criado" -ForegroundColor Green
}

# 8. Instalar dependências npm
Write-Host "📦 Instalando dependências npm..." -ForegroundColor Cyan
npm install

# 9. Instalar dependências globais necessárias
Write-Host "📦 Instalando dependências globais..." -ForegroundColor Cyan
npm install -g nodemon typescript ts-node

# 10. Verificar se todas as dependências foram instaladas
Write-Host "🔍 Verificando instalação..." -ForegroundColor Cyan

$dependencies = @("axios", "cheerio", "fs-extra", "uuid", "marked")
$allInstalled = $true

foreach ($dep in $dependencies) {
    try {
        $result = npm list $dep 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $dep instalado" -ForegroundColor Green
        } else {
            Write-Host "❌ $dep não encontrado" -ForegroundColor Red
            $allInstalled = $false
        }
    } catch {
        Write-Host "❌ Erro ao verificar $dep" -ForegroundColor Red
        $allInstalled = $false
    }
}

# 11. Executar integração do agente
if ($allInstalled) {
    Write-Host "🔧 Executando integração do agente..." -ForegroundColor Cyan
    node integrate-agent.js
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "🎉 INSTALAÇÃO CONCLUÍDA COM SUCESSO!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Próximos passos:" -ForegroundColor Yellow
        Write-Host "   1. Reinicie o VSCode completamente" -ForegroundColor White
        Write-Host "   2. Abra o Continue (Ctrl+Shift+L)" -ForegroundColor White
        Write-Host "   3. Verifique se o botão 'Agent' está ativo" -ForegroundColor White
        Write-Host "   4. Use comandos como /edit, /web-search, etc." -ForegroundColor White
        Write-Host ""
        Write-Host "🤖 O Continue Agent está pronto para uso!" -ForegroundColor Green
    } else {
        Write-Host "❌ Erro na integração do agente" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Algumas dependências não foram instaladas corretamente" -ForegroundColor Red
    Write-Host "💡 Tente executar: npm install --force" -ForegroundColor Yellow
}

# 12. Mostrar informações finais
Write-Host ""
Write-Host "📊 Resumo da Instalação:" -ForegroundColor Cyan
Write-Host "   • Node.js: $(if (Test-Command 'node') { '✅ Instalado' } else { '❌ Não instalado' })" -ForegroundColor White
Write-Host "   • npm: $(if (Test-Command 'npm') { '✅ Instalado' } else { '❌ Não instalado' })" -ForegroundColor White
Write-Host "   • Dependências: $(if ($allInstalled) { '✅ Instaladas' } else { '❌ Incompletas' })" -ForegroundColor White
Write-Host "   • Agente: $(if (Test-Path 'core/agent-main.js') { '✅ Configurado' } else { '❌ Não configurado' })" -ForegroundColor White

Write-Host ""
Write-Host "🔧 Para verificar o status do agente:" -ForegroundColor Cyan
Write-Host "   node integrate-agent.js --status" -ForegroundColor White

Write-Host ""
Write-Host "📁 Arquivos importantes:" -ForegroundColor Cyan
Write-Host "   • Configuração: C:\Users\<USER>\.continue\config.json" -ForegroundColor White
Write-Host "   • Agente: $agentPath" -ForegroundColor White

Write-Host ""
Write-Host "Pressione qualquer tecla para continuar..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
