/**
 * Continue_agente - Agente de Programação Offline Avançado
 * Baseado no Augment Agent com integração ao LM Studio
 *
 * Este agente fornece capacidades avançadas de programação incluindo:
 * - Análise de contexto profunda
 * - Refatoração inteligente
 * - Geração de testes
 * - Debugging avançado
 * - Prevenção de erros
 * - Pesquisa web integrada
 * - Motor de contexto avançado
 */

const vscode = require('vscode');
const path = require('path');
const fs = require('fs');

class ContinueAgent {
    constructor() {
        this.contextEngine = new ContextEngine();
        this.codeAnalyzer = new CodeAnalyzer();
        this.refactoringEngine = new RefactoringEngine();
        this.testGenerator = new TestGenerator();
        this.debugger = new AdvancedDebugger();
        this.qualityController = new QualityController();
        
        this.config = this.loadConfig();
        this.initializeAgent();
    }

    loadConfig() {
        try {
            const configPath = path.join(__dirname, 'config.json');
            const configData = fs.readFileSync(configPath, 'utf8');
            return JSON.parse(configData);
        } catch (error) {
            console.error('Erro ao carregar configuração:', error);
            return this.getDefaultConfig();
        }
    }

    getDefaultConfig() {
        return {
            models: [{
                title: "Qwen3-30B-Python-Coder",
                provider: "lmstudio",
                model: "Qwen3-30B-A3B-python-coder.i1-Q4_K_S",
                apiBase: "http://localhost:1234/v1",
                apiKey: "lm-studio"
            }]
        };
    }

    async initializeAgent() {
        console.log('🚀 Inicializando Continue_agente...');
        
        // Verificar conexão com LM Studio
        await this.verifyLMStudioConnection();
        
        // Inicializar motor de contexto
        await this.contextEngine.initialize();
        
        // Configurar analisador de código
        await this.codeAnalyzer.initialize();
        
        // Registrar comandos do VSCode
        this.registerCommands();
        
        console.log('✅ Continue_agente inicializado com sucesso!');
    }

    async verifyLMStudioConnection() {
        try {
            const response = await fetch(`${this.config.models[0].apiBase}/models`, {
                headers: {
                    'Authorization': `Bearer ${this.config.models[0].apiKey}`
                }
            });
            
            if (response.ok) {
                console.log('✅ Conexão com LM Studio estabelecida');
                return true;
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Erro na conexão com LM Studio:', error);
            vscode.window.showErrorMessage(
                'Não foi possível conectar ao LM Studio. Verifique se está rodando na porta 1234.'
            );
            return false;
        }
    }

    registerCommands() {
        const commands = [
            {
                command: 'continue-agent.analyzeCode',
                callback: this.analyzeCode.bind(this)
            },
            {
                command: 'continue-agent.refactorCode',
                callback: this.refactorCode.bind(this)
            },
            {
                command: 'continue-agent.generateTests',
                callback: this.generateTests.bind(this)
            },
            {
                command: 'continue-agent.debugCode',
                callback: this.debugCode.bind(this)
            },
            {
                command: 'continue-agent.optimizeCode',
                callback: this.optimizeCode.bind(this)
            },
            {
                command: 'continue-agent.validateChanges',
                callback: this.validateChanges.bind(this)
            }
        ];

        commands.forEach(cmd => {
            vscode.commands.registerCommand(cmd.command, cmd.callback);
        });
    }

    async analyzeCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Nenhum arquivo aberto para análise');
            return;
        }

        const document = editor.document;
        const selection = editor.selection;
        const code = selection.isEmpty ? document.getText() : document.getText(selection);

        try {
            vscode.window.showInformationMessage('🔍 Analisando código...');
            
            // Obter contexto relevante
            const context = await this.contextEngine.getRelevantContext(document.uri, code);
            
            // Analisar código
            const analysis = await this.codeAnalyzer.analyzeCode(code, context);
            
            // Mostrar resultados
            this.showAnalysisResults(analysis);
            
        } catch (error) {
            console.error('Erro na análise:', error);
            vscode.window.showErrorMessage('Erro durante a análise do código');
        }
    }

    async refactorCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Nenhum arquivo aberto para refatoração');
            return;
        }

        const document = editor.document;
        const selection = editor.selection;
        const code = selection.isEmpty ? document.getText() : document.getText(selection);

        try {
            vscode.window.showInformationMessage('🔧 Refatorando código...');
            
            // Validar antes da refatoração
            const validation = await this.qualityController.validateBeforeRefactoring(document.uri, code);
            if (!validation.safe) {
                vscode.window.showWarningMessage(`Refatoração pode ser arriscada: ${validation.reason}`);
                const proceed = await vscode.window.showQuickPick(['Sim', 'Não'], {
                    placeHolder: 'Deseja continuar mesmo assim?'
                });
                if (proceed !== 'Sim') return;
            }

            // Obter contexto
            const context = await this.contextEngine.getRelevantContext(document.uri, code);
            
            // Refatorar
            const refactoredCode = await this.refactoringEngine.refactor(code, context);
            
            // Aplicar mudanças
            await this.applyCodeChanges(editor, selection, refactoredCode);
            
        } catch (error) {
            console.error('Erro na refatoração:', error);
            vscode.window.showErrorMessage('Erro durante a refatoração do código');
        }
    }

    async generateTests() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Nenhum arquivo aberto para geração de testes');
            return;
        }

        const document = editor.document;
        const selection = editor.selection;
        const code = selection.isEmpty ? document.getText() : document.getText(selection);

        try {
            vscode.window.showInformationMessage('🧪 Gerando testes...');
            
            // Obter contexto
            const context = await this.contextEngine.getRelevantContext(document.uri, code);
            
            // Gerar testes
            const tests = await this.testGenerator.generateTests(code, context);
            
            // Criar arquivo de teste
            await this.createTestFile(document.uri, tests);
            
        } catch (error) {
            console.error('Erro na geração de testes:', error);
            vscode.window.showErrorMessage('Erro durante a geração de testes');
        }
    }

    async debugCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Nenhum arquivo aberto para debug');
            return;
        }

        try {
            vscode.window.showInformationMessage('🐛 Analisando bugs...');
            
            const document = editor.document;
            const diagnostics = vscode.languages.getDiagnostics(document.uri);
            
            // Analisar problemas
            const debugInfo = await this.debugger.analyzeBugs(document, diagnostics);
            
            // Mostrar sugestões de correção
            this.showDebugSuggestions(debugInfo);
            
        } catch (error) {
            console.error('Erro no debug:', error);
            vscode.window.showErrorMessage('Erro durante a análise de bugs');
        }
    }

    async optimizeCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Nenhum arquivo aberto para otimização');
            return;
        }

        const document = editor.document;
        const selection = editor.selection;
        const code = selection.isEmpty ? document.getText() : document.getText(selection);

        try {
            vscode.window.showInformationMessage('⚡ Otimizando código...');
            
            // Obter contexto
            const context = await this.contextEngine.getRelevantContext(document.uri, code);
            
            // Otimizar
            const optimizedCode = await this.codeAnalyzer.optimizeCode(code, context);
            
            // Aplicar mudanças
            await this.applyCodeChanges(editor, selection, optimizedCode);
            
        } catch (error) {
            console.error('Erro na otimização:', error);
            vscode.window.showErrorMessage('Erro durante a otimização do código');
        }
    }

    async validateChanges() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Nenhum arquivo aberto para validação');
            return;
        }

        try {
            vscode.window.showInformationMessage('✅ Validando mudanças...');
            
            const document = editor.document;
            const validation = await this.qualityController.validateDocument(document);
            
            this.showValidationResults(validation);
            
        } catch (error) {
            console.error('Erro na validação:', error);
            vscode.window.showErrorMessage('Erro durante a validação');
        }
    }

    // Métodos auxiliares serão implementados nos próximos arquivos
    showAnalysisResults(analysis) {
        // Implementação será adicionada
    }

    async applyCodeChanges(editor, selection, newCode) {
        // Implementação será adicionada
    }

    async createTestFile(originalUri, tests) {
        // Implementação será adicionada
    }

    showDebugSuggestions(debugInfo) {
        // Implementação será adicionada
    }

    showValidationResults(validation) {
        // Implementação será adicionada
    }
}

module.exports = ContinueAgent;
