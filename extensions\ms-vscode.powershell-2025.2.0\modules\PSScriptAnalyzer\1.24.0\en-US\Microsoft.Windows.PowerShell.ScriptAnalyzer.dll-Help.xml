﻿<?xml version="1.0" encoding="utf-8"?>
<helpItems schema="maml" xmlns="http://msh">
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Get-ScriptAnalyzerRule</command:name>
      <command:verb>Get</command:verb>
      <command:noun>ScriptAnalyzerRule</command:noun>
      <maml:description>
        <maml:para>Gets the script analyzer rules on the local computer.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Gets the script analyzer rules on the local computer. You can select rules by Name, Severity, Source, or SourceType, or even particular words in the rule description.</maml:para>
      <maml:para>Use this cmdlet to create collections of rules to include and exclude when running the `Invoke-ScriptAnalyzer` cmdlet.</maml:para>
      <maml:para>To get information about the rules, see the value of the Description property of each rule.</maml:para>
      <maml:para>The PSScriptAnalyzer module tests the PowerShell code in a script, module, or DSC resource to determine if it fulfils best practice standards.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Get-ScriptAnalyzerRule</maml:name>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="CustomizedRulePath">
          <maml:name>CustomRulePath</maml:name>
          <maml:description>
            <maml:para>By default, PSScriptAnalyzer gets only the standard rules specified in the `Microsoft.Windows.PowerShell.ScriptAnalyzer.BuiltinRules.dll` file in the module. Use this parameter to get the custom Script Analyzer rules in the specified path and the standard Script Analyzer rules.</maml:para>
            <maml:para>Enter the path to a .NET assembly or module that contains Script Analyzer rules. You can enter only one value, but wildcards are supported. To get rules in subdirectories of the path, use the RecurseCustomRulePath parameter.</maml:para>
            <maml:para>You can create custom rules using a .NET assembly or a PowerShell module, such as the Community Analyzer Rules (https://github.com/PowerShell/PSScriptAnalyzer/tree/main/Tests/Engine/CommunityAnalyzerRules)in the GitHub repository.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
          <maml:name>Name</maml:name>
          <maml:description>
            <maml:para>Gets only rules with the specified names or name patterns. Wildcards are supported. If you list multiple names or patterns, it gets all rules that match any of the name patterns.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RecurseCustomRulePath</maml:name>
          <maml:description>
            <maml:para>Searches the CustomRulePath location recursively to add rules defined in files in subdirectories of the path. By default, `Get-ScriptAnalyzerRule` adds only the custom rules in the specified path.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Severity</maml:name>
          <maml:description>
            <maml:para>Gets only rules with the specified severity values. Valid values are:</maml:para>
            <maml:para>- Information</maml:para>
            <maml:para>- Warning</maml:para>
            <maml:para>- Error</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="CustomizedRulePath">
        <maml:name>CustomRulePath</maml:name>
        <maml:description>
          <maml:para>By default, PSScriptAnalyzer gets only the standard rules specified in the `Microsoft.Windows.PowerShell.ScriptAnalyzer.BuiltinRules.dll` file in the module. Use this parameter to get the custom Script Analyzer rules in the specified path and the standard Script Analyzer rules.</maml:para>
          <maml:para>Enter the path to a .NET assembly or module that contains Script Analyzer rules. You can enter only one value, but wildcards are supported. To get rules in subdirectories of the path, use the RecurseCustomRulePath parameter.</maml:para>
          <maml:para>You can create custom rules using a .NET assembly or a PowerShell module, such as the Community Analyzer Rules (https://github.com/PowerShell/PSScriptAnalyzer/tree/main/Tests/Engine/CommunityAnalyzerRules)in the GitHub repository.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
        <maml:name>Name</maml:name>
        <maml:description>
          <maml:para>Gets only rules with the specified names or name patterns. Wildcards are supported. If you list multiple names or patterns, it gets all rules that match any of the name patterns.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>All rules</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>RecurseCustomRulePath</maml:name>
        <maml:description>
          <maml:para>Searches the CustomRulePath location recursively to add rules defined in files in subdirectories of the path. By default, `Get-ScriptAnalyzerRule` adds only the custom rules in the specified path.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Severity</maml:name>
        <maml:description>
          <maml:para>Gets only rules with the specified severity values. Valid values are:</maml:para>
          <maml:para>- Information</maml:para>
          <maml:para>- Warning</maml:para>
          <maml:para>- Error</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>All rules</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>You can't pipe input to this cmdlet.</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Windows.PowerShell.ScriptAnalyzer.Generic.RuleInfo</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>The RuleInfo object is a custom object created specifically for Script Analyzer.</maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>EXAMPLE 1 - Get all Script Analyzer rules on the local computer</maml:title>
        <dev:code>Get-ScriptAnalyzerRule</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>----- EXAMPLE 2 - Gets only rules with the Error severity -----</maml:title>
        <dev:code>Get-ScriptAnalyzerRule -Severity Error</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-- EXAMPLE 3 - Run only the DSC rules with the Error severity --</maml:title>
        <dev:code>$DSCError = Get-ScriptAnalyzerRule -Severity Error | Where-Object SourceName -eq PSDSC
$Path = "$home\Documents\WindowsPowerShell\Modules\MyDSCModule\*"
Invoke-ScriptAnalyzerRule -Path $Path -IncludeRule $DSCError -Recurse</dev:code>
        <dev:remarks>
          <maml:para>Using the IncludeRule parameter of `Invoke-ScriptAnalyzerRule` is more efficient than using its Severity parameter, which is applied only after using all rules to analyze all module files.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>---------- EXAMPLE 4 - Get rules by name and severity ----------</maml:title>
        <dev:code>$TestParameters = Get-ScriptAnalyzerRule -Severity Error, Warning -Name *Parameter*, *Alias*</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>----------------- EXAMPLE 5 - Get custom rules -----------------</maml:title>
        <dev:code>Get-ScriptAnalyzerRule -CustomRulePath $home\Documents\WindowsPowerShell\Modules\*StrictRules -RecurseCustomRulePath</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Version:</maml:linkText>
        <maml:uri>https://learn.microsoft.com/powershell/module/psscriptanalyzer/get-scriptanalyzerrule?view=ps-modules&amp;wt.mc_id=ps-gethelp</maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Invoke-ScriptAnalyzer</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>PSScriptAnalyzer on GitHub</maml:linkText>
        <maml:uri>https://github.com/PowerShell/PSScriptAnalyzer</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Invoke-Formatter</command:name>
      <command:verb>Invoke</command:verb>
      <command:noun>Formatter</command:noun>
      <maml:description>
        <maml:para>Formats a script text based on the input settings or default settings.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>The `Invoke-Formatter` cmdlet takes a string input and formats it according to defined settings. If no Settings parameter is provided, the cmdlet assumes the default code formatting settings as defined in `Settings/CodeFormatting.psd1`.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Invoke-Formatter</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="1" aliases="none">
          <maml:name>ScriptDefinition</maml:name>
          <maml:description>
            <maml:para>The text of the script to be formatted represented as a string. This is not a ScriptBlock object.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True" position="2" aliases="none">
          <maml:name>Settings</maml:name>
          <maml:description>
            <maml:para>A settings hashtable or a path to a PowerShell data file (`.psd1`) that contains the settings.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>CodeFormatting</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True" position="3" aliases="none">
          <maml:name>Range</maml:name>
          <maml:description>
            <maml:para>The range within which formatting should take place. The value of this parameter must be an array of four integers. These numbers must be greater than 0. The four integers represent the following four values in this order:</maml:para>
            <maml:para>- starting line number</maml:para>
            <maml:para>- starting column number</maml:para>
            <maml:para>- ending line number</maml:para>
            <maml:para>- ending column number</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Int32[]</command:parameterValue>
          <dev:type>
            <maml:name>Int32[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True" position="3" aliases="none">
        <maml:name>Range</maml:name>
        <maml:description>
          <maml:para>The range within which formatting should take place. The value of this parameter must be an array of four integers. These numbers must be greater than 0. The four integers represent the following four values in this order:</maml:para>
          <maml:para>- starting line number</maml:para>
          <maml:para>- starting column number</maml:para>
          <maml:para>- ending line number</maml:para>
          <maml:para>- ending column number</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Int32[]</command:parameterValue>
        <dev:type>
          <maml:name>Int32[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="1" aliases="none">
        <maml:name>ScriptDefinition</maml:name>
        <maml:description>
          <maml:para>The text of the script to be formatted represented as a string. This is not a ScriptBlock object.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True" position="2" aliases="none">
        <maml:name>Settings</maml:name>
        <maml:description>
          <maml:para>A settings hashtable or a path to a PowerShell data file (`.psd1`) that contains the settings.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
        <dev:type>
          <maml:name>Object</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>CodeFormatting</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.String</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>The formatted string result.</maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>EXAMPLE 1 - Format the input script text using the default settings</maml:title>
        <dev:code>$scriptDefinition = @'
function foo {
"hello"
  }
'@

Invoke-Formatter -ScriptDefinition $scriptDefinition

function foo {
    "hello"
}</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>EXAMPLE 2 - Format the input script using the settings defined in a hashtable</maml:title>
        <dev:code>$scriptDefinition = @'
function foo {
"hello"
}
'@

$settings = @{
    IncludeRules = @("PSPlaceOpenBrace", "PSUseConsistentIndentation")
    Rules = @{
        PSPlaceOpenBrace = @{
            Enable = $true
            OnSameLine = $false
        }
        PSUseConsistentIndentation = @{
            Enable = $true
        }
    }
}

Invoke-Formatter -ScriptDefinition $scriptDefinition -Settings $settings

function foo
{
    "hello"
}</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>EXAMPLE 3 - Format the input script text using the settings defined a `.psd1` file</maml:title>
        <dev:code>Invoke-Formatter -ScriptDefinition $scriptDefinition -Settings /path/to/settings.psd1</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Version:</maml:linkText>
        <maml:uri>https://learn.microsoft.com/powershell/module/psscriptanalyzer/invoke-formatter?view=ps-modules&amp;wt.mc_id=ps-gethelp</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Invoke-ScriptAnalyzer</command:name>
      <command:verb>Invoke</command:verb>
      <command:noun>ScriptAnalyzer</command:noun>
      <maml:description>
        <maml:para>Evaluates a script or module based on selected best practice rules</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>`Invoke-ScriptAnalyzer` evaluates scripts or module files (`.ps1`, `.psm1`, and `.psd1` files) based on a collection of best practice rules and returns objects that represent rule violations. It also includes special rules to analyze DSC resources.</maml:para>
      <maml:para>`Invoke-ScriptAnalyzer` comes with a set of built-in rules. By default, it uses all rules. You can use the IncludeRule and ExcludeRule parameters to select the rules you want. You can use the `Get-ScriptAnalyzerRule` cmdlet to examine and select the rules you want to include or exclude from the evaluation.</maml:para>
      <maml:para>You can also use customized rules that you write in PowerShell scripts, or compile in assemblies using C#. Custom rules can also be selected using the IncludeRule and ExcludeRule parameters.</maml:para>
      <maml:para>You can also include a rule in the analysis, but suppress the output of that rule for selected functions or scripts. This feature should be used only when necessary. To get rules that were suppressed, run `Invoke-ScriptAnalyzer` with the SuppressedOnly parameter.</maml:para>
      <maml:para>For usage in CI systems, the EnableExit exits the shell with an exit code equal to the number of error records.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Invoke-ScriptAnalyzer</maml:name>
        <command:parameter required="true" variableLength="true" globbing="true" pipelineInput="True (ByPropertyName, ByValue)" position="0" aliases="PSPath">
          <maml:name>Path</maml:name>
          <maml:description>
            <maml:para>Specifies the path to the scripts or module to be analyzed. Wildcard characters are supported.</maml:para>
            <maml:para>Enter the path to a script (`.ps1`) or module file (`.psm1`) or to a directory that contains scripts or modules. If the directory contains other types of files, they are ignored.</maml:para>
            <maml:para>To analyze files that are not in the root directory of the specified path, use a wildcard character (`C:\Modules\MyModule\ `) or the Recurse * parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="CustomizedRulePath">
          <maml:name>CustomRulePath</maml:name>
          <maml:description>
            <maml:para>Enter the path to a file that defines rules or a directory that contains files that define rules. Wildcard characters are supported. When CustomRulePath is specified, only the custom rules found in the specified paths are used for the analysis. If `Invoke-ScriptAnalyzer` cannot find rules in the , it runs the standard rules without notice.</maml:para>
            <maml:para>To add rules defined in subdirectories of the path, use the RecurseCustomRulePath parameter. To include the built-in rules, add the IncludeDefaultRules parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableExit</maml:name>
          <maml:description>
            <maml:para>On completion of the analysis, this parameter exits the PowerShell sessions and returns an exit code equal to the number of error records. This can be useful in continuous integration (CI) pipeline.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
          <maml:name>ExcludeRule</maml:name>
          <maml:description>
            <maml:para>Omits the specified rules from the Script Analyzer test. Wildcard characters are supported.</maml:para>
            <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. You can also specify a list of excluded rules in a Script Analyzer profile file. You can exclude standard rules and rules in a custom rule path.</maml:para>
            <maml:para>When you exclude a rule, the rule does not run on any of the files in the path. To exclude a rule on a particular line, parameter, function, script, or class, adjust the Path parameter or suppress the rule. For information about suppressing a rule, see the examples.</maml:para>
            <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules are included.</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Fix</maml:name>
          <maml:description>
            <maml:para>Fixes certain warnings that contain a fix in their DiagnosticRecord .</maml:para>
            <maml:para>When you used Fix , `Invoke-ScriptAnalyzer` applies the fixes before running the analysis. Make sure that you have a backup of your files when using this parameter. It tries to preserve the file encoding but there are still some cases where the encoding can change.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeDefaultRules</maml:name>
          <maml:description>
            <maml:para>Invoke default rules along with Custom rules.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeRule</maml:name>
          <maml:description>
            <maml:para>Runs only the specified rules in the Script Analyzer test. By default, PSScriptAnalyzer runs all rules.</maml:para>
            <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. Wildcard characters are supported. You can also specify rule names in a Script Analyzer profile file.</maml:para>
            <maml:para>When you use the CustomizedRulePath parameter, you can use this parameter to include standard rules and rules in the custom rule paths.</maml:para>
            <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
            <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules are included.</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Recurse</maml:name>
          <maml:description>
            <maml:para>Runs Script Analyzer on the files in the Path directory and all subdirectories recursively.</maml:para>
            <maml:para>Recurse applies only to the Path parameter value. To search the CustomRulePath recursively, use the RecurseCustomRulePath parameter.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RecurseCustomRulePath</maml:name>
          <maml:description>
            <maml:para>Adds rules defined in subdirectories of the CustomRulePath location. By default, `Invoke-ScriptAnalyzer` uses only the custom rules defined in the specified file or directory. To include the built-in rules, use the IncludeDefaultRules parameter.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ReportSummary</maml:name>
          <maml:description>
            <maml:para>Write a summary of the violations found to the host.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SaveDscDependency</maml:name>
          <maml:description>
            <maml:para>Resolve DSC resource dependencies.</maml:para>
            <maml:para>When `Invoke-ScriptAnalyzer` is run with this parameter, it looks for instances of `Import-DSCResource -ModuleName &lt;somemodule&gt;`. If `&lt;somemodule&gt;` is cannot be found by searching the `$env:PSModulePath`, `Invoke-ScriptAnalyzer` returns parse error. This error is caused by the PowerShell parser not being able to find the symbol for `&lt;somemodule&gt;`.</maml:para>
            <maml:para>If `Invoke-ScriptAnalyzer` finds the module in the PowerShell Gallery, it downloads the missing module to a temp path. The temp path is then added to `$env:PSModulePath` for duration of the scan. The temp location can be found in `$LOCALAPPDATA/PSScriptAnalyzer/TempModuleDir`.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Profile">
          <maml:name>Settings</maml:name>
          <maml:description>
            <maml:para>A path to a file containing a user-defined profile or a hashtable object containing settings for ScriptAnalyzer.</maml:para>
            <maml:para>Runs `Invoke-ScriptAnalyzer` with the parameters and values specified in the file or hashtable.</maml:para>
            <maml:para>If the path or the content of the file or hashtable is invalid, it is ignored. The parameters and values in the profile take precedence over the same parameter and values specified at the command line.</maml:para>
            <maml:para>A Script Analyzer profile file is a text file that contains a hashtable with one or more of the following keys:</maml:para>
            <maml:para>- CustomRulePath</maml:para>
            <maml:para>- ExcludeRules</maml:para>
            <maml:para>- IncludeDefaultRules</maml:para>
            <maml:para>- IncludeRules</maml:para>
            <maml:para>- RecurseCustomRulePath</maml:para>
            <maml:para>- Rules</maml:para>
            <maml:para>- Severity</maml:para>
            <maml:para></maml:para>
            <maml:para>The keys and values in the profile are interpreted as if they were standard parameters and values of `Invoke-ScriptAnalyzer`, similar to splatting. For more information, see about_Splatting (https://learn.microsoft.com/powershell/module/microsoft.powershell.core/about/about_splatting).</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Severity</maml:name>
          <maml:description>
            <maml:para>After running Script Analyzer with all rules, this parameter selects rule violations with the specified severity.</maml:para>
            <maml:para>Valid values are:</maml:para>
            <maml:para>- Error</maml:para>
            <maml:para>- Warning</maml:para>
            <maml:para>- Information.</maml:para>
            <maml:para></maml:para>
            <maml:para>You can specify one ore more severity values.</maml:para>
            <maml:para>The parameter filters the rules violations only after running all rules. To filter rules efficiently, use `Get-ScriptAnalyzerRule` to select the rules you want to run.</maml:para>
            <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rule violations</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SuppressedOnly</maml:name>
          <maml:description>
            <maml:para>Returns violations only for rules that are suppressed.</maml:para>
            <maml:para>Returns a SuppressedRecord object ( Microsoft.Windows.PowerShell.ScriptAnalyzer.Generic.SuppressedRecord ).</maml:para>
            <maml:para>To suppress a rule, use the SuppressMessageAttribute . For help, see the examples.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Invoke-ScriptAnalyzer</maml:name>
        <command:parameter required="true" variableLength="true" globbing="true" pipelineInput="True (ByPropertyName, ByValue)" position="0" aliases="PSPath">
          <maml:name>Path</maml:name>
          <maml:description>
            <maml:para>Specifies the path to the scripts or module to be analyzed. Wildcard characters are supported.</maml:para>
            <maml:para>Enter the path to a script (`.ps1`) or module file (`.psm1`) or to a directory that contains scripts or modules. If the directory contains other types of files, they are ignored.</maml:para>
            <maml:para>To analyze files that are not in the root directory of the specified path, use a wildcard character (`C:\Modules\MyModule\ `) or the Recurse * parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="CustomizedRulePath">
          <maml:name>CustomRulePath</maml:name>
          <maml:description>
            <maml:para>Enter the path to a file that defines rules or a directory that contains files that define rules. Wildcard characters are supported. When CustomRulePath is specified, only the custom rules found in the specified paths are used for the analysis. If `Invoke-ScriptAnalyzer` cannot find rules in the , it runs the standard rules without notice.</maml:para>
            <maml:para>To add rules defined in subdirectories of the path, use the RecurseCustomRulePath parameter. To include the built-in rules, add the IncludeDefaultRules parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableExit</maml:name>
          <maml:description>
            <maml:para>On completion of the analysis, this parameter exits the PowerShell sessions and returns an exit code equal to the number of error records. This can be useful in continuous integration (CI) pipeline.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
          <maml:name>ExcludeRule</maml:name>
          <maml:description>
            <maml:para>Omits the specified rules from the Script Analyzer test. Wildcard characters are supported.</maml:para>
            <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. You can also specify a list of excluded rules in a Script Analyzer profile file. You can exclude standard rules and rules in a custom rule path.</maml:para>
            <maml:para>When you exclude a rule, the rule does not run on any of the files in the path. To exclude a rule on a particular line, parameter, function, script, or class, adjust the Path parameter or suppress the rule. For information about suppressing a rule, see the examples.</maml:para>
            <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules are included.</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Fix</maml:name>
          <maml:description>
            <maml:para>Fixes certain warnings that contain a fix in their DiagnosticRecord .</maml:para>
            <maml:para>When you used Fix , `Invoke-ScriptAnalyzer` applies the fixes before running the analysis. Make sure that you have a backup of your files when using this parameter. It tries to preserve the file encoding but there are still some cases where the encoding can change.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeDefaultRules</maml:name>
          <maml:description>
            <maml:para>Invoke default rules along with Custom rules.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeRule</maml:name>
          <maml:description>
            <maml:para>Runs only the specified rules in the Script Analyzer test. By default, PSScriptAnalyzer runs all rules.</maml:para>
            <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. Wildcard characters are supported. You can also specify rule names in a Script Analyzer profile file.</maml:para>
            <maml:para>When you use the CustomizedRulePath parameter, you can use this parameter to include standard rules and rules in the custom rule paths.</maml:para>
            <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
            <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules are included.</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeSuppressed</maml:name>
          <maml:description>
            <maml:para>Include suppressed diagnostics in output.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Recurse</maml:name>
          <maml:description>
            <maml:para>Runs Script Analyzer on the files in the Path directory and all subdirectories recursively.</maml:para>
            <maml:para>Recurse applies only to the Path parameter value. To search the CustomRulePath recursively, use the RecurseCustomRulePath parameter.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RecurseCustomRulePath</maml:name>
          <maml:description>
            <maml:para>Adds rules defined in subdirectories of the CustomRulePath location. By default, `Invoke-ScriptAnalyzer` uses only the custom rules defined in the specified file or directory. To include the built-in rules, use the IncludeDefaultRules parameter.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ReportSummary</maml:name>
          <maml:description>
            <maml:para>Write a summary of the violations found to the host.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SaveDscDependency</maml:name>
          <maml:description>
            <maml:para>Resolve DSC resource dependencies.</maml:para>
            <maml:para>When `Invoke-ScriptAnalyzer` is run with this parameter, it looks for instances of `Import-DSCResource -ModuleName &lt;somemodule&gt;`. If `&lt;somemodule&gt;` is cannot be found by searching the `$env:PSModulePath`, `Invoke-ScriptAnalyzer` returns parse error. This error is caused by the PowerShell parser not being able to find the symbol for `&lt;somemodule&gt;`.</maml:para>
            <maml:para>If `Invoke-ScriptAnalyzer` finds the module in the PowerShell Gallery, it downloads the missing module to a temp path. The temp path is then added to `$env:PSModulePath` for duration of the scan. The temp location can be found in `$LOCALAPPDATA/PSScriptAnalyzer/TempModuleDir`.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Profile">
          <maml:name>Settings</maml:name>
          <maml:description>
            <maml:para>A path to a file containing a user-defined profile or a hashtable object containing settings for ScriptAnalyzer.</maml:para>
            <maml:para>Runs `Invoke-ScriptAnalyzer` with the parameters and values specified in the file or hashtable.</maml:para>
            <maml:para>If the path or the content of the file or hashtable is invalid, it is ignored. The parameters and values in the profile take precedence over the same parameter and values specified at the command line.</maml:para>
            <maml:para>A Script Analyzer profile file is a text file that contains a hashtable with one or more of the following keys:</maml:para>
            <maml:para>- CustomRulePath</maml:para>
            <maml:para>- ExcludeRules</maml:para>
            <maml:para>- IncludeDefaultRules</maml:para>
            <maml:para>- IncludeRules</maml:para>
            <maml:para>- RecurseCustomRulePath</maml:para>
            <maml:para>- Rules</maml:para>
            <maml:para>- Severity</maml:para>
            <maml:para></maml:para>
            <maml:para>The keys and values in the profile are interpreted as if they were standard parameters and values of `Invoke-ScriptAnalyzer`, similar to splatting. For more information, see about_Splatting (https://learn.microsoft.com/powershell/module/microsoft.powershell.core/about/about_splatting).</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Severity</maml:name>
          <maml:description>
            <maml:para>After running Script Analyzer with all rules, this parameter selects rule violations with the specified severity.</maml:para>
            <maml:para>Valid values are:</maml:para>
            <maml:para>- Error</maml:para>
            <maml:para>- Warning</maml:para>
            <maml:para>- Information.</maml:para>
            <maml:para></maml:para>
            <maml:para>You can specify one ore more severity values.</maml:para>
            <maml:para>The parameter filters the rules violations only after running all rules. To filter rules efficiently, use `Get-ScriptAnalyzerRule` to select the rules you want to run.</maml:para>
            <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rule violations</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Invoke-ScriptAnalyzer</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="0" aliases="none">
          <maml:name>ScriptDefinition</maml:name>
          <maml:description>
            <maml:para>Runs the analysis on commands, functions, or expressions in a string. You can use this feature to analyze statements, expressions, and functions, independent of their script context.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="CustomizedRulePath">
          <maml:name>CustomRulePath</maml:name>
          <maml:description>
            <maml:para>Enter the path to a file that defines rules or a directory that contains files that define rules. Wildcard characters are supported. When CustomRulePath is specified, only the custom rules found in the specified paths are used for the analysis. If `Invoke-ScriptAnalyzer` cannot find rules in the , it runs the standard rules without notice.</maml:para>
            <maml:para>To add rules defined in subdirectories of the path, use the RecurseCustomRulePath parameter. To include the built-in rules, add the IncludeDefaultRules parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableExit</maml:name>
          <maml:description>
            <maml:para>On completion of the analysis, this parameter exits the PowerShell sessions and returns an exit code equal to the number of error records. This can be useful in continuous integration (CI) pipeline.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
          <maml:name>ExcludeRule</maml:name>
          <maml:description>
            <maml:para>Omits the specified rules from the Script Analyzer test. Wildcard characters are supported.</maml:para>
            <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. You can also specify a list of excluded rules in a Script Analyzer profile file. You can exclude standard rules and rules in a custom rule path.</maml:para>
            <maml:para>When you exclude a rule, the rule does not run on any of the files in the path. To exclude a rule on a particular line, parameter, function, script, or class, adjust the Path parameter or suppress the rule. For information about suppressing a rule, see the examples.</maml:para>
            <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules are included.</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeDefaultRules</maml:name>
          <maml:description>
            <maml:para>Invoke default rules along with Custom rules.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeRule</maml:name>
          <maml:description>
            <maml:para>Runs only the specified rules in the Script Analyzer test. By default, PSScriptAnalyzer runs all rules.</maml:para>
            <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. Wildcard characters are supported. You can also specify rule names in a Script Analyzer profile file.</maml:para>
            <maml:para>When you use the CustomizedRulePath parameter, you can use this parameter to include standard rules and rules in the custom rule paths.</maml:para>
            <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
            <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules are included.</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeSuppressed</maml:name>
          <maml:description>
            <maml:para>Include suppressed diagnostics in output.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Recurse</maml:name>
          <maml:description>
            <maml:para>Runs Script Analyzer on the files in the Path directory and all subdirectories recursively.</maml:para>
            <maml:para>Recurse applies only to the Path parameter value. To search the CustomRulePath recursively, use the RecurseCustomRulePath parameter.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RecurseCustomRulePath</maml:name>
          <maml:description>
            <maml:para>Adds rules defined in subdirectories of the CustomRulePath location. By default, `Invoke-ScriptAnalyzer` uses only the custom rules defined in the specified file or directory. To include the built-in rules, use the IncludeDefaultRules parameter.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ReportSummary</maml:name>
          <maml:description>
            <maml:para>Write a summary of the violations found to the host.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SaveDscDependency</maml:name>
          <maml:description>
            <maml:para>Resolve DSC resource dependencies.</maml:para>
            <maml:para>When `Invoke-ScriptAnalyzer` is run with this parameter, it looks for instances of `Import-DSCResource -ModuleName &lt;somemodule&gt;`. If `&lt;somemodule&gt;` is cannot be found by searching the `$env:PSModulePath`, `Invoke-ScriptAnalyzer` returns parse error. This error is caused by the PowerShell parser not being able to find the symbol for `&lt;somemodule&gt;`.</maml:para>
            <maml:para>If `Invoke-ScriptAnalyzer` finds the module in the PowerShell Gallery, it downloads the missing module to a temp path. The temp path is then added to `$env:PSModulePath` for duration of the scan. The temp location can be found in `$LOCALAPPDATA/PSScriptAnalyzer/TempModuleDir`.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Profile">
          <maml:name>Settings</maml:name>
          <maml:description>
            <maml:para>A path to a file containing a user-defined profile or a hashtable object containing settings for ScriptAnalyzer.</maml:para>
            <maml:para>Runs `Invoke-ScriptAnalyzer` with the parameters and values specified in the file or hashtable.</maml:para>
            <maml:para>If the path or the content of the file or hashtable is invalid, it is ignored. The parameters and values in the profile take precedence over the same parameter and values specified at the command line.</maml:para>
            <maml:para>A Script Analyzer profile file is a text file that contains a hashtable with one or more of the following keys:</maml:para>
            <maml:para>- CustomRulePath</maml:para>
            <maml:para>- ExcludeRules</maml:para>
            <maml:para>- IncludeDefaultRules</maml:para>
            <maml:para>- IncludeRules</maml:para>
            <maml:para>- RecurseCustomRulePath</maml:para>
            <maml:para>- Rules</maml:para>
            <maml:para>- Severity</maml:para>
            <maml:para></maml:para>
            <maml:para>The keys and values in the profile are interpreted as if they were standard parameters and values of `Invoke-ScriptAnalyzer`, similar to splatting. For more information, see about_Splatting (https://learn.microsoft.com/powershell/module/microsoft.powershell.core/about/about_splatting).</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Severity</maml:name>
          <maml:description>
            <maml:para>After running Script Analyzer with all rules, this parameter selects rule violations with the specified severity.</maml:para>
            <maml:para>Valid values are:</maml:para>
            <maml:para>- Error</maml:para>
            <maml:para>- Warning</maml:para>
            <maml:para>- Information.</maml:para>
            <maml:para></maml:para>
            <maml:para>You can specify one ore more severity values.</maml:para>
            <maml:para>The parameter filters the rules violations only after running all rules. To filter rules efficiently, use `Get-ScriptAnalyzerRule` to select the rules you want to run.</maml:para>
            <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rule violations</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Invoke-ScriptAnalyzer</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="0" aliases="none">
          <maml:name>ScriptDefinition</maml:name>
          <maml:description>
            <maml:para>Runs the analysis on commands, functions, or expressions in a string. You can use this feature to analyze statements, expressions, and functions, independent of their script context.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="CustomizedRulePath">
          <maml:name>CustomRulePath</maml:name>
          <maml:description>
            <maml:para>Enter the path to a file that defines rules or a directory that contains files that define rules. Wildcard characters are supported. When CustomRulePath is specified, only the custom rules found in the specified paths are used for the analysis. If `Invoke-ScriptAnalyzer` cannot find rules in the , it runs the standard rules without notice.</maml:para>
            <maml:para>To add rules defined in subdirectories of the path, use the RecurseCustomRulePath parameter. To include the built-in rules, add the IncludeDefaultRules parameter.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableExit</maml:name>
          <maml:description>
            <maml:para>On completion of the analysis, this parameter exits the PowerShell sessions and returns an exit code equal to the number of error records. This can be useful in continuous integration (CI) pipeline.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
          <maml:name>ExcludeRule</maml:name>
          <maml:description>
            <maml:para>Omits the specified rules from the Script Analyzer test. Wildcard characters are supported.</maml:para>
            <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. You can also specify a list of excluded rules in a Script Analyzer profile file. You can exclude standard rules and rules in a custom rule path.</maml:para>
            <maml:para>When you exclude a rule, the rule does not run on any of the files in the path. To exclude a rule on a particular line, parameter, function, script, or class, adjust the Path parameter or suppress the rule. For information about suppressing a rule, see the examples.</maml:para>
            <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules are included.</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeDefaultRules</maml:name>
          <maml:description>
            <maml:para>Invoke default rules along with Custom rules.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeRule</maml:name>
          <maml:description>
            <maml:para>Runs only the specified rules in the Script Analyzer test. By default, PSScriptAnalyzer runs all rules.</maml:para>
            <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. Wildcard characters are supported. You can also specify rule names in a Script Analyzer profile file.</maml:para>
            <maml:para>When you use the CustomizedRulePath parameter, you can use this parameter to include standard rules and rules in the custom rule paths.</maml:para>
            <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
            <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rules are included.</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Recurse</maml:name>
          <maml:description>
            <maml:para>Runs Script Analyzer on the files in the Path directory and all subdirectories recursively.</maml:para>
            <maml:para>Recurse applies only to the Path parameter value. To search the CustomRulePath recursively, use the RecurseCustomRulePath parameter.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RecurseCustomRulePath</maml:name>
          <maml:description>
            <maml:para>Adds rules defined in subdirectories of the CustomRulePath location. By default, `Invoke-ScriptAnalyzer` uses only the custom rules defined in the specified file or directory. To include the built-in rules, use the IncludeDefaultRules parameter.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ReportSummary</maml:name>
          <maml:description>
            <maml:para>Write a summary of the violations found to the host.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SaveDscDependency</maml:name>
          <maml:description>
            <maml:para>Resolve DSC resource dependencies.</maml:para>
            <maml:para>When `Invoke-ScriptAnalyzer` is run with this parameter, it looks for instances of `Import-DSCResource -ModuleName &lt;somemodule&gt;`. If `&lt;somemodule&gt;` is cannot be found by searching the `$env:PSModulePath`, `Invoke-ScriptAnalyzer` returns parse error. This error is caused by the PowerShell parser not being able to find the symbol for `&lt;somemodule&gt;`.</maml:para>
            <maml:para>If `Invoke-ScriptAnalyzer` finds the module in the PowerShell Gallery, it downloads the missing module to a temp path. The temp path is then added to `$env:PSModulePath` for duration of the scan. The temp location can be found in `$LOCALAPPDATA/PSScriptAnalyzer/TempModuleDir`.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Profile">
          <maml:name>Settings</maml:name>
          <maml:description>
            <maml:para>A path to a file containing a user-defined profile or a hashtable object containing settings for ScriptAnalyzer.</maml:para>
            <maml:para>Runs `Invoke-ScriptAnalyzer` with the parameters and values specified in the file or hashtable.</maml:para>
            <maml:para>If the path or the content of the file or hashtable is invalid, it is ignored. The parameters and values in the profile take precedence over the same parameter and values specified at the command line.</maml:para>
            <maml:para>A Script Analyzer profile file is a text file that contains a hashtable with one or more of the following keys:</maml:para>
            <maml:para>- CustomRulePath</maml:para>
            <maml:para>- ExcludeRules</maml:para>
            <maml:para>- IncludeDefaultRules</maml:para>
            <maml:para>- IncludeRules</maml:para>
            <maml:para>- RecurseCustomRulePath</maml:para>
            <maml:para>- Rules</maml:para>
            <maml:para>- Severity</maml:para>
            <maml:para></maml:para>
            <maml:para>The keys and values in the profile are interpreted as if they were standard parameters and values of `Invoke-ScriptAnalyzer`, similar to splatting. For more information, see about_Splatting (https://learn.microsoft.com/powershell/module/microsoft.powershell.core/about/about_splatting).</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Severity</maml:name>
          <maml:description>
            <maml:para>After running Script Analyzer with all rules, this parameter selects rule violations with the specified severity.</maml:para>
            <maml:para>Valid values are:</maml:para>
            <maml:para>- Error</maml:para>
            <maml:para>- Warning</maml:para>
            <maml:para>- Information.</maml:para>
            <maml:para></maml:para>
            <maml:para>You can specify one ore more severity values.</maml:para>
            <maml:para>The parameter filters the rules violations only after running all rules. To filter rules efficiently, use `Get-ScriptAnalyzerRule` to select the rules you want to run.</maml:para>
            <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
          </maml:description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>All rule violations</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SuppressedOnly</maml:name>
          <maml:description>
            <maml:para>Returns violations only for rules that are suppressed.</maml:para>
            <maml:para>Returns a SuppressedRecord object ( Microsoft.Windows.PowerShell.ScriptAnalyzer.Generic.SuppressedRecord ).</maml:para>
            <maml:para>To suppress a rule, use the SuppressMessageAttribute . For help, see the examples.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="CustomizedRulePath">
        <maml:name>CustomRulePath</maml:name>
        <maml:description>
          <maml:para>Enter the path to a file that defines rules or a directory that contains files that define rules. Wildcard characters are supported. When CustomRulePath is specified, only the custom rules found in the specified paths are used for the analysis. If `Invoke-ScriptAnalyzer` cannot find rules in the , it runs the standard rules without notice.</maml:para>
          <maml:para>To add rules defined in subdirectories of the path, use the RecurseCustomRulePath parameter. To include the built-in rules, add the IncludeDefaultRules parameter.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>EnableExit</maml:name>
        <maml:description>
          <maml:para>On completion of the analysis, this parameter exits the PowerShell sessions and returns an exit code equal to the number of error records. This can be useful in continuous integration (CI) pipeline.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
        <maml:name>ExcludeRule</maml:name>
        <maml:description>
          <maml:para>Omits the specified rules from the Script Analyzer test. Wildcard characters are supported.</maml:para>
          <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. You can also specify a list of excluded rules in a Script Analyzer profile file. You can exclude standard rules and rules in a custom rule path.</maml:para>
          <maml:para>When you exclude a rule, the rule does not run on any of the files in the path. To exclude a rule on a particular line, parameter, function, script, or class, adjust the Path parameter or suppress the rule. For information about suppressing a rule, see the examples.</maml:para>
          <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>All rules are included.</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Fix</maml:name>
        <maml:description>
          <maml:para>Fixes certain warnings that contain a fix in their DiagnosticRecord .</maml:para>
          <maml:para>When you used Fix , `Invoke-ScriptAnalyzer` applies the fixes before running the analysis. Make sure that you have a backup of your files when using this parameter. It tries to preserve the file encoding but there are still some cases where the encoding can change.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>IncludeDefaultRules</maml:name>
        <maml:description>
          <maml:para>Invoke default rules along with Custom rules.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="true" pipelineInput="False" position="named" aliases="none">
        <maml:name>IncludeRule</maml:name>
        <maml:description>
          <maml:para>Runs only the specified rules in the Script Analyzer test. By default, PSScriptAnalyzer runs all rules.</maml:para>
          <maml:para>Enter a comma-separated list of rule names, a variable that contains rule names, or a command that gets rule names. Wildcard characters are supported. You can also specify rule names in a Script Analyzer profile file.</maml:para>
          <maml:para>When you use the CustomizedRulePath parameter, you can use this parameter to include standard rules and rules in the custom rule paths.</maml:para>
          <maml:para>If a rule is specified in both the ExcludeRule and IncludeRule collections, the rule is excluded.</maml:para>
          <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>All rules are included.</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>IncludeSuppressed</maml:name>
        <maml:description>
          <maml:para>Include suppressed diagnostics in output.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="true" pipelineInput="True (ByPropertyName, ByValue)" position="0" aliases="PSPath">
        <maml:name>Path</maml:name>
        <maml:description>
          <maml:para>Specifies the path to the scripts or module to be analyzed. Wildcard characters are supported.</maml:para>
          <maml:para>Enter the path to a script (`.ps1`) or module file (`.psm1`) or to a directory that contains scripts or modules. If the directory contains other types of files, they are ignored.</maml:para>
          <maml:para>To analyze files that are not in the root directory of the specified path, use a wildcard character (`C:\Modules\MyModule\ `) or the Recurse * parameter.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Recurse</maml:name>
        <maml:description>
          <maml:para>Runs Script Analyzer on the files in the Path directory and all subdirectories recursively.</maml:para>
          <maml:para>Recurse applies only to the Path parameter value. To search the CustomRulePath recursively, use the RecurseCustomRulePath parameter.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>RecurseCustomRulePath</maml:name>
        <maml:description>
          <maml:para>Adds rules defined in subdirectories of the CustomRulePath location. By default, `Invoke-ScriptAnalyzer` uses only the custom rules defined in the specified file or directory. To include the built-in rules, use the IncludeDefaultRules parameter.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ReportSummary</maml:name>
        <maml:description>
          <maml:para>Write a summary of the violations found to the host.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>SaveDscDependency</maml:name>
        <maml:description>
          <maml:para>Resolve DSC resource dependencies.</maml:para>
          <maml:para>When `Invoke-ScriptAnalyzer` is run with this parameter, it looks for instances of `Import-DSCResource -ModuleName &lt;somemodule&gt;`. If `&lt;somemodule&gt;` is cannot be found by searching the `$env:PSModulePath`, `Invoke-ScriptAnalyzer` returns parse error. This error is caused by the PowerShell parser not being able to find the symbol for `&lt;somemodule&gt;`.</maml:para>
          <maml:para>If `Invoke-ScriptAnalyzer` finds the module in the PowerShell Gallery, it downloads the missing module to a temp path. The temp path is then added to `$env:PSModulePath` for duration of the scan. The temp location can be found in `$LOCALAPPDATA/PSScriptAnalyzer/TempModuleDir`.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="True (ByPropertyName, ByValue)" position="0" aliases="none">
        <maml:name>ScriptDefinition</maml:name>
        <maml:description>
          <maml:para>Runs the analysis on commands, functions, or expressions in a string. You can use this feature to analyze statements, expressions, and functions, independent of their script context.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Profile">
        <maml:name>Settings</maml:name>
        <maml:description>
          <maml:para>A path to a file containing a user-defined profile or a hashtable object containing settings for ScriptAnalyzer.</maml:para>
          <maml:para>Runs `Invoke-ScriptAnalyzer` with the parameters and values specified in the file or hashtable.</maml:para>
          <maml:para>If the path or the content of the file or hashtable is invalid, it is ignored. The parameters and values in the profile take precedence over the same parameter and values specified at the command line.</maml:para>
          <maml:para>A Script Analyzer profile file is a text file that contains a hashtable with one or more of the following keys:</maml:para>
          <maml:para>- CustomRulePath</maml:para>
          <maml:para>- ExcludeRules</maml:para>
          <maml:para>- IncludeDefaultRules</maml:para>
          <maml:para>- IncludeRules</maml:para>
          <maml:para>- RecurseCustomRulePath</maml:para>
          <maml:para>- Rules</maml:para>
          <maml:para>- Severity</maml:para>
          <maml:para></maml:para>
          <maml:para>The keys and values in the profile are interpreted as if they were standard parameters and values of `Invoke-ScriptAnalyzer`, similar to splatting. For more information, see about_Splatting (https://learn.microsoft.com/powershell/module/microsoft.powershell.core/about/about_splatting).</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
        <dev:type>
          <maml:name>Object</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Severity</maml:name>
        <maml:description>
          <maml:para>After running Script Analyzer with all rules, this parameter selects rule violations with the specified severity.</maml:para>
          <maml:para>Valid values are:</maml:para>
          <maml:para>- Error</maml:para>
          <maml:para>- Warning</maml:para>
          <maml:para>- Information.</maml:para>
          <maml:para></maml:para>
          <maml:para>You can specify one ore more severity values.</maml:para>
          <maml:para>The parameter filters the rules violations only after running all rules. To filter rules efficiently, use `Get-ScriptAnalyzerRule` to select the rules you want to run.</maml:para>
          <maml:para>The Severity parameter takes precedence over IncludeRule . For example, if Severity is `Error`, you cannot use IncludeRule to include a `Warning` rule.</maml:para>
        </maml:description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>All rule violations</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>SuppressedOnly</maml:name>
        <maml:description>
          <maml:para>Returns violations only for rules that are suppressed.</maml:para>
          <maml:para>Returns a SuppressedRecord object ( Microsoft.Windows.PowerShell.ScriptAnalyzer.Generic.SuppressedRecord ).</maml:para>
          <maml:para>To suppress a rule, use the SuppressMessageAttribute . For help, see the examples.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
        <maml:name>Confirm</maml:name>
        <maml:description>
          <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
        <maml:name>WhatIf</maml:name>
        <maml:description>
          <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
        </maml:description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>You cannot pipe input to this cmdlet.</maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Windows.PowerShell.ScriptAnalyzer.Generic.DiagnosticRecord</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>By default, `Invoke-ScriptAnalyzer` returns one DiagnosticRecord object for each rule violation.</maml:para>
        </maml:description>
      </command:returnValue>
      <command:returnValue>
        <dev:type>
          <maml:name>Microsoft.Windows.PowerShell.ScriptAnalyzer.Generic.SuppressedRecord</maml:name>
        </dev:type>
        <maml:description>
          <maml:para>If you use the SuppressedOnly parameter, `Invoke-ScriptAnalyzer` instead returns a SuppressedRecord objects.</maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---- EXAMPLE 1 - Run all Script Analyzer rules on a script ----</maml:title>
        <dev:code>Invoke-ScriptAnalyzer -Path C:\Scripts\Get-LogData.ps1</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>EXAMPLE 2 - Run all Script Analyzer rules on all files in the Modules directory</maml:title>
        <dev:code>Invoke-ScriptAnalyzer -Path $home\Documents\WindowsPowerShell\Modules -Recurse</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>---------- EXAMPLE 3 - Run a single rule on a module ----------</maml:title>
        <dev:code>Invoke-ScriptAnalyzer -Path C:\Windows\System32\WindowsPowerShell\v1.0\Modules\PSDiagnostics -IncludeRule PSAvoidUsingPositionalParameters</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>----- EXAMPLE 4 - Run all rules except two on your modules -----</maml:title>
        <dev:code>Invoke-ScriptAnalyzer -Path C:\ps-test\MyModule -Recurse -ExcludeRule PSAvoidUsingCmdletAliases, PSAvoidUsingInternalURLs</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------ EXAMPLE 5 - Run Script Analyzer with custom rules ------</maml:title>
        <dev:code>Invoke-ScriptAnalyzer -Path D:\test_scripts\Test-Script.ps1 -CustomRulePath C:\CommunityAnalyzerRules -IncludeDefaultRules</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>EXAMPLE 6 - Run only the rules that are Error severity and have the PSDSC source name</maml:title>
        <dev:code>$DSCError = Get-ScriptAnalyzerRule -Severity Error | Where SourceName -eq PSDSC
$Path = "$home\Documents\WindowsPowerShell\Modules\MyDSCModule"
Invoke-ScriptAnalyzerRule -Path $Path -IncludeRule $DSCError -Recurse</dev:code>
        <dev:remarks>
          <maml:para></maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>----------- EXAMPLE 7 - Suppressing rule violations -----------</maml:title>
        <dev:code>function Get-Widgets
{
    [CmdletBinding()]
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("PSUseSingularNouns", "")]
    [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("PSAvoidUsingCmdletAliases", "", Justification="Resolution in progress.")]
    Param()

    dir $pshome
    ...
}

Invoke-ScriptAnalyzer -Path .\Get-Widgets.ps1

RuleName                            Severity     FileName   Line  Message
--------                            --------     --------   ----  -------
PSProvideCommentHelp                Information  ManageProf 14    The cmdlet 'Get-Widget' does not have a help comment.
                                                 iles.psm1

Invoke-ScriptAnalyzer -Path .\Get-Widgets.ps1 -SuppressedOnly

Rule Name                           Severity     File Name  Line  Justification
---------                           --------     ---------  ----  -------------
PSAvoidUsingCmdletAliases           Warning      ManageProf 21    Resolution in progress.
                                                 iles.psm1
PSUseSingularNouns                  Warning      ManageProf 14
                                                 iles.psm1</dev:code>
        <dev:remarks>
          <maml:para>The second command uses the SuppressedOnly parameter to report violations of the rules that are suppressed script file.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>- EXAMPLE 8 - Analyze script files using a profile definition -</maml:title>
        <dev:code># In .\ScriptAnalyzerProfile.txt
@{
    Severity = @('Error', 'Warning')
    IncludeRules = 'PSAvoid*'
    ExcludeRules = '*WriteHost'
}

Invoke-ScriptAnalyzer -Path $pshome\Modules\BitLocker -Settings .\ScriptAnalyzerProfile.txt</dev:code>
        <dev:remarks>
          <maml:para>If you include a conflicting parameter in the `Invoke-ScriptAnalyzer` command, such as `-Severity Error`, the cmdlet uses the profile value and ignores the parameter.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------- EXAMPLE 9 - Analyze a script stored as a string -------</maml:title>
        <dev:code>Invoke-ScriptAnalyzer -ScriptDefinition "function Get-Widgets {Write-Host 'Hello'}"

RuleName                            Severity     FileName   Line  Message
--------                            --------     --------   ----  -------
PSAvoidUsingWriteHost               Warning                 1     Script
                                                                  because
                                                                  there i
                                                                  suppres
                                                                  Write-O
PSUseSingularNouns                  Warning                 1     The cmd
                                                                  noun sh</dev:code>
        <dev:remarks>
          <maml:para>When you use the ScriptDefinition parameter, the FileName property of the DiagnosticRecord object is `$null`.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Version:</maml:linkText>
        <maml:uri>https://learn.microsoft.com/powershell/module/psscriptanalyzer/invoke-scriptanalyzer?view=ps-modules&amp;wt.mc_id=ps-gethelp</maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>Get-ScriptAnalyzerRule</maml:linkText>
        <maml:uri></maml:uri>
      </maml:navigationLink>
      <maml:navigationLink>
        <maml:linkText>PSScriptAnalyzer on GitHub</maml:linkText>
        <maml:uri>https://github.com/PowerShell/PSScriptAnalyzer</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
</helpItems>