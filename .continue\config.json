{"models": [{"title": "Qwen3-30B-Python-Coder", "provider": "lmstudio", "model": "Qwen3-30B-A3B-python-coder.i1-Q4_K_S", "modelPath": "C:\\Users\\<USER>\\.lmstudio\\models\\mradermacher\\Qwen3-30B-A3B-python-coder-i1-GGUF\\Qwen3-30B-A3B-python-coder.i1-Q4_K_S.gguf", "apiBase": "http://localhost:1234/v1", "apiKey": "lm-studio", "contextLength": 32768, "maxTokens": 4096, "temperature": 0.1, "topP": 0.9, "topK": 40, "presencePenalty": 0.1, "frequencyPenalty": 0.1, "systemMessage": "Você é um agente de programação avançado com capacidades similares ao Augment Agent. Você tem acesso a ferramentas de análise de código, refatoração, geração de testes e debugging. Sempre analise o contexto completo antes de fazer alterações e garanta que não haja conflitos entre arquivos.", "supportsImages": false, "supportsTools": true, "supportsFim": true}], "tabAutocompleteModel": {"title": "Qwen3-30B-Autocomplete", "provider": "lmstudio", "model": "Qwen3-30B-A3B-python-coder.i1-Q4_K_S", "apiBase": "http://localhost:1234/v1", "apiKey": "lm-studio", "contextLength": 8192, "maxTokens": 256, "temperature": 0.2, "topP": 0.95}, "embeddingsProvider": {"provider": "transformers.js", "model": "all-MiniLM-L6-v2", "maxChunkSize": 512, "chunkOverlap": 50}, "contextProviders": [{"name": "code", "params": {"maxResults": 25, "includeSignatures": true, "includeDocstrings": true}}, {"name": "diff", "params": {}}, {"name": "terminal", "params": {}}, {"name": "problems", "params": {}}, {"name": "folder", "params": {"maxDepth": 3}}, {"name": "codebase", "params": {"nRetrieve": 25, "nFinal": 10, "useReranking": true}}], "slashCommands": [{"name": "edit", "description": "Editar código selecionado com análise de contexto"}, {"name": "comment", "description": "Adicionar coment<PERSON><PERSON>s detalhados ao código"}, {"name": "share", "description": "Compartilhar código com análise"}, {"name": "cmd", "description": "Executar comando no terminal"}, {"name": "commit", "description": "Gerar mensagem de commit inteligente"}], "customCommands": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "prompt": "Analise este código considerando: 1) Estrutura e arquitetura, 2) Possíveis bugs, 3) Oportunidades de otimização, 4) Conformidade com boas práticas, 5) Impacto em outros arquivos", "description": "Análise completa de código com verificação de impacto"}], "allowAnonymousTelemetry": false, "experimentalFeatures": {"contextRetrieval": true, "codebaseIndexing": true, "smartRefactoring": true, "errorPrevention": true, "agentMode": true, "advancedFileEditing": true, "webSearchIntegration": true}, "agentConfig": {"enabled": true, "version": "1.0.0", "capabilities": ["file-editing", "web-search", "codebase-analysis", "task-management", "code-refactoring", "test-generation", "debugging"]}, "tools": [{"type": "function", "function": {"name": "edit_file", "description": "Edit files with string replacement or insertion", "parameters": {"type": "object", "properties": {"filepath": {"type": "string", "description": "Path to the file to edit"}, "operation": {"type": "string", "enum": ["replace", "insert"], "description": "Type of edit operation"}, "old_str": {"type": "string", "description": "String to replace (for replace operation)"}, "new_str": {"type": "string", "description": "New string content"}}, "required": ["filepath", "operation", "new_str"]}}}, {"type": "function", "function": {"name": "web_search", "description": "Search the web for information", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}}, "required": ["query"]}}}, {"type": "function", "function": {"name": "codebase_search", "description": "Search the codebase for relevant code", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query for codebase"}}, "required": ["query"]}}}]}