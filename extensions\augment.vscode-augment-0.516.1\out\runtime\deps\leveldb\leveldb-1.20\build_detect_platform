#!/bin/sh
#
# Detects OS we're compiling on and outputs a file specified by the first
# argument, which in turn gets read while processing <PERSON><PERSON><PERSON>.
#
# The output will set the following variables:
#   CC                          C Compiler path
#   CXX                         C++ Compiler path
#   PLATFORM_LDFLAGS            Linker flags
#   PLATFORM_LIBS               Libraries flags
#   PLATFORM_SHARED_EXT         Extension for shared libraries
#   PLATFORM_SHARED_LDFLAGS     Flags for building shared library
#                               This flag is embedded just before the name
#                               of the shared library without intervening spaces
#   PLATFORM_SHARED_CFLAGS      Flags for compiling objects for shared library
#   PLATFORM_CCFLAGS            C compiler flags
#   PLATFORM_CXXFLAGS           C++ compiler flags.  Will contain:
#   PLATFORM_SHARED_VERSIONED   Set to 'true' if platform supports versioned
#                               shared libraries, empty otherwise.
#
# The PLATFORM_CCFLAGS and PLATFORM_CXXFLAGS might include the following:
#
#       -DLEVELDB_ATOMIC_PRESENT     if <atomic> is present
#       -DLEVELDB_PLATFORM_POSIX     for Posix-based platforms
#       -DSNAPPY                     if the Snappy library is present
#

OUTPUT=$1
PREFIX=$2
if test -z "$OUTPUT" || test -z "$PREFIX"; then
  echo "usage: $0 <output-filename> <directory_prefix>" >&2
  exit 1
fi

# Delete existing output, if it exists
rm -f $OUTPUT
touch $OUTPUT

if test -z "$CC"; then
    CC=cc
fi

if test -z "$CXX"; then
    CXX=g++
fi

if test -z "$TMPDIR"; then
    TMPDIR=/tmp
fi

# Detect OS
if test -z "$TARGET_OS"; then
    TARGET_OS=`uname -s`
fi

COMMON_FLAGS=
CROSS_COMPILE=
PLATFORM_CCFLAGS=
PLATFORM_CXXFLAGS=
PLATFORM_LDFLAGS=
PLATFORM_LIBS=
PLATFORM_SHARED_EXT="so"
PLATFORM_SHARED_LDFLAGS="-shared -Wl,-soname -Wl,"
PLATFORM_SHARED_CFLAGS="-fPIC"
PLATFORM_SHARED_VERSIONED=true
PLATFORM_SSEFLAGS=

MEMCMP_FLAG=
if [ "$CXX" = "g++" ]; then
    # Use libc's memcmp instead of GCC's memcmp.  This results in ~40%
    # performance improvement on readrandom under gcc 4.4.3 on Linux/x86.
    MEMCMP_FLAG="-fno-builtin-memcmp"
fi

case "$TARGET_OS" in
    CYGWIN_*)
        PLATFORM=OS_LINUX
        COMMON_FLAGS="$MEMCMP_FLAG -lpthread -DOS_LINUX -DCYGWIN"
        PLATFORM_LDFLAGS="-lpthread"
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        ;;
    Darwin)
        PLATFORM=OS_MACOSX
        COMMON_FLAGS="$MEMCMP_FLAG -DOS_MACOSX"
        PLATFORM_SHARED_EXT=dylib
        [ -z "$INSTALL_PATH" ] && INSTALL_PATH=`pwd`
        PLATFORM_SHARED_LDFLAGS="-dynamiclib -install_name $INSTALL_PATH/"
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        ;;
    Linux)
        PLATFORM=OS_LINUX
        COMMON_FLAGS="$MEMCMP_FLAG -pthread -DOS_LINUX"
        PLATFORM_LDFLAGS="-pthread"
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        ;;
    SunOS)
        PLATFORM=OS_SOLARIS
        COMMON_FLAGS="$MEMCMP_FLAG -D_REENTRANT -DOS_SOLARIS"
        PLATFORM_LIBS="-lpthread -lrt"
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        ;;
    FreeBSD)
        PLATFORM=OS_FREEBSD
        COMMON_FLAGS="$MEMCMP_FLAG -D_REENTRANT -DOS_FREEBSD"
        PLATFORM_LIBS="-lpthread"
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        ;;
    NetBSD)
        PLATFORM=OS_NETBSD
        COMMON_FLAGS="$MEMCMP_FLAG -D_REENTRANT -DOS_NETBSD"
        PLATFORM_LIBS="-lpthread -lgcc_s"
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        ;;
    OpenBSD)
        PLATFORM=OS_OPENBSD
        COMMON_FLAGS="$MEMCMP_FLAG -D_REENTRANT -DOS_OPENBSD"
        PLATFORM_LDFLAGS="-pthread"
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        ;;
    DragonFly)
        PLATFORM=OS_DRAGONFLYBSD
        COMMON_FLAGS="$MEMCMP_FLAG -D_REENTRANT -DOS_DRAGONFLYBSD"
        PLATFORM_LIBS="-lpthread"
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        ;;
    OS_ANDROID_CROSSCOMPILE)
        PLATFORM=OS_ANDROID
        COMMON_FLAGS="$MEMCMP_FLAG -D_REENTRANT -DOS_ANDROID -DLEVELDB_PLATFORM_POSIX"
        PLATFORM_LDFLAGS=""  # All pthread features are in the Android C library
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        CROSS_COMPILE=true
        ;;
    HP-UX)
        PLATFORM=OS_HPUX
        COMMON_FLAGS="$MEMCMP_FLAG -D_REENTRANT -DOS_HPUX"
        PLATFORM_LDFLAGS="-pthread"
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        # man ld: +h internal_name
        PLATFORM_SHARED_LDFLAGS="-shared -Wl,+h -Wl,"
        ;;
    IOS)
        PLATFORM=IOS
        COMMON_FLAGS="$MEMCMP_FLAG -DOS_MACOSX"
        [ -z "$INSTALL_PATH" ] && INSTALL_PATH=`pwd`
        PORT_FILE=port/port_posix.cc
        PORT_SSE_FILE=port/port_posix_sse.cc
        PLATFORM_SHARED_EXT=
        PLATFORM_SHARED_LDFLAGS=
        PLATFORM_SHARED_CFLAGS=
        PLATFORM_SHARED_VERSIONED=
        ;;
    *)
        echo "Unknown platform!" >&2
        exit 1
esac

# We want to make a list of all cc files within util, db, table, and helpers
# except for the test and benchmark files. By default, find will output a list
# of all files matching either rule, so we need to append -print to make the
# prune take effect.
DIRS="$PREFIX/db $PREFIX/util $PREFIX/table"

set -f # temporarily disable globbing so that our patterns aren't expanded
PRUNE_TEST="-name *test*.cc -prune"
PRUNE_BENCH="-name *_bench.cc -prune"
PRUNE_TOOL="-name leveldbutil.cc -prune"
PORTABLE_FILES=`find $DIRS $PRUNE_TEST -o $PRUNE_BENCH -o $PRUNE_TOOL -o -name '*.cc' -print | sort | sed "s,^$PREFIX/,," | tr "\n" " "`

set +f # re-enable globbing

# The sources consist of the portable files, plus the platform-specific port
# file.
echo "SOURCES=$PORTABLE_FILES $PORT_FILE $PORT_SSE_FILE" >> $OUTPUT
echo "MEMENV_SOURCES=helpers/memenv/memenv.cc" >> $OUTPUT

if [ "$CROSS_COMPILE" = "true" ]; then
    # Cross-compiling; do not try any compilation tests.
    true
else
    CXXOUTPUT="${TMPDIR}/leveldb_build_detect_platform-cxx.$$"

    # If -std=c++0x works, use <atomic> as fallback for when memory barriers
    # are not available.
    $CXX $CXXFLAGS -std=c++0x -x c++ - -o $CXXOUTPUT 2>/dev/null  <<EOF
      #include <atomic>
      int main() {}
EOF
    if [ "$?" = 0 ]; then
        COMMON_FLAGS="$COMMON_FLAGS -DLEVELDB_PLATFORM_POSIX -DLEVELDB_ATOMIC_PRESENT"
        PLATFORM_CXXFLAGS="-std=c++0x"
    else
        COMMON_FLAGS="$COMMON_FLAGS -DLEVELDB_PLATFORM_POSIX"
    fi

    # Test whether Snappy library is installed
    # http://code.google.com/p/snappy/
    $CXX $CXXFLAGS -x c++ - -o $CXXOUTPUT 2>/dev/null  <<EOF
      #include <snappy.h>
      int main() {}
EOF
    if [ "$?" = 0 ]; then
        COMMON_FLAGS="$COMMON_FLAGS -DSNAPPY"
        PLATFORM_LIBS="$PLATFORM_LIBS -lsnappy"
    fi

    # Test whether tcmalloc is available
    $CXX $CXXFLAGS -x c++ - -o $CXXOUTPUT -ltcmalloc 2>/dev/null  <<EOF
      int main() {}
EOF
    if [ "$?" = 0 ]; then
        PLATFORM_LIBS="$PLATFORM_LIBS -ltcmalloc"
    fi

    rm -f $CXXOUTPUT 2>/dev/null

    # Test if gcc SSE 4.2 is supported
    $CXX $CXXFLAGS -x c++ - -o $CXXOUTPUT -msse4.2 2>/dev/null  <<EOF
      int main() {}
EOF
    if [ "$?" = 0 ]; then
        PLATFORM_SSEFLAGS="-msse4.2"
    fi

    rm -f $CXXOUTPUT 2>/dev/null
fi

# Use the SSE 4.2 CRC32C intrinsics iff runtime checks indicate compiler supports them.
if [ -n "$PLATFORM_SSEFLAGS" ]; then
    PLATFORM_SSEFLAGS="$PLATFORM_SSEFLAGS -DLEVELDB_PLATFORM_POSIX_SSE"
fi

PLATFORM_CCFLAGS="$PLATFORM_CCFLAGS $COMMON_FLAGS"
PLATFORM_CXXFLAGS="$PLATFORM_CXXFLAGS $COMMON_FLAGS"

echo "CC=$CC" >> $OUTPUT
echo "CXX=$CXX" >> $OUTPUT
echo "PLATFORM=$PLATFORM" >> $OUTPUT
echo "PLATFORM_LDFLAGS=$PLATFORM_LDFLAGS" >> $OUTPUT
echo "PLATFORM_LIBS=$PLATFORM_LIBS" >> $OUTPUT
echo "PLATFORM_CCFLAGS=$PLATFORM_CCFLAGS" >> $OUTPUT
echo "PLATFORM_CXXFLAGS=$PLATFORM_CXXFLAGS" >> $OUTPUT
echo "PLATFORM_SSEFLAGS=$PLATFORM_SSEFLAGS" >> $OUTPUT
echo "PLATFORM_SHARED_CFLAGS=$PLATFORM_SHARED_CFLAGS" >> $OUTPUT
echo "PLATFORM_SHARED_EXT=$PLATFORM_SHARED_EXT" >> $OUTPUT
echo "PLATFORM_SHARED_LDFLAGS=$PLATFORM_SHARED_LDFLAGS" >> $OUTPUT
echo "PLATFORM_SHARED_VERSIONED=$PLATFORM_SHARED_VERSIONED" >> $OUTPUT
