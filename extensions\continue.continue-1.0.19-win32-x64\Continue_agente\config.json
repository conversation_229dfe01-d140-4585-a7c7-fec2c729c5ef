{"models": [{"title": "Qwen3-30B-Python-Coder", "provider": "lmstudio", "model": "Qwen3-30B-A3B-python-coder.i1-Q4_K_S", "modelPath": "C:\\Users\\<USER>\\.lmstudio\\models\\mradermacher\\Qwen3-30B-A3B-python-coder-i1-GGUF\\Qwen3-30B-A3B-python-coder.i1-Q4_K_S.gguf", "apiBase": "http://localhost:1234/v1", "apiKey": "lm-studio", "contextLength": 32768, "maxTokens": 4096, "temperature": 0.1, "topP": 0.9, "topK": 40, "presencePenalty": 0.1, "frequencyPenalty": 0.1, "systemMessage": "Você é um agente de programação avançado com capacidades similares ao Augment Agent. Você tem acesso a ferramentas de análise de código, refatoração, geração de testes e debugging. Sempre analise o contexto completo antes de fazer alterações e garanta que não haja conflitos entre arquivos.", "capabilities": {"codeAnalysis": true, "contextRetrieval": true, "refactoring": true, "testGeneration": true, "debugging": true, "optimization": true, "errorPrevention": true}}], "tabAutocompleteModel": {"title": "Qwen3-30B-Autocomplete", "provider": "lmstudio", "model": "Qwen3-30B-A3B-python-coder.i1-Q4_K_S", "apiBase": "http://localhost:1234/v1", "apiKey": "lm-studio", "contextLength": 8192, "maxTokens": 256, "temperature": 0.2, "topP": 0.95}, "embeddingsProvider": {"provider": "transformers.js", "model": "all-MiniLM-L6-v2", "maxChunkSize": 512, "chunkOverlap": 50}, "contextProviders": [{"name": "code", "params": {"maxResults": 25, "includeSignatures": true, "includeDocstrings": true}}, {"name": "diff", "params": {}}, {"name": "terminal", "params": {}}, {"name": "problems", "params": {}}, {"name": "folder", "params": {"maxDepth": 3}}, {"name": "codebase", "params": {"nRetrieve": 25, "nFinal": 10, "useReranking": true}}], "slashCommands": [{"name": "edit", "description": "Editar código selecionado com análise de contexto"}, {"name": "comment", "description": "Adicionar coment<PERSON><PERSON>s detalhados ao código"}, {"name": "share", "description": "Compartilhar código com análise"}, {"name": "cmd", "description": "Executar comando no terminal"}, {"name": "commit", "description": "Gerar mensagem de commit inteligente"}, {"name": "analyze", "description": "Análise profunda de código e arquitetura"}, {"name": "refactor", "description": "Refatoração inteligente com preservação de funcionalidade"}, {"name": "test", "description": "Geração automática de testes unitários"}, {"name": "debug", "description": "Análise e correção de bugs"}, {"name": "optimize", "description": "Otimização de performance e código"}], "customCommands": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "prompt": "Analise este código considerando: 1) Estrutura e arquitetura, 2) Possíveis bugs, 3) Oportunidades de otimização, 4) Conformidade com boas práticas, 5) Impacto em outros arquivos", "description": "Análise completa de código com verificação de impacto"}, {"name": "Refatoração Segura", "prompt": "Refatore este código mantendo a funcionalidade original. Antes de sugerir mudanças, analise todas as dependências e possíveis impactos em outros arquivos.", "description": "Refatoração com análise de impacto"}, {"name": "Geração de Testes", "prompt": "Gere testes unitários abrangentes para este código, incluindo casos edge, testes de erro e mocks quando necessário.", "description": "Geração automática de testes"}, {"name": "Editar <PERSON>", "prompt": "Edite o arquivo especificado usando as ferramentas de edição avançadas. Sempre faça backup antes de editar e valide a sintaxe após as mudanças.", "description": "Edição segura de arquivos com backup"}, {"name": "<PERSON><PERSON>r <PERSON>", "prompt": "Crie uma estrutura de projeto completa com arquivos e diretórios organizados seguindo as melhores práticas.", "description": "Criação de estrutura de projeto"}, {"name": "Busca Web Avançada", "prompt": "Busque informações na web usando múltiplos provedores e analise os resultados para fornecer informações precisas e atualizadas.", "description": "Busca web com múltiplos provedores"}, {"name": "Análise de Codebase", "prompt": "Analise todo o codebase para entender a arquitetura, dependências e possíveis melhorias. Use o contexto engine para análise profunda.", "description": "Análise completa do codebase"}], "allowAnonymousTelemetry": false, "experimentalFeatures": {"contextRetrieval": true, "codebaseIndexing": true, "smartRefactoring": true, "errorPrevention": true}}