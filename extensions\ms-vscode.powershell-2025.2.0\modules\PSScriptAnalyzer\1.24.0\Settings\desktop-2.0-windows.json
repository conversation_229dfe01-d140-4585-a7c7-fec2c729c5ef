﻿{
    "Modules":  [
                    {
                        "Name":  "ADRMS",
                        "Version":  "1.0.0.0",
                        "ExportedCommands":  [
                                                 {
                                                     "Name":  "Install-ADRMS",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-ADFSUrl] \u003cString\u003e [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Uninstall-ADRMS",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ADFSOnly] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Update-ADRMS",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ServiceAccount] \u003cPSCredential\u003e [[-PrivateKeyPassword] \u003cSecureString\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 }
                                             ],
                        "ExportedAliases":  [

                                            ]
                    },
                    {
                        "Name":  "AppLocker",
                        "Version":  "1.0.0.0",
                        "ExportedCommands":  [
                                                 {
                                                     "Name":  "Get-AppLockerFileInformation",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cList`1\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -Directory \u003cString\u003e [-FileType \u003cList`1\u003e] [-Recurse] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -EventLog [-LogPath \u003cString\u003e] [-EventType \u003cList`1\u003e] [-Statistics] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-AppLockerPolicy",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "-Local [-Xml] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -Domain -Ldap \u003cString\u003e [-Xml] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -Effective [-Xml] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-AppLockerPolicy",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-FileInformation] \u003cList`1\u003e [-RuleType \u003cList`1\u003e] [-RuleNamePrefix \u003cString\u003e] [-User \u003cString\u003e] [-Optimize] [-IgnoreMissingFileInformation] [-Xml] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-AppLockerPolicy",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-XmlPolicy] \u003cString\u003e [-Ldap \u003cString\u003e] [-Merge] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PolicyObject] \u003cAppLockerPolicy\u003e [-Ldap \u003cString\u003e] [-Merge] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Test-AppLockerPolicy",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-XmlPolicy] \u003cString\u003e -Path \u003cList`1\u003e [-User \u003cString\u003e] [-Filter \u003cList`1\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-PolicyObject] \u003cAppLockerPolicy\u003e -Path \u003cList`1\u003e [-User \u003cString\u003e] [-Filter \u003cList`1\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 }
                                             ],
                        "ExportedAliases":  [

                                            ]
                    },
                    {
                        "Name":  "BestPractices",
                        "Version":  "1.0",
                        "ExportedCommands":  [
                                                 {
                                                     "Name":  "Get-BpaModel",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-BestPracticesModelId] \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-BpaResult",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BestPracticesModelId] \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Invoke-BpaModel",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BestPracticesModelId] \u003cString\u003e [[-ArgumentTable] \u003cHashtable\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-BpaResult",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BestPracticesModelId] \u003cString\u003e [-Exclude] [-Results] \u003cList`1\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 }
                                             ],
                        "ExportedAliases":  [

                                            ]
                    },
                    {
                        "Name":  "BitsTransfer",
                        "Version":  "1.0.0.0",
                        "ExportedCommands":  [
                                                 {
                                                     "Name":  "Add-BitsFile",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BitsJob] \u003cBitsJob[]\u003e [-Source] \u003cString[]\u003e [[-Destination] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Complete-BitsTransfer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BitsJob] \u003cBitsJob[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Get-BitsTransfer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-AllUsers] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-JobId] \u003cGuid[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Remove-BitsTransfer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BitsJob] \u003cBitsJob[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Resume-BitsTransfer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BitsJob] \u003cBitsJob[]\u003e [-Asynchronous] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Set-BitsTransfer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BitsJob] \u003cBitsJob[]\u003e [-DisplayName \u003cString\u003e] [-Priority \u003cString\u003e] [-Description \u003cString\u003e] [-ProxyAuthentication \u003cString\u003e] [-RetryInterval \u003cInt32\u003e] [-RetryTimeout \u003cInt32\u003e] [-Credential \u003cPSCredential\u003e] [-ProxyCredential \u003cPSCredential\u003e] [-Authentication \u003cString\u003e] [-SetOwnerToCurrentUser] [-ProxyUsage \u003cString\u003e] [-ProxyList \u003cUri[]\u003e] [-ProxyBypass \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Start-BitsTransfer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Source] \u003cString[]\u003e [[-Destination] \u003cString[]\u003e] [-Asynchronous] [-Authentication \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Description \u003cString\u003e] [-DisplayName \u003cString\u003e] [-Priority \u003cString\u003e] [-ProxyAuthentication \u003cString\u003e] [-ProxyBypass \u003cString[]\u003e] [-ProxyCredential \u003cPSCredential\u003e] [-ProxyList \u003cUri[]\u003e] [-ProxyUsage \u003cString\u003e] [-RetryInterval \u003cInt32\u003e] [-RetryTimeout \u003cInt32\u003e] [-Suspended] [-TransferType \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Suspend-BitsTransfer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BitsJob] \u003cBitsJob[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 }
                                             ],
                        "ExportedAliases":  [

                                            ]
                    },
                    {
                        "Name":  "PSDiagnostics",
                        "Version":  "1.0.0.0",
                        "ExportedCommands":  [
                                                 {
                                                     "Name":  "Disable-PSTrace",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  ""
                                                 },
                                                 {
                                                     "Name":  "Disable-PSWSManCombinedTrace",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  ""
                                                 },
                                                 {
                                                     "Name":  "Disable-WSManTrace",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  ""
                                                 },
                                                 {
                                                     "Name":  "Enable-PSTrace",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  ""
                                                 },
                                                 {
                                                     "Name":  "Enable-PSWSManCombinedTrace",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  ""
                                                 },
                                                 {
                                                     "Name":  "Enable-WSManTrace",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  ""
                                                 },
                                                 {
                                                     "Name":  "Get-LogProperties",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  "[-Name] \u003cObject\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-LogProperties",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  "[-LogDetails] \u003cLogDetails\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Start-Trace",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  "[-SessionName] \u003cString\u003e [[-OutputFilePath] \u003cString\u003e] [[-ProviderFilePath] \u003cString\u003e] [-ETS] [-Format \u003cObject\u003e] [-MinBuffers \u003cInt32\u003e] [-MaxBuffers \u003cInt32\u003e] [-BufferSizeInKB \u003cInt32\u003e] [-MaxLogFileSizeInMB \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Stop-Trace",
                                                     "CommandType":  "Function",
                                                     "ParameterSets":  "[-SessionName] \u003cObject\u003e [-ETS] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 }
                                             ],
                        "ExportedAliases":  [

                                            ]
                    },
                    {
                        "Name":  "ServerManager",
                        "Version":  "1.0.0.0",
                        "ExportedCommands":  [
                                                 {
                                                     "Name":  "Add-WindowsFeature",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cFeature[]\u003e [-IncludeAllSubFeature] [-LogPath \u003cString\u003e] [-Concurrent] [-Restart] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Get-WindowsFeature",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-LogPath \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Remove-WindowsFeature",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cFeature[]\u003e [-LogPath \u003cString\u003e] [-Concurrent] [-Restart] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 }
                                             ],
                        "ExportedAliases":  [

                                            ]
                    },
                    {
                        "Name":  "TroubleshootingPack",
                        "Version":  "1.0.0.0",
                        "ExportedCommands":  [
                                                 {
                                                     "Name":  "Get-TroubleshootingPack",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [-AnswerFile \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Invoke-TroubleshootingPack",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Pack] \u003cDiagPack\u003e [-AnswerFile \u003cString\u003e] [-Result \u003cString\u003e] [-Unattended] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 }
                                             ],
                        "ExportedAliases":  [

                                            ]
                    },
                    {
                        "Name":  "Microsoft.PowerShell.Core",
                        "Version":  "2.0",
                        "ExportedCommands":  [
                                                 {
                                                     "Name":  "Export-Counter",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [-FileFormat \u003cString\u003e] [-MaxSize \u003cUInt32\u003e] -InputObject \u003cPerformanceCounterSampleSet[]\u003e [-Force] [-Circular] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Counter",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Counter] \u003cString[]\u003e] [-SampleInterval \u003cInt32\u003e] [-MaxSamples \u003cInt64\u003e] [-Continuous] [-ComputerName \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ListSet] \u003cString[]\u003e [-ComputerName \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-WinEvent",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-LogName] \u003cString[]\u003e] [-MaxEvents \u003cInt64\u003e] [-ComputerName \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-FilterXPath \u003cString\u003e] [-Force] [-Oldest] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ListLog] \u003cString[]\u003e [-ComputerName \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ListProvider] \u003cString[]\u003e [-ComputerName \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ProviderName] \u003cString[]\u003e [-MaxEvents \u003cInt64\u003e] [-ComputerName \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-FilterXPath \u003cString\u003e] [-Force] [-Oldest] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Path] \u003cString[]\u003e [-MaxEvents \u003cInt64\u003e] [-Credential \u003cPSCredential\u003e] [-FilterXPath \u003cString\u003e] [-Oldest] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-FilterXml] \u003cXmlDocument\u003e [-MaxEvents \u003cInt64\u003e] [-ComputerName \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Oldest] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-FilterHashtable] \u003cHashtable[]\u003e [-MaxEvents \u003cInt64\u003e] [-ComputerName \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Force] [-Oldest] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Import-Counter",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-StartTime \u003cDateTime\u003e] [-EndTime \u003cDateTime\u003e] [-Counter \u003cString[]\u003e] [-MaxSamples \u003cInt64\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Path] \u003cString[]\u003e -ListSet \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Path] \u003cString[]\u003e [-Summary] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Connect-WSMan",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-ComputerName] \u003cString\u003e] [-ApplicationName \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-Port \u003cInt32\u003e] [-SessionOption \u003cSessionOption\u003e] [-UseSSL] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ConnectionURI \u003cUri\u003e] [-OptionSet \u003cHashtable\u003e] [-Port \u003cInt32\u003e] [-SessionOption \u003cSessionOption\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Disable-WSManCredSSP",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Role] \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Disconnect-WSMan",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-ComputerName] \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Enable-WSManCredSSP",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Role] \u003cString\u003e [[-DelegateComputer] \u003cString[]\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-WSManCredSSP",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-WSManInstance",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ResourceURI] \u003cUri\u003e [-ApplicationName \u003cString\u003e] [-ComputerName \u003cString\u003e] [-ConnectionURI \u003cUri\u003e] [-Dialect \u003cUri\u003e] [-Fragment \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-Port \u003cInt32\u003e] [-SelectorSet \u003cHashtable\u003e] [-SessionOption \u003cSessionOption\u003e] [-UseSSL] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ResourceURI] \u003cUri\u003e [-ApplicationName \u003cString\u003e] [-BasePropertiesOnly] [-ComputerName \u003cString\u003e] [-ConnectionURI \u003cUri\u003e] [-Dialect \u003cUri\u003e] -Enumerate [-Filter \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-Port \u003cInt32\u003e] [-Associations] [-ReturnType \u003cString\u003e] [-SessionOption \u003cSessionOption\u003e] [-Shallow] [-UseSSL] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Invoke-WSManAction",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ResourceURI] \u003cUri\u003e [-Action] \u003cString\u003e [[-SelectorSet] \u003cHashtable\u003e] [-ConnectionURI \u003cUri\u003e] [-FilePath \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-SessionOption \u003cSessionOption\u003e] [-ValueSet \u003cHashtable\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ResourceURI] \u003cUri\u003e [-Action] \u003cString\u003e [[-SelectorSet] \u003cHashtable\u003e] [-ApplicationName \u003cString\u003e] [-ComputerName \u003cString\u003e] [-FilePath \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-Port \u003cInt32\u003e] [-SessionOption \u003cSessionOption\u003e] [-UseSSL] [-ValueSet \u003cHashtable\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-WSManInstance",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ResourceURI] \u003cUri\u003e [-SelectorSet] \u003cHashtable\u003e [-ApplicationName \u003cString\u003e] [-ComputerName \u003cString\u003e] [-FilePath \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-Port \u003cInt32\u003e] [-SessionOption \u003cSessionOption\u003e] [-UseSSL] [-ValueSet \u003cHashtable\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ResourceURI] \u003cUri\u003e [-SelectorSet] \u003cHashtable\u003e [-ConnectionURI \u003cUri\u003e] [-FilePath \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-SessionOption \u003cSessionOption\u003e] [-ValueSet \u003cHashtable\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-WSManSessionOption",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ProxyAccessType \u003cProxyAccessType\u003e] [-ProxyAuthentication \u003cProxyAuthentication\u003e] [-ProxyCredential \u003cPSCredential\u003e] [-SkipCACheck] [-SkipCNCheck] [-SkipRevocationCheck] [-SPNPort \u003cInt32\u003e] [-OperationTimeout \u003cInt32\u003e] [-NoEncryption] [-UseUTF16] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Remove-WSManInstance",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ResourceURI] \u003cUri\u003e [-SelectorSet] \u003cHashtable\u003e [-ApplicationName \u003cString\u003e] [-ComputerName \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-Port \u003cInt32\u003e] [-SessionOption \u003cSessionOption\u003e] [-UseSSL] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ResourceURI] \u003cUri\u003e [-SelectorSet] \u003cHashtable\u003e [-ConnectionURI \u003cUri\u003e] [-OptionSet \u003cHashtable\u003e] [-SessionOption \u003cSessionOption\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-WSManInstance",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ResourceURI] \u003cUri\u003e [[-SelectorSet] \u003cHashtable\u003e] [-ApplicationName \u003cString\u003e] [-ComputerName \u003cString\u003e] [-Dialect \u003cUri\u003e] [-FilePath \u003cString\u003e] [-Fragment \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-Port \u003cInt32\u003e] [-SessionOption \u003cSessionOption\u003e] [-UseSSL] [-ValueSet \u003cHashtable\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ResourceURI] \u003cUri\u003e [[-SelectorSet] \u003cHashtable\u003e] [-ConnectionURI \u003cUri\u003e] [-Dialect \u003cUri\u003e] [-FilePath \u003cString\u003e] [-Fragment \u003cString\u003e] [-OptionSet \u003cHashtable\u003e] [-SessionOption \u003cSessionOption\u003e] [-ValueSet \u003cHashtable\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-WSManQuickConfig",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-UseSSL] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Test-WSMan",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-ComputerName] \u003cString\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-Port \u003cInt32\u003e] [-UseSSL] [-ApplicationName \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Add-History",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-InputObject] \u003cPSObject[]\u003e] [-Passthru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Add-PSSnapin",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Clear-History",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Id] \u003cInt32[]\u003e] [[-Count] \u003cInt32\u003e] [-Newest] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [[-Count] \u003cInt32\u003e] [-CommandLine \u003cString[]\u003e] [-Newest] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Disable-PSSessionConfiguration",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Enable-PSRemoting",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Enable-PSSessionConfiguration",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-Force] [-SecurityDescriptorSddl \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Enter-PSSession",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ComputerName] \u003cString\u003e [-Credential \u003cPSCredential\u003e] [-Port \u003cInt32\u003e] [-UseSSL] [-ConfigurationName \u003cString\u003e] [-ApplicationName \u003cString\u003e] [-SessionOption \u003cPSSessionOption\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Session] \u003cPSSession\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-ConnectionUri] \u003cUri\u003e] [-Credential \u003cPSCredential\u003e] [-ConfigurationName \u003cString\u003e] [-AllowRedirection] [-SessionOption \u003cPSSessionOption\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-InstanceId \u003cGuid\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Id] \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Name \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Exit-PSSession",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Export-Console",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Path] \u003cString\u003e] [-Force] [-NoClobber] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Export-ModuleMember",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Function] \u003cString[]\u003e] [-Cmdlet \u003cString[]\u003e] [-Variable \u003cString[]\u003e] [-Alias \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "ForEach-Object",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Process] \u003cScriptBlock[]\u003e [-InputObject \u003cPSObject\u003e] [-Begin \u003cScriptBlock\u003e] [-End \u003cScriptBlock\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Command",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-ArgumentList] \u003cObject[]\u003e] [-Verb \u003cString[]\u003e] [-Noun \u003cString[]\u003e] [-Module \u003cString[]\u003e] [-TotalCount \u003cInt32\u003e] [-Syntax] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Name] \u003cString[]\u003e] [[-ArgumentList] \u003cObject[]\u003e] [-Module \u003cString[]\u003e] [-CommandType \u003cCommandTypes\u003e] [-TotalCount \u003cInt32\u003e] [-Syntax] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Help",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString\u003e] [-Path \u003cString\u003e] [-Category \u003cString[]\u003e] [-Component \u003cString[]\u003e] [-Functionality \u003cString[]\u003e] [-Role \u003cString[]\u003e] [-Full] [-Online] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Name] \u003cString\u003e] [-Path \u003cString\u003e] [-Category \u003cString[]\u003e] [-Component \u003cString[]\u003e] [-Functionality \u003cString[]\u003e] [-Role \u003cString[]\u003e] [-Detailed] [-Online] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Name] \u003cString\u003e] [-Path \u003cString\u003e] [-Category \u003cString[]\u003e] [-Component \u003cString[]\u003e] [-Functionality \u003cString[]\u003e] [-Role \u003cString[]\u003e] [-Examples] [-Online] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Name] \u003cString\u003e] [-Path \u003cString\u003e] [-Category \u003cString[]\u003e] [-Component \u003cString[]\u003e] [-Functionality \u003cString[]\u003e] [-Role \u003cString[]\u003e] [-Parameter \u003cString\u003e] [-Online] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-History",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Id] \u003cInt64[]\u003e] [[-Count] \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Job",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Id] \u003cInt32[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Name] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-InstanceId] \u003cGuid[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-State \u003cJobState\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Command \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Module",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-All] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Name] \u003cString[]\u003e] [-All] [-ListAvailable] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-PSSession",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-ComputerName] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-InstanceId \u003cGuid[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Id] \u003cInt32[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Name \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-PSSessionConfiguration",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-PSSnapin",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-Registered] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Import-Module",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-Global] [-Prefix \u003cString\u003e] [-Function \u003cString[]\u003e] [-Cmdlet \u003cString[]\u003e] [-Variable \u003cString[]\u003e] [-Alias \u003cString[]\u003e] [-Force] [-PassThru] [-AsCustomObject] [-Version \u003cVersion\u003e] [-ArgumentList \u003cObject[]\u003e] [-DisableNameChecking] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Assembly] \u003cAssembly[]\u003e [-Global] [-Prefix \u003cString\u003e] [-Function \u003cString[]\u003e] [-Cmdlet \u003cString[]\u003e] [-Variable \u003cString[]\u003e] [-Alias \u003cString[]\u003e] [-Force] [-PassThru] [-AsCustomObject] [-ArgumentList \u003cObject[]\u003e] [-DisableNameChecking] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ModuleInfo] \u003cPSModuleInfo[]\u003e [-Global] [-Prefix \u003cString\u003e] [-Function \u003cString[]\u003e] [-Cmdlet \u003cString[]\u003e] [-Variable \u003cString[]\u003e] [-Alias \u003cString[]\u003e] [-Force] [-PassThru] [-AsCustomObject] [-ArgumentList \u003cObject[]\u003e] [-DisableNameChecking] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Invoke-Command",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ScriptBlock] \u003cScriptBlock\u003e [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Session] \u003cPSSession[]\u003e] [-ScriptBlock] \u003cScriptBlock\u003e [-ThrottleLimit \u003cInt32\u003e] [-AsJob] [-HideComputerName] [-JobName \u003cString\u003e] [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Session] \u003cPSSession[]\u003e] [-FilePath] \u003cString\u003e [-ThrottleLimit \u003cInt32\u003e] [-AsJob] [-HideComputerName] [-JobName \u003cString\u003e] [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-ComputerName] \u003cString[]\u003e] [-FilePath] \u003cString\u003e [-Credential \u003cPSCredential\u003e] [-Port \u003cInt32\u003e] [-UseSSL] [-ConfigurationName \u003cString\u003e] [-ApplicationName \u003cString\u003e] [-ThrottleLimit \u003cInt32\u003e] [-AsJob] [-HideComputerName] [-JobName \u003cString\u003e] [-SessionOption \u003cPSSessionOption\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-ComputerName] \u003cString[]\u003e] [-ScriptBlock] \u003cScriptBlock\u003e [-Credential \u003cPSCredential\u003e] [-Port \u003cInt32\u003e] [-UseSSL] [-ConfigurationName \u003cString\u003e] [-ApplicationName \u003cString\u003e] [-ThrottleLimit \u003cInt32\u003e] [-AsJob] [-HideComputerName] [-JobName \u003cString\u003e] [-SessionOption \u003cPSSessionOption\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-ConnectionUri] \u003cUri[]\u003e] [-ScriptBlock] \u003cScriptBlock\u003e [-Credential \u003cPSCredential\u003e] [-ConfigurationName \u003cString\u003e] [-ThrottleLimit \u003cInt32\u003e] [-AsJob] [-HideComputerName] [-JobName \u003cString\u003e] [-AllowRedirection] [-SessionOption \u003cPSSessionOption\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-ConnectionUri] \u003cUri[]\u003e] [-FilePath] \u003cString\u003e [-Credential \u003cPSCredential\u003e] [-ConfigurationName \u003cString\u003e] [-ThrottleLimit \u003cInt32\u003e] [-AsJob] [-HideComputerName] [-JobName \u003cString\u003e] [-AllowRedirection] [-SessionOption \u003cPSSessionOption\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Invoke-History",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Id] \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "New-Module",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ScriptBlock] \u003cScriptBlock\u003e [-Function \u003cString[]\u003e] [-Cmdlet \u003cString[]\u003e] [-ReturnResult] [-AsCustomObject] [-ArgumentList \u003cObject[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Name] \u003cString\u003e [-ScriptBlock] \u003cScriptBlock\u003e [-Function \u003cString[]\u003e] [-Cmdlet \u003cString[]\u003e] [-ReturnResult] [-AsCustomObject] [-ArgumentList \u003cObject[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-ModuleManifest",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e -NestedModules \u003cString[]\u003e [-Guid \u003cGuid\u003e] -Author \u003cString\u003e -CompanyName \u003cString\u003e -Copyright \u003cString\u003e -ModuleToProcess \u003cString\u003e [-ModuleVersion \u003cVersion\u003e] -Description \u003cString\u003e [-ProcessorArchitecture \u003cProcessorArchitecture\u003e] [-PowerShellVersion \u003cVersion\u003e] [-ClrVersion \u003cVersion\u003e] [-DotNetFrameworkVersion \u003cVersion\u003e] [-PowerShellHostName \u003cString\u003e] [-PowerShellHostVersion \u003cVersion\u003e] [-RequiredModules \u003cObject[]\u003e] -TypesToProcess \u003cString[]\u003e -FormatsToProcess \u003cString[]\u003e [-ScriptsToProcess \u003cString[]\u003e] -RequiredAssemblies \u003cString[]\u003e -FileList \u003cString[]\u003e [-ModuleList \u003cObject[]\u003e] [-FunctionsToExport \u003cString[]\u003e] [-AliasesToExport \u003cString[]\u003e] [-VariablesToExport \u003cString[]\u003e] [-CmdletsToExport \u003cString[]\u003e] [-PrivateData \u003cObject\u003e] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "New-PSSession",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-ComputerName] \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Name \u003cString[]\u003e] [-Port \u003cInt32\u003e] [-UseSSL] [-ConfigurationName \u003cString\u003e] [-ApplicationName \u003cString\u003e] [-ThrottleLimit \u003cInt32\u003e] [-SessionOption \u003cPSSessionOption\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ConnectionUri] \u003cUri[]\u003e [-Credential \u003cPSCredential\u003e] [-Name \u003cString[]\u003e] [-ConfigurationName \u003cString\u003e] [-ThrottleLimit \u003cInt32\u003e] [-AllowRedirection] [-SessionOption \u003cPSSessionOption\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-CertificateThumbprint \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Session] \u003cPSSession[]\u003e] [-Name \u003cString[]\u003e] [-ThrottleLimit \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-PSSessionOption",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-MaximumRedirection \u003cInt32\u003e] [-NoCompression] [-NoMachineProfile] [-Culture \u003cCultureInfo\u003e] [-UICulture \u003cCultureInfo\u003e] [-MaximumReceivedDataSizePerCommand \u003cInt32\u003e] [-MaximumReceivedObjectSize \u003cInt32\u003e] [-ApplicationArguments \u003cPSPrimitiveDictionary\u003e] [-OpenTimeout \u003cInt32\u003e] [-CancelTimeout \u003cInt32\u003e] [-IdleTimeout \u003cInt32\u003e] [-ProxyAccessType \u003cProxyAccessType\u003e] [-ProxyAuthentication \u003cAuthenticationMechanism\u003e] [-ProxyCredential \u003cPSCredential\u003e] [-SkipCACheck] [-SkipCNCheck] [-SkipRevocationCheck] [-OperationTimeout \u003cInt32\u003e] [-NoEncryption] [-UseUTF16] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Receive-Job",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Job] \u003cJob[]\u003e [[-Location] \u003cString[]\u003e] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Job] \u003cJob[]\u003e [[-ComputerName] \u003cString[]\u003e] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Job] \u003cJob[]\u003e [[-Session] \u003cPSSession[]\u003e] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Name] \u003cString[]\u003e] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-InstanceId] \u003cGuid[]\u003e] [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Id] \u003cInt32[]\u003e [-Keep] [-NoRecurse] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Register-PSSessionConfiguration",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString\u003e [-ProcessorArchitecture \u003cString\u003e] [-ApplicationBase \u003cString\u003e] [-ThreadApartmentState \u003cApartmentState\u003e] [-ThreadOptions \u003cPSThreadOptions\u003e] [-StartupScript \u003cString\u003e] [-MaximumReceivedDataSizePerCommandMB \u003cNullable`1\u003e] [-MaximumReceivedObjectSizeMB \u003cNullable`1\u003e] [-SecurityDescriptorSddl \u003cString\u003e] [-ShowSecurityDescriptorUI] [-Force] [-NoServiceRestart] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Name] \u003cString\u003e [-AssemblyName] \u003cString\u003e [-ConfigurationTypeName] \u003cString\u003e [-ProcessorArchitecture \u003cString\u003e] [-ApplicationBase \u003cString\u003e] [-ThreadApartmentState \u003cApartmentState\u003e] [-ThreadOptions \u003cPSThreadOptions\u003e] [-StartupScript \u003cString\u003e] [-MaximumReceivedDataSizePerCommandMB \u003cNullable`1\u003e] [-MaximumReceivedObjectSizeMB \u003cNullable`1\u003e] [-SecurityDescriptorSddl \u003cString\u003e] [-ShowSecurityDescriptorUI] [-Force] [-NoServiceRestart] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Remove-Job",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Id] \u003cInt32[]\u003e [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Job] \u003cJob[]\u003e [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [[-Name] \u003cString[]\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [[-InstanceId] \u003cGuid[]\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-State \u003cJobState\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Command \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Remove-Module",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-ModuleInfo] \u003cPSModuleInfo[]\u003e [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Remove-PSSession",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Id] \u003cInt32[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Session] \u003cPSSession[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-InstanceId \u003cGuid[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Name \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [[-ComputerName] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Remove-PSSnapin",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Set-PSDebug",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Trace \u003cInt32\u003e] [-Step] [-Strict] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Off] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-PSSessionConfiguration",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString\u003e [-ApplicationBase \u003cString\u003e] [-ThreadApartmentState \u003cApartmentState\u003e] [-ThreadOptions \u003cPSThreadOptions\u003e] [-StartupScript \u003cString\u003e] [-MaximumReceivedDataSizePerCommandMB \u003cNullable`1\u003e] [-MaximumReceivedObjectSizeMB \u003cNullable`1\u003e] [-SecurityDescriptorSddl \u003cString\u003e] [-ShowSecurityDescriptorUI] [-Force] [-NoServiceRestart] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Name] \u003cString\u003e [-AssemblyName] \u003cString\u003e [-ConfigurationTypeName] \u003cString\u003e [-ApplicationBase \u003cString\u003e] [-ThreadApartmentState \u003cApartmentState\u003e] [-ThreadOptions \u003cPSThreadOptions\u003e] [-StartupScript \u003cString\u003e] [-MaximumReceivedDataSizePerCommandMB \u003cNullable`1\u003e] [-MaximumReceivedObjectSizeMB \u003cNullable`1\u003e] [-SecurityDescriptorSddl \u003cString\u003e] [-ShowSecurityDescriptorUI] [-Force] [-NoServiceRestart] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Set-StrictMode",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "-Version \u003cVersion\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -Off [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Start-Job",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ScriptBlock] \u003cScriptBlock\u003e [[-InitializationScript] \u003cScriptBlock\u003e] [-Name \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-RunAs32] [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-FilePath] \u003cString\u003e] [[-InitializationScript] \u003cScriptBlock\u003e] [-Name \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Authentication \u003cAuthenticationMechanism\u003e] [-RunAs32] [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Stop-Job",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Id] \u003cInt32[]\u003e [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Job] \u003cJob[]\u003e [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [[-Name] \u003cString[]\u003e] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [[-InstanceId] \u003cGuid[]\u003e] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PassThru] [-State \u003cJobState\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Test-ModuleManifest",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Unregister-PSSessionConfiguration",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString\u003e [-Force] [-NoServiceRestart] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Wait-Job",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Id] \u003cInt32[]\u003e [-Any] [-Timeout \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Job] \u003cJob[]\u003e [-Any] [-Timeout \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Name] \u003cString[]\u003e] [-Any] [-Timeout \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-InstanceId] \u003cGuid[]\u003e] [-Any] [-Timeout \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Any] [-Timeout \u003cInt32\u003e] [-State \u003cJobState\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Where-Object",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-FilterScript] \u003cScriptBlock\u003e [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Add-Member",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-MemberType] \u003cPSMemberTypes\u003e [-Name] \u003cString\u003e [[-Value] \u003cObject\u003e] [[-SecondValue] \u003cObject\u003e] -InputObject \u003cPSObject\u003e [-Force] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Add-Type",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-TypeDefinition] \u003cString\u003e [-Language \u003cLanguage\u003e] [-ReferencedAssemblies \u003cString[]\u003e] [-CodeDomProvider \u003cCodeDomProvider\u003e] [-CompilerParameters \u003cCompilerParameters\u003e] [-OutputAssembly \u003cString\u003e] [-OutputType \u003cOutputAssemblyType\u003e] [-PassThru] [-IgnoreWarnings] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Name] \u003cString\u003e [-MemberDefinition] \u003cString[]\u003e [-Namespace \u003cString\u003e] [-UsingNamespace \u003cString[]\u003e] [-Language \u003cLanguage\u003e] [-ReferencedAssemblies \u003cString[]\u003e] [-CodeDomProvider \u003cCodeDomProvider\u003e] [-CompilerParameters \u003cCompilerParameters\u003e] [-OutputAssembly \u003cString\u003e] [-OutputType \u003cOutputAssemblyType\u003e] [-PassThru] [-IgnoreWarnings] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Path] \u003cString[]\u003e [-ReferencedAssemblies \u003cString[]\u003e] [-CompilerParameters \u003cCompilerParameters\u003e] [-OutputAssembly \u003cString\u003e] [-OutputType \u003cOutputAssemblyType\u003e] [-PassThru] [-IgnoreWarnings] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -AssemblyName \u003cString[]\u003e [-PassThru] [-IgnoreWarnings] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Clear-Variable",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-PassThru] [-Scope \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Compare-Object",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ReferenceObject] \u003cPSObject[]\u003e [-DifferenceObject] \u003cPSObject[]\u003e [-SyncWindow \u003cInt32\u003e] [-Property \u003cObject[]\u003e] [-ExcludeDifferent] [-IncludeEqual] [-PassThru] [-Culture \u003cString\u003e] [-CaseSensitive] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "ConvertFrom-Csv",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject] \u003cPSObject[]\u003e [[-Delimiter] \u003cChar\u003e] [-Header \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-InputObject] \u003cPSObject[]\u003e -UseCulture [-Header \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "ConvertFrom-StringData",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-StringData] \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "ConvertTo-Csv",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject] \u003cPSObject\u003e [[-Delimiter] \u003cChar\u003e] [-NoTypeInformation] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-InputObject] \u003cPSObject\u003e [-UseCulture] [-NoTypeInformation] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "ConvertTo-Html",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cObject[]\u003e] [[-Head] \u003cString[]\u003e] [[-Title] \u003cString\u003e] [[-Body] \u003cString[]\u003e] [-InputObject \u003cPSObject\u003e] [-As \u003cString\u003e] [-CssUri \u003cUri\u003e] [-PostContent \u003cString[]\u003e] [-PreContent \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Property] \u003cObject[]\u003e] [-InputObject \u003cPSObject\u003e] [-As \u003cString\u003e] [-Fragment] [-PostContent \u003cString[]\u003e] [-PreContent \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "ConvertTo-Xml",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject] \u003cPSObject\u003e [-Depth \u003cInt32\u003e] [-NoTypeInformation] [-As \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Disable-PSBreakpoint",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Breakpoint] \u003cBreakpoint[]\u003e [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Id] \u003cInt32[]\u003e [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Enable-PSBreakpoint",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Id] \u003cInt32[]\u003e [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Breakpoint] \u003cBreakpoint[]\u003e [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Export-Alias",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [[-Name] \u003cString[]\u003e] [-PassThru] [-As \u003cExportAliasFormat\u003e] [-Append] [-Force] [-NoClobber] [-Description \u003cString\u003e] [-Scope \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Export-Clixml",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [-Depth \u003cInt32\u003e] -InputObject \u003cPSObject\u003e [-Force] [-NoClobber] [-Encoding \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Export-Csv",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [[-Delimiter] \u003cChar\u003e] -InputObject \u003cPSObject\u003e [-Force] [-NoClobber] [-Encoding \u003cString\u003e] [-NoTypeInformation] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Path] \u003cString\u003e -InputObject \u003cPSObject\u003e [-Force] [-NoClobber] [-Encoding \u003cString\u003e] [-UseCulture] [-NoTypeInformation] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Export-FormatData",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject \u003cExtendedTypeDefinition[]\u003e] [-Path \u003cString\u003e] [-Force] [-NoClobber] [-IncludeScriptBlock] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Export-PSSession",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Session] \u003cPSSession\u003e [-OutputModule] \u003cString\u003e [[-CommandName] \u003cString[]\u003e] [[-FormatTypeName] \u003cString[]\u003e] [-Force] [-Encoding \u003cString\u003e] [-AllowClobber] [-ArgumentList \u003cObject[]\u003e] [-CommandType \u003cCommandTypes\u003e] [-Module \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Format-Custom",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cObject[]\u003e] [-Depth \u003cInt32\u003e] [-GroupBy \u003cObject\u003e] [-View \u003cString\u003e] [-ShowError] [-DisplayError] [-Force] [-Expand \u003cString\u003e] [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Format-List",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cObject[]\u003e] [-GroupBy \u003cObject\u003e] [-View \u003cString\u003e] [-ShowError] [-DisplayError] [-Force] [-Expand \u003cString\u003e] [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Format-Table",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cObject[]\u003e] [-AutoSize] [-HideTableHeaders] [-Wrap] [-GroupBy \u003cObject\u003e] [-View \u003cString\u003e] [-ShowError] [-DisplayError] [-Force] [-Expand \u003cString\u003e] [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Format-Wide",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cObject\u003e] [-AutoSize] [-Column \u003cInt32\u003e] [-GroupBy \u003cObject\u003e] [-View \u003cString\u003e] [-ShowError] [-DisplayError] [-Force] [-Expand \u003cString\u003e] [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Alias",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Scope \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Exclude \u003cString[]\u003e] [-Scope \u003cString\u003e] [-Definition \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Culture",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Date",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Date] \u003cDateTime\u003e] [-Year \u003cInt32\u003e] [-Month \u003cInt32\u003e] [-Day \u003cInt32\u003e] [-Hour \u003cInt32\u003e] [-Minute \u003cInt32\u003e] [-Second \u003cInt32\u003e] [-DisplayHint \u003cDisplayHintType\u003e] [-Format \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Date] \u003cDateTime\u003e] [-Year \u003cInt32\u003e] [-Month \u003cInt32\u003e] [-Day \u003cInt32\u003e] [-Hour \u003cInt32\u003e] [-Minute \u003cInt32\u003e] [-Second \u003cInt32\u003e] [-DisplayHint \u003cDisplayHintType\u003e] [-UFormat \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Event",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-SourceIdentifier] \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-EventIdentifier] \u003cInt32\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-EventSubscriber",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-SourceIdentifier] \u003cString\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-SubscriptionId] \u003cInt32\u003e [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-FormatData",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-TypeName] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Host",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Member",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-InputObject \u003cPSObject\u003e] [-MemberType \u003cPSMemberTypes\u003e] [-View \u003cPSMemberViewTypes\u003e] [-Static] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-PSBreakpoint",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Script] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Type] \u003cBreakpointType[]\u003e [-Script \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Script \u003cString[]\u003e] -Variable \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Script \u003cString[]\u003e] -Command \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Id] \u003cInt32[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-PSCallStack",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Random",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Maximum] \u003cObject\u003e] [-SetSeed \u003cNullable`1\u003e] [-Minimum \u003cObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-InputObject] \u003cObject[]\u003e [-SetSeed \u003cNullable`1\u003e] [-Count \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-TraceSource",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-UICulture",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Unique",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject \u003cPSObject\u003e] [-AsString] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-InputObject \u003cPSObject\u003e] [-OnType] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Variable",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-ValueOnly] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Scope \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Group-Object",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cObject[]\u003e] [-NoElement] [-AsHashTable] [-AsString] [-InputObject \u003cPSObject\u003e] [-Culture \u003cString\u003e] [-CaseSensitive] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Import-Alias",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [-Scope \u003cString\u003e] [-PassThru] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Import-Clixml",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Import-Csv",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [[-Delimiter] \u003cChar\u003e] [-Header \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Path] \u003cString[]\u003e -UseCulture [-Header \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Import-LocalizedData",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-BindingVariable] \u003cString\u003e [[-UICulture] \u003cString\u003e] [-BaseDirectory \u003cString\u003e] [-FileName \u003cString\u003e] [-SupportedCommand \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Import-PSSession",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Session] \u003cPSSession\u003e [[-CommandName] \u003cString[]\u003e] [[-FormatTypeName] \u003cString[]\u003e] [-Prefix \u003cString\u003e] [-DisableNameChecking] [-AllowClobber] [-ArgumentList \u003cObject[]\u003e] [-CommandType \u003cCommandTypes\u003e] [-Module \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Invoke-Expression",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Command] \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Measure-Command",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Expression] \u003cScriptBlock\u003e [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Measure-Object",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cString[]\u003e] [-InputObject \u003cPSObject\u003e] [-Sum] [-Average] [-Maximum] [-Minimum] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Property] \u003cString[]\u003e] [-InputObject \u003cPSObject\u003e] [-Line] [-Word] [-Character] [-IgnoreWhiteSpace] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-Alias",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString\u003e [-Value] \u003cString\u003e [-Description \u003cString\u003e] [-Option \u003cScopedItemOptions\u003e] [-PassThru] [-Scope \u003cString\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "New-Event",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-SourceIdentifier] \u003cString\u003e [[-Sender] \u003cPSObject\u003e] [[-EventArguments] \u003cPSObject[]\u003e] [[-MessageData] \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-Object",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-TypeName] \u003cString\u003e [[-ArgumentList] \u003cObject[]\u003e] [-Property \u003cHashtable\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ComObject] \u003cString\u003e [-Strict] [-Property \u003cHashtable\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-TimeSpan",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Start] \u003cDateTime\u003e] [[-End] \u003cDateTime\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Days \u003cInt32\u003e] [-Hours \u003cInt32\u003e] [-Minutes \u003cInt32\u003e] [-Seconds \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-Variable",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString\u003e [[-Value] \u003cObject\u003e] [-Description \u003cString\u003e] [-Option \u003cScopedItemOptions\u003e] [-Visibility \u003cSessionStateEntryVisibility\u003e] [-Force] [-PassThru] [-Scope \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Out-Default",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Out-File",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-FilePath] \u003cString\u003e [[-Encoding] \u003cString\u003e] [-Append] [-Force] [-NoClobber] [-Width \u003cInt32\u003e] [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Out-GridView",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject \u003cPSObject\u003e] [-Title \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Out-Host",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Paging] [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Out-Null",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Out-Printer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString\u003e] [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Out-String",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Stream] [-Width \u003cInt32\u003e] [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Read-Host",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Prompt] \u003cObject\u003e] [-AsSecureString] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Register-EngineEvent",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-SourceIdentifier] \u003cString\u003e [[-Action] \u003cScriptBlock\u003e] [-MessageData \u003cPSObject\u003e] [-SupportEvent] [-Forward] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Register-ObjectEvent",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject] \u003cPSObject\u003e [-EventName] \u003cString\u003e [[-SourceIdentifier] \u003cString\u003e] [[-Action] \u003cScriptBlock\u003e] [-MessageData \u003cPSObject\u003e] [-SupportEvent] [-Forward] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Remove-Event",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-SourceIdentifier] \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-EventIdentifier] \u003cInt32\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Remove-PSBreakpoint",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Breakpoint] \u003cBreakpoint[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Id] \u003cInt32[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Remove-Variable",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Scope \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Select-Object",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cObject[]\u003e] [-InputObject \u003cPSObject\u003e] [-ExcludeProperty \u003cString[]\u003e] [-ExpandProperty \u003cString\u003e] [-Unique] [-Last \u003cInt32\u003e] [-First \u003cInt32\u003e] [-Skip \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-InputObject \u003cPSObject\u003e] [-Unique] [-Index \u003cInt32[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Select-String",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Pattern] \u003cString[]\u003e -InputObject \u003cPSObject\u003e [-SimpleMatch] [-CaseSensitive] [-Quiet] [-List] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-NotMatch] [-AllMatches] [-Encoding \u003cString\u003e] [-Context \u003cInt32[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Pattern] \u003cString[]\u003e [-Path] \u003cString[]\u003e [-SimpleMatch] [-CaseSensitive] [-Quiet] [-List] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-NotMatch] [-AllMatches] [-Encoding \u003cString\u003e] [-Context \u003cInt32[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Select-Xml",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-XPath] \u003cString\u003e [-Xml] \u003cXmlNode[]\u003e [-Namespace \u003cHashtable\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-XPath] \u003cString\u003e [-Path] \u003cString[]\u003e [-Namespace \u003cHashtable\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-XPath] \u003cString\u003e -Content \u003cString[]\u003e [-Namespace \u003cHashtable\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Send-MailMessage",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-To] \u003cString[]\u003e [-Subject] \u003cString\u003e [[-Body] \u003cString\u003e] [[-SmtpServer] \u003cString\u003e] [-Attachments \u003cString[]\u003e] [-Bcc \u003cString[]\u003e] [-BodyAsHtml] [-Encoding \u003cEncoding\u003e] [-Cc \u003cString[]\u003e] [-DeliveryNotificationOption \u003cDeliveryNotificationOptions\u003e] -From \u003cString\u003e [-Priority \u003cMailPriority\u003e] [-Credential \u003cPSCredential\u003e] [-UseSsl] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-Alias",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString\u003e [-Value] \u003cString\u003e [-Description \u003cString\u003e] [-Option \u003cScopedItemOptions\u003e] [-PassThru] [-Scope \u003cString\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Set-Date",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Date] \u003cDateTime\u003e [-DisplayHint \u003cDisplayHintType\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Adjust] \u003cTimeSpan\u003e [-DisplayHint \u003cDisplayHintType\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Set-PSBreakpoint",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Script] \u003cString[]\u003e [-Line] \u003cInt32[]\u003e [[-Column] \u003cInt32\u003e] [-Action \u003cScriptBlock\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Script] \u003cString[]\u003e] [-Action \u003cScriptBlock\u003e] -Command \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Script] \u003cString[]\u003e] [-Action \u003cScriptBlock\u003e] -Variable \u003cString[]\u003e [-Mode \u003cVariableAccessMode\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-TraceSource",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [[-Option] \u003cPSTraceSourceOptions\u003e] [-ListenerOption \u003cTraceOptions\u003e] [-FilePath \u003cString\u003e] [-Force] [-Debugger] [-PSHost] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Name] \u003cString[]\u003e [-RemoveListener \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Name] \u003cString[]\u003e [-RemoveFileListener \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-Variable",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [[-Value] \u003cObject\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Description \u003cString\u003e] [-Option \u003cScopedItemOptions\u003e] [-Force] [-Visibility \u003cSessionStateEntryVisibility\u003e] [-PassThru] [-Scope \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Sort-Object",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cObject[]\u003e] [-Descending] [-Unique] [-InputObject \u003cPSObject\u003e] [-Culture \u003cString\u003e] [-CaseSensitive] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Start-Sleep",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Seconds] \u003cInt32\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -Milliseconds \u003cInt32\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Tee-Object",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-FilePath] \u003cString\u003e [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-InputObject \u003cPSObject\u003e] -Variable \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Trace-Command",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-Expression] \u003cScriptBlock\u003e [[-Option] \u003cPSTraceSourceOptions\u003e] [-InputObject \u003cPSObject\u003e] [-ListenerOption \u003cTraceOptions\u003e] [-FilePath \u003cString\u003e] [-Force] [-Debugger] [-PSHost] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Name] \u003cString[]\u003e [-Command] \u003cString\u003e [[-Option] \u003cPSTraceSourceOptions\u003e] [-InputObject \u003cPSObject\u003e] [-ArgumentList \u003cObject[]\u003e] [-ListenerOption \u003cTraceOptions\u003e] [-FilePath \u003cString\u003e] [-Force] [-Debugger] [-PSHost] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Unregister-Event",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-SourceIdentifier] \u003cString\u003e [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-SubscriptionId] \u003cInt32\u003e [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Update-FormatData",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-AppendPath] \u003cString[]\u003e] [-PrependPath \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Update-List",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Property] \u003cString\u003e] [-Add \u003cObject[]\u003e] [-Remove \u003cObject[]\u003e] [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Property] \u003cString\u003e] -Replace \u003cObject[]\u003e [-InputObject \u003cPSObject\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Update-TypeData",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-AppendPath] \u003cString[]\u003e] [-PrependPath \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Wait-Event",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-SourceIdentifier] \u003cString\u003e] [-Timeout \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Write-Debug",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Message] \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Write-Error",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Message] \u003cString\u003e [-Category \u003cErrorCategory\u003e] [-ErrorId \u003cString\u003e] [-TargetObject \u003cObject\u003e] [-RecommendedAction \u003cString\u003e] [-CategoryActivity \u003cString\u003e] [-CategoryReason \u003cString\u003e] [-CategoryTargetName \u003cString\u003e] [-CategoryTargetType \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -Exception \u003cException\u003e [-Message \u003cString\u003e] [-Category \u003cErrorCategory\u003e] [-ErrorId \u003cString\u003e] [-TargetObject \u003cObject\u003e] [-RecommendedAction \u003cString\u003e] [-CategoryActivity \u003cString\u003e] [-CategoryReason \u003cString\u003e] [-CategoryTargetName \u003cString\u003e] [-CategoryTargetType \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -ErrorRecord \u003cErrorRecord\u003e [-RecommendedAction \u003cString\u003e] [-CategoryActivity \u003cString\u003e] [-CategoryReason \u003cString\u003e] [-CategoryTargetName \u003cString\u003e] [-CategoryTargetType \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Write-Host",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Object] \u003cObject\u003e] [-NoNewline] [-Separator \u003cObject\u003e] [-ForegroundColor \u003cConsoleColor\u003e] [-BackgroundColor \u003cConsoleColor\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Write-Output",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-InputObject] \u003cPSObject[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Write-Progress",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Activity] \u003cString\u003e [-Status] \u003cString\u003e [[-Id] \u003cInt32\u003e] [-PercentComplete \u003cInt32\u003e] [-SecondsRemaining \u003cInt32\u003e] [-CurrentOperation \u003cString\u003e] [-ParentId \u003cInt32\u003e] [-Completed] [-SourceId \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Write-Verbose",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Message] \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Write-Warning",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Message] \u003cString\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Start-Transcript",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Path] \u003cString\u003e] [-Append] [-Force] [-NoClobber] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Stop-Transcript",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Add-Computer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-DomainName] \u003cString\u003e [-Credential \u003cPSCredential\u003e] [-OUPath \u003cString\u003e] [-PassThru] [-Server \u003cString\u003e] [-UnSecure] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-WorkGroupName] \u003cString\u003e [-Credential \u003cPSCredential\u003e] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Add-Content",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Value] \u003cObject[]\u003e [-PassThru] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-Encoding \u003cFileSystemCmdletProviderEncoding\u003e] [-LiteralPath] \u003cString[]\u003e [-Value] \u003cObject[]\u003e [-PassThru] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-Encoding \u003cFileSystemCmdletProviderEncoding\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Checkpoint-Computer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Description] \u003cString\u003e [[-RestorePointType] \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Clear-Content",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Clear-EventLog",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-LogName] \u003cString[]\u003e [[-ComputerName] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Clear-Item",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Clear-ItemProperty",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Name] \u003cString\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Name] \u003cString\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Complete-Transaction",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Convert-Path",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Copy-Item",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [[-Destination] \u003cString\u003e] [-Container] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Recurse] [-PassThru] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [[-Destination] \u003cString\u003e] [-Container] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Recurse] [-PassThru] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Copy-ItemProperty",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Destination] \u003cString\u003e [-Name] \u003cString\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Destination] \u003cString\u003e [-Name] \u003cString\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Debug-Process",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Id] \u003cInt32[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] -InputObject \u003cProcess[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Disable-ComputerRestore",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Drive] \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Enable-ComputerRestore",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Drive] \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Get-ChildItem",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Path] \u003cString[]\u003e] [[-Filter] \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Recurse] [-Force] [-Name] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [[-Filter] \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Recurse] [-Force] [-Name] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Get-ComputerRestorePoint",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-RestorePoint] \u003cInt32[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -LastStatus [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Content",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-ReadCount \u003cInt64\u003e] [-TotalCount \u003cInt64\u003e] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-Delimiter \u003cString\u003e] [-Wait] [-Encoding \u003cFileSystemCmdletProviderEncoding\u003e] [-LiteralPath] \u003cString[]\u003e [-ReadCount \u003cInt64\u003e] [-TotalCount \u003cInt64\u003e] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-Delimiter \u003cString\u003e] [-Wait] [-Encoding \u003cFileSystemCmdletProviderEncoding\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-EventLog",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-LogName] \u003cString\u003e [[-InstanceId] \u003cInt64[]\u003e] [-ComputerName \u003cString[]\u003e] [-Newest \u003cInt32\u003e] [-After \u003cDateTime\u003e] [-Before \u003cDateTime\u003e] [-UserName \u003cString[]\u003e] [-Index \u003cInt32[]\u003e] [-EntryType \u003cString[]\u003e] [-Source \u003cString[]\u003e] [-Message \u003cString\u003e] [-AsBaseObject] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-List] [-AsString] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-HotFix",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Id] \u003cString[]\u003e] [-ComputerName \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Description \u003cString[]\u003e] [-ComputerName \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Item",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Get-ItemProperty",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [[-Name] \u003cString[]\u003e] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [[-Name] \u003cString[]\u003e] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Get-Location",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-PSProvider \u003cString[]\u003e] [-PSDrive \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-Stack] [-StackName \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Get-Process",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-ComputerName \u003cString[]\u003e] [-Module] [-FileVersionInfo] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] -Id \u003cInt32[]\u003e [-ComputerName \u003cString[]\u003e] [-Module] [-FileVersionInfo] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Module] [-FileVersionInfo] -InputObject \u003cProcess[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-PSDrive",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-Scope \u003cString\u003e] [-PSProvider \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-LiteralName] \u003cString[]\u003e [-Scope \u003cString\u003e] [-PSProvider \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Get-PSProvider",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-PSProvider] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Service",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Name] \u003cString[]\u003e] [-ComputerName \u003cString[]\u003e] [-DependentServices] [-RequiredServices] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-DependentServices] [-RequiredServices] -DisplayName \u003cString[]\u003e [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-DependentServices] [-RequiredServices] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-InputObject \u003cServiceController[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Transaction",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-WmiObject",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Class] \u003cString\u003e [[-Property] \u003cString[]\u003e] [-Filter \u003cString\u003e] [-Amended] [-DirectRead] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Class] \u003cString\u003e] [-Recurse] [-Amended] [-List] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Amended] [-DirectRead] -Query \u003cString\u003e [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Amended] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Amended] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Invoke-Item",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Invoke-WmiMethod",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Class] \u003cString\u003e [-Name] \u003cString\u003e [[-ArgumentList] \u003cObject[]\u003e] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Name] \u003cString\u003e -InputObject \u003cManagementObject\u003e [-ArgumentList \u003cObject[]\u003e] [-AsJob] [-ThrottleLimit \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Name] \u003cString\u003e -Path \u003cString\u003e [-ArgumentList \u003cObject[]\u003e] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Name] \u003cString\u003e [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Name] \u003cString\u003e [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Name] \u003cString\u003e [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Join-Path",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-ChildPath] \u003cString\u003e [-Resolve] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Limit-EventLog",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-LogName] \u003cString[]\u003e [-ComputerName \u003cString[]\u003e] [-RetentionDays \u003cInt32\u003e] [-OverflowAction \u003cOverflowAction\u003e] [-MaximumSize \u003cInt64\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Move-Item",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [[-Destination] \u003cString\u003e] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-PassThru] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [[-Destination] \u003cString\u003e] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-PassThru] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Move-ItemProperty",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Destination] \u003cString\u003e [-Name] \u003cString[]\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Destination] \u003cString\u003e [-Name] \u003cString[]\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "New-EventLog",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-LogName] \u003cString\u003e [-Source] \u003cString[]\u003e [[-ComputerName] \u003cString[]\u003e] [-CategoryResourceFile \u003cString\u003e] [-MessageResourceFile \u003cString\u003e] [-ParameterResourceFile \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "New-Item",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-ItemType \u003cString\u003e] [-Value \u003cObject\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [[-Path] \u003cString[]\u003e] -Name \u003cString\u003e [-ItemType \u003cString\u003e] [-Value \u003cObject\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "New-ItemProperty",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Name] \u003cString\u003e [-PropertyType \u003cString\u003e] [-Value \u003cObject\u003e] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Name] \u003cString\u003e [-PropertyType \u003cString\u003e] [-Value \u003cObject\u003e] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "New-PSDrive",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString\u003e [-PSProvider] \u003cString\u003e [-Root] \u003cString\u003e [-Description \u003cString\u003e] [-Scope \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "New-Service",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString\u003e [-BinaryPathName] \u003cString\u003e [-DisplayName \u003cString\u003e] [-Description \u003cString\u003e] [-StartupType \u003cServiceStartMode\u003e] [-Credential \u003cPSCredential\u003e] [-DependsOn \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "New-WebServiceProxy",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Uri] \u003cUri\u003e [[-Class] \u003cString\u003e] [[-Namespace] \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Uri] \u003cUri\u003e [[-Class] \u003cString\u003e] [[-Namespace] \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Uri] \u003cUri\u003e [[-Class] \u003cString\u003e] [[-Namespace] \u003cString\u003e] [-UseDefaultCredential] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Pop-Location",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-PassThru] [-StackName \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Push-Location",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Path] \u003cString\u003e] [-PassThru] [-StackName \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [[-LiteralPath] \u003cString\u003e] [-PassThru] [-StackName \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Register-WmiEvent",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Class] \u003cString\u003e [[-SourceIdentifier] \u003cString\u003e] [[-Action] \u003cScriptBlock\u003e] [-Namespace \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ComputerName \u003cString\u003e] [-Timeout \u003cInt64\u003e] [-MessageData \u003cPSObject\u003e] [-SupportEvent] [-Forward] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Query] \u003cString\u003e [[-SourceIdentifier] \u003cString\u003e] [[-Action] \u003cScriptBlock\u003e] [-Namespace \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ComputerName \u003cString\u003e] [-Timeout \u003cInt64\u003e] [-MessageData \u003cPSObject\u003e] [-SupportEvent] [-Forward] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Remove-Computer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Credential] \u003cPSCredential\u003e] [-Force] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Remove-EventLog",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-LogName] \u003cString[]\u003e [[-ComputerName] \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [[-ComputerName] \u003cString[]\u003e] [-Source \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Remove-Item",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Recurse] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Recurse] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Remove-ItemProperty",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Name] \u003cString[]\u003e [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Name] \u003cString[]\u003e [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Remove-PSDrive",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-PSProvider \u003cString[]\u003e] [-Scope \u003cString\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralName] \u003cString[]\u003e [-PSProvider \u003cString[]\u003e] [-Scope \u003cString\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Remove-WmiObject",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Class] \u003cString\u003e [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] -InputObject \u003cManagementObject\u003e [-AsJob] [-ThrottleLimit \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] -Path \u003cString\u003e [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Rename-Item",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [-NewName] \u003cString\u003e [-Force] [-PassThru] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Rename-ItemProperty",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString\u003e [-Name] \u003cString\u003e [-NewName] \u003cString\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString\u003e [-Name] \u003cString\u003e [-NewName] \u003cString\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Reset-ComputerMachinePassword",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Server \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Resolve-Path",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Relative] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Relative] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Restart-Computer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-ComputerName] \u003cString[]\u003e] [[-Credential] \u003cPSCredential\u003e] [-AsJob] [-Authentication \u003cAuthenticationLevel\u003e] [-Force] [-Impersonation \u003cImpersonationLevel\u003e] [-ThrottleLimit \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Restart-Service",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-Force] [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Force] [-PassThru] -DisplayName \u003cString[]\u003e [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Force] [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-InputObject \u003cServiceController[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Restore-Computer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-RestorePoint] \u003cInt32\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Resume-Service",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PassThru] -DisplayName \u003cString[]\u003e [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-InputObject \u003cServiceController[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Set-Content",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Value] \u003cObject[]\u003e [-PassThru] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-Encoding \u003cFileSystemCmdletProviderEncoding\u003e] [-LiteralPath] \u003cString[]\u003e [-Value] \u003cObject[]\u003e [-PassThru] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Force] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-Encoding \u003cFileSystemCmdletProviderEncoding\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-Item",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [[-Value] \u003cObject\u003e] [-Force] [-PassThru] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [[-Value] \u003cObject\u003e] [-Force] [-PassThru] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Set-ItemProperty",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Name] \u003cString\u003e [-Value] \u003cObject\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-Path] \u003cString[]\u003e -InputObject \u003cPSObject\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e -InputObject \u003cPSObject\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Name] \u003cString\u003e [-Value] \u003cObject\u003e [-PassThru] [-Force] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Set-Location",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Path] \u003cString\u003e] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-LiteralPath] \u003cString\u003e [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-PassThru] [-StackName \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Set-Service",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString\u003e [-ComputerName \u003cString[]\u003e] [-DisplayName \u003cString\u003e] [-Description \u003cString\u003e] [-StartupType \u003cServiceStartMode\u003e] [-Status \u003cString\u003e] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-ComputerName \u003cString[]\u003e] [-DisplayName \u003cString\u003e] [-Description \u003cString\u003e] [-StartupType \u003cServiceStartMode\u003e] [-Status \u003cString\u003e] [-InputObject \u003cServiceController\u003e] [-PassThru] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Set-WmiInstance",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Class] \u003cString\u003e [[-Arguments] \u003cHashtable\u003e] [-PutType \u003cPutType\u003e] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] -InputObject \u003cManagementObject\u003e [-Arguments \u003cHashtable\u003e] [-PutType \u003cPutType\u003e] [-AsJob] [-ThrottleLimit \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] -Path \u003cString\u003e [-Arguments \u003cHashtable\u003e] [-PutType \u003cPutType\u003e] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PutType \u003cPutType\u003e] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PutType \u003cPutType\u003e] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PutType \u003cPutType\u003e] [-AsJob] [-Impersonation \u003cImpersonationLevel\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-Locale \u003cString\u003e] [-EnableAllPrivileges] [-Authority \u003cString\u003e] [-Credential \u003cPSCredential\u003e] [-ThrottleLimit \u003cInt32\u003e] [-ComputerName \u003cString[]\u003e] [-Namespace \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Show-EventLog",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-ComputerName] \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Split-Path",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-LiteralPath \u003cString[]\u003e] [-Parent] [-Resolve] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-Path] \u003cString[]\u003e [-Qualifier] [-LiteralPath \u003cString[]\u003e] [-Resolve] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-Path] \u003cString[]\u003e [-LiteralPath \u003cString[]\u003e] [-NoQualifier] [-Resolve] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-Path] \u003cString[]\u003e [-LiteralPath \u003cString[]\u003e] [-Leaf] [-Resolve] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-Path] \u003cString[]\u003e [-LiteralPath \u003cString[]\u003e] [-Resolve] [-IsAbsolute] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Start-Process",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-FilePath] \u003cString\u003e [[-ArgumentList] \u003cString[]\u003e] [-Credential \u003cPSCredential\u003e] [-WorkingDirectory \u003cString\u003e] [-LoadUserProfile] [-NoNewWindow] [-PassThru] [-RedirectStandardError \u003cString\u003e] [-RedirectStandardInput \u003cString\u003e] [-RedirectStandardOutput \u003cString\u003e] [-Wait] [-UseNewEnvironment] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-FilePath] \u003cString\u003e [[-ArgumentList] \u003cString[]\u003e] [-WorkingDirectory \u003cString\u003e] [-PassThru] [-Verb \u003cString\u003e] [-Wait] [-WindowStyle \u003cProcessWindowStyle\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Start-Service",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PassThru] -DisplayName \u003cString[]\u003e [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-InputObject \u003cServiceController[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Start-Transaction",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Timeout \u003cInt32\u003e] [-Independent] [-RollbackPreference \u003cRollbackSeverity\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Stop-Computer",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-ComputerName] \u003cString[]\u003e] [[-Credential] \u003cPSCredential\u003e] [-AsJob] [-Authentication \u003cAuthenticationLevel\u003e] [-Impersonation \u003cImpersonationLevel\u003e] [-ThrottleLimit \u003cInt32\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Stop-Process",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Id] \u003cInt32[]\u003e [-PassThru] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] -Name \u003cString[]\u003e [-PassThru] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PassThru] [-Force] -InputObject \u003cProcess[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Stop-Service",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-Force] [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Force] [-PassThru] -DisplayName \u003cString[]\u003e [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-Force] [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-InputObject \u003cServiceController[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Suspend-Service",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PassThru] -DisplayName \u003cString[]\u003e [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-PassThru] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-InputObject \u003cServiceController[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Test-ComputerSecureChannel",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Repair] [-Server \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Test-Connection",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ComputerName] \u003cString[]\u003e [[-Source] \u003cString[]\u003e] [-AsJob] [-Authentication \u003cAuthenticationLevel\u003e] [-BufferSize \u003cInt32\u003e] [-Count \u003cInt32\u003e] [-Credential \u003cPSCredential\u003e] [-Impersonation \u003cImpersonationLevel\u003e] [-ThrottleLimit \u003cInt32\u003e] [-TimeToLive \u003cInt32\u003e] [-Delay \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-ComputerName] \u003cString[]\u003e [[-Source] \u003cString[]\u003e] [-Authentication \u003cAuthenticationLevel\u003e] [-BufferSize \u003cInt32\u003e] [-Count \u003cInt32\u003e] [-Credential \u003cPSCredential\u003e] [-Impersonation \u003cImpersonationLevel\u003e] [-TimeToLive \u003cInt32\u003e] [-Delay \u003cInt32\u003e] [-Quiet] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Test-Path",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-PathType \u003cTestPathType\u003e] [-IsValid] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction] [-LiteralPath] \u003cString[]\u003e [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-PathType \u003cTestPathType\u003e] [-IsValid] [-Credential \u003cPSCredential\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Undo-Transaction",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Use-Transaction",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-TransactedScript] \u003cScriptBlock\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Wait-Process",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Name] \u003cString[]\u003e [[-Timeout] \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-Id] \u003cInt32[]\u003e [[-Timeout] \u003cInt32\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [[-Timeout] \u003cInt32\u003e] -InputObject \u003cProcess[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Write-EventLog",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-LogName] \u003cString\u003e [-Source] \u003cString\u003e [-EventId] \u003cInt32\u003e [[-EntryType] \u003cEventLogEntryType\u003e] [-Message] \u003cString\u003e [-Category \u003cInt16\u003e] [-RawData \u003cByte[]\u003e] [-ComputerName \u003cString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "ConvertFrom-SecureString",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-SecureString] \u003cSecureString\u003e [[-SecureKey] \u003cSecureString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-SecureString] \u003cSecureString\u003e [-Key \u003cByte[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "ConvertTo-SecureString",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-String] \u003cString\u003e [[-SecureKey] \u003cSecureString\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-String] \u003cString\u003e [-AsPlainText] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-String] \u003cString\u003e [-Key \u003cByte[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Acl",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Path] \u003cString[]\u003e] [-Audit] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Get-AuthenticodeSignature",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-FilePath] \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-Credential",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Credential] \u003cPSCredential\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-ExecutionPolicy",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[[-Scope] \u003cExecutionPolicyScope\u003e] [-List] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Get-PfxCertificate",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-FilePath] \u003cString[]\u003e [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e]"
                                                 },
                                                 {
                                                     "Name":  "Set-Acl",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-Path] \u003cString[]\u003e [-AclObject] \u003cObjectSecurity\u003e [-Passthru] [-Filter \u003cString\u003e] [-Include \u003cString[]\u003e] [-Exclude \u003cString[]\u003e] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm] [-UseTransaction]"
                                                 },
                                                 {
                                                     "Name":  "Set-AuthenticodeSignature",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-FilePath] \u003cString[]\u003e [-Certificate] \u003cX509Certificate2\u003e [-IncludeChain \u003cString\u003e] [-TimestampServer \u003cString\u003e] [-HashAlgorithm \u003cString\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 },
                                                 {
                                                     "Name":  "Set-ExecutionPolicy",
                                                     "CommandType":  "Cmdlet",
                                                     "ParameterSets":  "[-ExecutionPolicy] \u003cExecutionPolicy\u003e [[-Scope] \u003cExecutionPolicyScope\u003e] [-Force] [-Verbose] [-Debug] [-ErrorAction \u003cActionPreference\u003e] [-WarningAction \u003cActionPreference\u003e] [-ErrorVariable \u003cString\u003e] [-WarningVariable \u003cString\u003e] [-OutVariable \u003cString\u003e] [-OutBuffer \u003cInt32\u003e] [-WhatIf] [-Confirm]"
                                                 }
                                             ],
                        "ExportedAliases":  "ise"
                    }
                ],
    "SchemaVersion":  "0.0.1"
}
