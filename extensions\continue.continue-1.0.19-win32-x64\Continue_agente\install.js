/**
 * Instalador do Continue Agent
 * Script para integrar o agente na extensão Continue
 */

const fs = require('fs').promises;
const path = require('path');
const vscode = require('vscode');

class ContinueAgentInstaller {
    constructor() {
        this.extensionPath = path.join(__dirname, '..');
        this.agentPath = __dirname;
        this.configPath = path.join(this.agentPath, 'config.json');
        this.isInstalled = false;
    }

    async install() {
        try {
            console.log('🚀 Iniciando instalação do Continue Agent...');
            
            // Verificar se a extensão Continue está instalada
            await this.checkContinueExtension();
            
            // Verificar se o LM Studio está configurado
            await this.checkLMStudioConfig();
            
            // Registrar o agente na extensão
            await this.registerAgent();
            
            // Configurar comandos
            await this.setupCommands();
            
            // Inicializar componentes
            await this.initializeComponents();
            
            this.isInstalled = true;
            console.log('✅ Continue Agent instalado com sucesso!');
            
            // Mostrar mensagem de sucesso
            vscode.window.showInformationMessage(
                '🎉 Continue Agent instalado! Use os comandos /edit, /create, /web-search e outros.',
                'Ver Comandos',
                'Configurar LM Studio'
            ).then(selection => {
                if (selection === 'Ver Comandos') {
                    this.showAvailableCommands();
                } else if (selection === 'Configurar LM Studio') {
                    this.openLMStudioConfig();
                }
            });
            
            return { success: true, message: 'Instalação concluída com sucesso' };
            
        } catch (error) {
            console.error('❌ Erro na instalação:', error);
            vscode.window.showErrorMessage(`Erro na instalação: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    async checkContinueExtension() {
        console.log('🔍 Verificando extensão Continue...');
        
        const extension = vscode.extensions.getExtension('continue.continue');
        if (!extension) {
            throw new Error('Extensão Continue não encontrada. Instale a extensão Continue primeiro.');
        }
        
        if (!extension.isActive) {
            console.log('📦 Ativando extensão Continue...');
            await extension.activate();
        }
        
        console.log('✅ Extensão Continue verificada');
    }

    async checkLMStudioConfig() {
        console.log('🔍 Verificando configuração do LM Studio...');
        
        try {
            // Verificar se o arquivo de modelo existe
            const modelPath = "C:\\Users\\<USER>\\.lmstudio\\models\\mradermacher\\Qwen3-30B-A3B-python-coder-i1-GGUF\\Qwen3-30B-A3B-python-coder.i1-Q4_K_S.gguf";
            
            try {
                await fs.access(modelPath);
                console.log('✅ Modelo LLM encontrado:', modelPath);
            } catch {
                console.warn('⚠️ Modelo LLM não encontrado no caminho esperado:', modelPath);
                vscode.window.showWarningMessage(
                    'Modelo LLM não encontrado. Verifique se o LM Studio está configurado corretamente.',
                    'Abrir LM Studio'
                ).then(selection => {
                    if (selection === 'Abrir LM Studio') {
                        vscode.env.openExternal(vscode.Uri.parse('http://localhost:1234'));
                    }
                });
            }
            
            // Testar conexão com LM Studio
            await this.testLMStudioConnection();
            
        } catch (error) {
            console.warn('⚠️ Erro ao verificar LM Studio:', error.message);
        }
    }

    async testLMStudioConnection() {
        try {
            const response = await fetch('http://localhost:1234/v1/models', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer lm-studio'
                }
            });
            
            if (response.ok) {
                const models = await response.json();
                console.log('✅ Conexão com LM Studio estabelecida');
                console.log('📋 Modelos disponíveis:', models.data?.length || 0);
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.warn('⚠️ Não foi possível conectar ao LM Studio:', error.message);
            vscode.window.showWarningMessage(
                'LM Studio não está rodando ou não está acessível em localhost:1234. Inicie o LM Studio e carregue o modelo.',
                'Abrir LM Studio'
            ).then(selection => {
                if (selection === 'Abrir LM Studio') {
                    vscode.env.openExternal(vscode.Uri.parse('http://localhost:1234'));
                }
            });
        }
    }

    async registerAgent() {
        console.log('📝 Registrando Continue Agent...');
        
        // Carregar configuração do agente
        const config = await this.loadAgentConfig();
        
        // Registrar na configuração global do Continue
        await this.updateContinueConfig(config);
        
        console.log('✅ Agente registrado');
    }

    async loadAgentConfig() {
        try {
            const configContent = await fs.readFile(this.configPath, 'utf8');
            return JSON.parse(configContent);
        } catch (error) {
            throw new Error(`Erro ao carregar configuração: ${error.message}`);
        }
    }

    async updateContinueConfig(agentConfig) {
        try {
            // Obter configuração atual do Continue
            const continueConfig = vscode.workspace.getConfiguration('continue');
            
            // Atualizar com configurações do agente
            await continueConfig.update('models', agentConfig.models, vscode.ConfigurationTarget.Global);
            await continueConfig.update('contextProviders', agentConfig.contextProviders, vscode.ConfigurationTarget.Global);
            await continueConfig.update('slashCommands', agentConfig.slashCommands, vscode.ConfigurationTarget.Global);
            
            console.log('✅ Configuração do Continue atualizada');
        } catch (error) {
            console.warn('⚠️ Erro ao atualizar configuração do Continue:', error.message);
        }
    }

    async setupCommands() {
        console.log('⚙️ Configurando comandos...');
        
        // Importar e inicializar o agente principal
        const ContinueAgent = require('./core/agent-main');
        this.agent = new ContinueAgent();
        
        // Inicializar o agente
        await this.agent.initialize();
        
        console.log('✅ Comandos configurados');
    }

    async initializeComponents() {
        console.log('🔧 Inicializando componentes...');
        
        if (this.agent) {
            // Os componentes já foram inicializados no setupCommands
            console.log('✅ Componentes inicializados');
        } else {
            throw new Error('Agente não foi inicializado corretamente');
        }
    }

    showAvailableCommands() {
        const commands = [
            '📝 /edit - Editar arquivos com substituição ou inserção',
            '📄 /create - Criar novos arquivos',
            '🗑️ /delete - Deletar arquivos (com backup)',
            '📋 /copy - Copiar arquivos',
            '📦 /move - Mover/renomear arquivos',
            '📖 /read - Ler conteúdo de arquivos',
            '📁 /list - Listar conteúdo de diretórios',
            '📁 /mkdir - Criar diretórios',
            '↶ /undo - Desfazer última edição',
            '↷ /redo - Refazer edição desfeita',
            '🔍 /web-search - Buscar na web',
            '🌐 /fetch-web - Buscar conteúdo de URL',
            '🔍 /codebase - Buscar no codebase',
            '📊 /diagram - Criar diagramas Mermaid',
            '📋 /task - Gerenciar tarefas'
        ];

        const panel = vscode.window.createWebviewPanel(
            'continueAgentCommands',
            'Continue Agent - Comandos Disponíveis',
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );

        panel.webview.html = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Continue Agent - Comandos</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                h1 { color: #0066cc; }
                .command { 
                    background: #f5f5f5; 
                    padding: 10px; 
                    margin: 5px 0; 
                    border-radius: 5px; 
                    border-left: 4px solid #0066cc;
                }
                .description { color: #666; font-size: 14px; margin-top: 20px; }
            </style>
        </head>
        <body>
            <h1>🤖 Continue Agent - Comandos Disponíveis</h1>
            <div class="description">
                Use estes comandos no chat do Continue para interagir com o agente:
            </div>
            ${commands.map(cmd => `<div class="command">${cmd}</div>`).join('')}
            <div class="description">
                <strong>Como usar:</strong><br>
                1. Abra o chat do Continue (Ctrl+Shift+P → "Continue: Open Chat")<br>
                2. Digite um dos comandos acima<br>
                3. Siga as instruções interativas<br><br>
                <strong>Exemplo:</strong> Digite "/edit" para editar um arquivo
            </div>
        </body>
        </html>`;
    }

    openLMStudioConfig() {
        const panel = vscode.window.createWebviewPanel(
            'lmstudioConfig',
            'Configuração do LM Studio',
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );

        panel.webview.html = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Configuração do LM Studio</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                h1 { color: #0066cc; }
                .step { 
                    background: #f5f5f5; 
                    padding: 15px; 
                    margin: 10px 0; 
                    border-radius: 5px; 
                    border-left: 4px solid #0066cc;
                }
                .code { 
                    background: #2d2d2d; 
                    color: #f8f8f2; 
                    padding: 10px; 
                    border-radius: 5px; 
                    font-family: 'Courier New', monospace;
                    margin: 10px 0;
                }
                .warning { 
                    background: #fff3cd; 
                    border: 1px solid #ffeaa7; 
                    padding: 10px; 
                    border-radius: 5px; 
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
            <h1>🚀 Configuração do LM Studio</h1>
            
            <div class="step">
                <h3>1. Instalar LM Studio</h3>
                <p>Baixe e instale o LM Studio de: <a href="https://lmstudio.ai">https://lmstudio.ai</a></p>
            </div>
            
            <div class="step">
                <h3>2. Baixar o Modelo</h3>
                <p>No LM Studio, procure e baixe o modelo:</p>
                <div class="code">mradermacher/Qwen3-30B-A3B-python-coder-i1-GGUF</div>
                <p>Selecione a versão: <strong>Qwen3-30B-A3B-python-coder.i1-Q4_K_S.gguf</strong></p>
            </div>
            
            <div class="step">
                <h3>3. Iniciar o Servidor</h3>
                <p>1. Carregue o modelo no LM Studio</p>
                <p>2. Vá para a aba "Local Server"</p>
                <p>3. Configure a porta para <strong>1234</strong></p>
                <p>4. Clique em "Start Server"</p>
            </div>
            
            <div class="step">
                <h3>4. Verificar Configuração</h3>
                <p>O modelo deve estar localizado em:</p>
                <div class="code">C:\\Users\\<USER>\\.lmstudio\\models\\mradermacher\\Qwen3-30B-A3B-python-coder-i1-GGUF\\Qwen3-30B-A3B-python-coder.i1-Q4_K_S.gguf</div>
            </div>
            
            <div class="warning">
                <strong>⚠️ Importante:</strong> O LM Studio deve estar rodando na porta 1234 para o Continue Agent funcionar corretamente.
            </div>
            
            <div class="step">
                <h3>5. Testar Conexão</h3>
                <p>Após configurar, teste a conexão abrindo: <a href="http://localhost:1234">http://localhost:1234</a></p>
            </div>
        </body>
        </html>`;
    }

    async uninstall() {
        try {
            console.log('🗑️ Desinstalando Continue Agent...');
            
            // Remover configurações
            const continueConfig = vscode.workspace.getConfiguration('continue');
            await continueConfig.update('models', undefined, vscode.ConfigurationTarget.Global);
            
            this.isInstalled = false;
            console.log('✅ Continue Agent desinstalado');
            
            vscode.window.showInformationMessage('Continue Agent desinstalado com sucesso');
            
            return { success: true, message: 'Desinstalação concluída' };
            
        } catch (error) {
            console.error('❌ Erro na desinstalação:', error);
            return { success: false, error: error.message };
        }
    }

    getStatus() {
        return {
            installed: this.isInstalled,
            agentPath: this.agentPath,
            configPath: this.configPath,
            extensionPath: this.extensionPath
        };
    }
}

// Exportar para uso em outros módulos
module.exports = ContinueAgentInstaller;

// Auto-instalação se executado diretamente
if (require.main === module) {
    const installer = new ContinueAgentInstaller();
    installer.install().then(result => {
        if (result.success) {
            console.log('🎉 Instalação automática concluída!');
        } else {
            console.error('❌ Falha na instalação automática:', result.error);
        }
    });
}
