/**
 * Code Analyzer - Analisador de Código Avançado
 * Análise profunda de código com detecção de problemas e sugestões
 */

const vscode = require('vscode');

class CodeAnalyzer {
    constructor() {
        this.analysisRules = new Map();
        this.qualityMetrics = new Map();
        this.isInitialized = false;
    }

    async initialize() {
        if (this.isInitialized) return;

        console.log('🔧 Inicializando Analisador de Código...');
        
        this.setupAnalysisRules();
        this.setupQualityMetrics();
        
        this.isInitialized = true;
        console.log('✅ Analisador de Código inicializado');
    }

    setupAnalysisRules() {
        // Regras para JavaScript/TypeScript
        this.analysisRules.set('javascript', {
            complexity: this.analyzeComplexity.bind(this),
            security: this.analyzeSecurityJS.bind(this),
            performance: this.analyzePerformanceJS.bind(this),
            maintainability: this.analyzeMaintainabilityJS.bind(this),
            bugs: this.analyzeBugsJS.bind(this)
        });

        // Regras para Python
        this.analysisRules.set('python', {
            complexity: this.analyzeComplexity.bind(this),
            security: this.analyzeSecurityPython.bind(this),
            performance: this.analyzePerformancePython.bind(this),
            maintainability: this.analyzeMaintainabilityPython.bind(this),
            bugs: this.analyzeBugsPython.bind(this)
        });

        // Regras genéricas
        this.analysisRules.set('generic', {
            complexity: this.analyzeComplexity.bind(this),
            structure: this.analyzeStructure.bind(this),
            documentation: this.analyzeDocumentation.bind(this),
            naming: this.analyzeNaming.bind(this)
        });
    }

    setupQualityMetrics() {
        this.qualityMetrics.set('cyclomaticComplexity', this.calculateCyclomaticComplexity.bind(this));
        this.qualityMetrics.set('linesOfCode', this.calculateLinesOfCode.bind(this));
        this.qualityMetrics.set('maintainabilityIndex', this.calculateMaintainabilityIndex.bind(this));
        this.qualityMetrics.set('technicalDebt', this.calculateTechnicalDebt.bind(this));
    }

    async analyzeCode(code, context) {
        const language = this.detectLanguage(context.currentFile?.language || 'generic');
        
        const analysis = {
            language,
            summary: '',
            issues: [],
            suggestions: [],
            metrics: {},
            security: [],
            performance: [],
            maintainability: [],
            bugs: [],
            refactoringOpportunities: []
        };

        try {
            // Análise básica
            analysis.metrics = await this.calculateMetrics(code);
            
            // Análise específica da linguagem
            const rules = this.analysisRules.get(language) || this.analysisRules.get('generic');
            
            if (rules.complexity) {
                const complexityIssues = await rules.complexity(code);
                analysis.issues.push(...complexityIssues);
            }

            if (rules.security) {
                const securityIssues = await rules.security(code);
                analysis.security.push(...securityIssues);
            }

            if (rules.performance) {
                const performanceIssues = await rules.performance(code);
                analysis.performance.push(...performanceIssues);
            }

            if (rules.maintainability) {
                const maintainabilityIssues = await rules.maintainability(code);
                analysis.maintainability.push(...maintainabilityIssues);
            }

            if (rules.bugs) {
                const bugIssues = await rules.bugs(code);
                analysis.bugs.push(...bugIssues);
            }

            // Análise de contexto
            if (context.relatedFiles) {
                analysis.contextAnalysis = await this.analyzeContext(code, context);
            }

            // Gerar resumo
            analysis.summary = this.generateSummary(analysis);
            
            // Gerar sugestões
            analysis.suggestions = this.generateSuggestions(analysis);

            return analysis;

        } catch (error) {
            console.error('Erro na análise de código:', error);
            analysis.issues.push('Erro durante a análise: ' + error.message);
            return analysis;
        }
    }

    detectLanguage(language) {
        const languageMap = {
            'javascript': 'javascript',
            'typescript': 'javascript',
            'javascriptreact': 'javascript',
            'typescriptreact': 'javascript',
            'python': 'python',
            'java': 'java',
            'csharp': 'csharp',
            'cpp': 'cpp',
            'c': 'c'
        };

        return languageMap[language] || 'generic';
    }

    async calculateMetrics(code) {
        const metrics = {};
        
        for (const [metricName, calculator] of this.qualityMetrics) {
            try {
                metrics[metricName] = await calculator(code);
            } catch (error) {
                console.error(`Erro ao calcular métrica ${metricName}:`, error);
                metrics[metricName] = 0;
            }
        }

        return metrics;
    }

    calculateCyclomaticComplexity(code) {
        // Contar estruturas de controle
        const controlStructures = [
            /if\s*\(/g,
            /else\s+if\s*\(/g,
            /while\s*\(/g,
            /for\s*\(/g,
            /switch\s*\(/g,
            /case\s+/g,
            /catch\s*\(/g,
            /&&/g,
            /\|\|/g,
            /\?/g
        ];

        let complexity = 1; // Base complexity
        
        controlStructures.forEach(regex => {
            const matches = code.match(regex);
            if (matches) {
                complexity += matches.length;
            }
        });

        return complexity;
    }

    calculateLinesOfCode(code) {
        const lines = code.split('\n');
        const nonEmptyLines = lines.filter(line => line.trim().length > 0);
        const commentLines = lines.filter(line => {
            const trimmed = line.trim();
            return trimmed.startsWith('//') || trimmed.startsWith('#') || 
                   trimmed.startsWith('/*') || trimmed.startsWith('*');
        });

        return {
            total: lines.length,
            code: nonEmptyLines.length - commentLines.length,
            comments: commentLines.length,
            blank: lines.length - nonEmptyLines.length
        };
    }

    calculateMaintainabilityIndex(code) {
        const loc = this.calculateLinesOfCode(code);
        const complexity = this.calculateCyclomaticComplexity(code);
        
        // Fórmula simplificada do índice de manutenibilidade
        const maintainabilityIndex = Math.max(0, 
            171 - 5.2 * Math.log(loc.code) - 0.23 * complexity - 16.2 * Math.log(loc.code)
        );

        return Math.round(maintainabilityIndex);
    }

    calculateTechnicalDebt(code) {
        let debtScore = 0;
        
        // TODOs e FIXMEs
        const todoMatches = code.match(/TODO|FIXME|HACK|XXX/gi);
        if (todoMatches) debtScore += todoMatches.length * 2;
        
        // Código duplicado (simplificado)
        const lines = code.split('\n').map(line => line.trim()).filter(line => line.length > 10);
        const duplicates = lines.filter((line, index) => lines.indexOf(line) !== index);
        debtScore += duplicates.length;
        
        // Funções muito longas
        const functionMatches = code.match(/function\s+\w+[^{]*{[^}]*}/g);
        if (functionMatches) {
            functionMatches.forEach(func => {
                const funcLines = func.split('\n').length;
                if (funcLines > 50) debtScore += 5;
                else if (funcLines > 30) debtScore += 2;
            });
        }

        return debtScore;
    }

    async analyzeComplexity(code) {
        const issues = [];
        const complexity = this.calculateCyclomaticComplexity(code);
        
        if (complexity > 15) {
            issues.push({
                type: 'complexity',
                severity: 'high',
                message: `Complexidade ciclomática muito alta: ${complexity}. Considere refatorar.`,
                suggestion: 'Divida a função em funções menores ou simplifique a lógica condicional.'
            });
        } else if (complexity > 10) {
            issues.push({
                type: 'complexity',
                severity: 'medium',
                message: `Complexidade ciclomática alta: ${complexity}. Considere simplificar.`,
                suggestion: 'Revise a lógica condicional e considere extrair algumas condições.'
            });
        }

        return issues;
    }

    async analyzeSecurityJS(code) {
        const issues = [];
        
        // Verificar eval()
        if (code.includes('eval(')) {
            issues.push({
                type: 'security',
                severity: 'high',
                message: 'Uso de eval() detectado - risco de segurança',
                suggestion: 'Evite usar eval(). Use JSON.parse() ou outras alternativas seguras.'
            });
        }

        // Verificar innerHTML
        if (code.includes('innerHTML')) {
            issues.push({
                type: 'security',
                severity: 'medium',
                message: 'Uso de innerHTML pode ser vulnerável a XSS',
                suggestion: 'Use textContent ou sanitize o conteúdo antes de inserir.'
            });
        }

        // Verificar console.log em produção
        if (code.includes('console.log')) {
            issues.push({
                type: 'security',
                severity: 'low',
                message: 'console.log pode vazar informações sensíveis',
                suggestion: 'Remova console.log em código de produção.'
            });
        }

        return issues;
    }

    async analyzePerformanceJS(code) {
        const issues = [];
        
        // Loops aninhados
        const nestedLoopRegex = /for\s*\([^}]*for\s*\(/g;
        if (nestedLoopRegex.test(code)) {
            issues.push({
                type: 'performance',
                severity: 'medium',
                message: 'Loops aninhados detectados - possível problema de performance',
                suggestion: 'Considere otimizar a lógica ou usar estruturas de dados mais eficientes.'
            });
        }

        // Uso de document.getElementById em loops
        if (code.includes('document.getElementById') && /for|while/.test(code)) {
            issues.push({
                type: 'performance',
                severity: 'medium',
                message: 'Acesso ao DOM dentro de loops pode ser lento',
                suggestion: 'Cache as referências do DOM fora do loop.'
            });
        }

        return issues;
    }

    async analyzeMaintainabilityJS(code) {
        const issues = [];
        const loc = this.calculateLinesOfCode(code);
        
        // Funções muito longas
        const functionRegex = /function\s+\w+[^{]*{([^{}]*{[^{}]*}[^{}]*)*[^{}]*}/g;
        let match;
        while ((match = functionRegex.exec(code)) !== null) {
            const funcLines = match[0].split('\n').length;
            if (funcLines > 50) {
                issues.push({
                    type: 'maintainability',
                    severity: 'high',
                    message: `Função muito longa: ${funcLines} linhas`,
                    suggestion: 'Divida a função em funções menores e mais específicas.'
                });
            }
        }

        // Falta de comentários
        if (loc.comments / loc.code < 0.1) {
            issues.push({
                type: 'maintainability',
                severity: 'medium',
                message: 'Código com poucos comentários',
                suggestion: 'Adicione comentários explicativos para melhorar a legibilidade.'
            });
        }

        return issues;
    }

    async analyzeBugsJS(code) {
        const issues = [];
        
        // Comparação com ==
        if (code.includes('==') && !code.includes('===')) {
            issues.push({
                type: 'bug',
                severity: 'medium',
                message: 'Uso de == pode causar comparações inesperadas',
                suggestion: 'Use === para comparações estritas.'
            });
        }

        // Variáveis não declaradas
        const undeclaredVarRegex = /(?<!var\s|let\s|const\s)\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g;
        let match;
        while ((match = undeclaredVarRegex.exec(code)) !== null) {
            if (!['window', 'document', 'console'].includes(match[1])) {
                issues.push({
                    type: 'bug',
                    severity: 'high',
                    message: `Possível variável não declarada: ${match[1]}`,
                    suggestion: 'Declare a variável com var, let ou const.'
                });
            }
        }

        return issues;
    }

    async analyzeSecurityPython(code) {
        const issues = [];
        
        // Verificar eval()
        if (code.includes('eval(')) {
            issues.push({
                type: 'security',
                severity: 'high',
                message: 'Uso de eval() detectado - risco de segurança',
                suggestion: 'Evite usar eval(). Use ast.literal_eval() ou outras alternativas seguras.'
            });
        }

        // Verificar exec()
        if (code.includes('exec(')) {
            issues.push({
                type: 'security',
                severity: 'high',
                message: 'Uso de exec() detectado - risco de segurança',
                suggestion: 'Evite usar exec() com entrada não confiável.'
            });
        }

        return issues;
    }

    async analyzePerformancePython(code) {
        const issues = [];
        
        // Loops com append
        if (code.includes('.append(') && /for\s+\w+\s+in/.test(code)) {
            issues.push({
                type: 'performance',
                severity: 'medium',
                message: 'Uso de append() em loops pode ser lento',
                suggestion: 'Considere usar list comprehension ou extend().'
            });
        }

        return issues;
    }

    async analyzeMaintainabilityPython(code) {
        const issues = [];
        
        // Linhas muito longas
        const lines = code.split('\n');
        lines.forEach((line, index) => {
            if (line.length > 120) {
                issues.push({
                    type: 'maintainability',
                    severity: 'low',
                    message: `Linha ${index + 1} muito longa: ${line.length} caracteres`,
                    suggestion: 'Mantenha as linhas com menos de 120 caracteres.'
                });
            }
        });

        return issues;
    }

    async analyzeBugsPython(code) {
        const issues = [];
        
        // Imports não utilizados
        const importRegex = /^import\s+(\w+)|^from\s+\w+\s+import\s+(\w+)/gm;
        let match;
        const imports = [];
        while ((match = importRegex.exec(code)) !== null) {
            const importName = match[1] || match[2];
            if (importName && !code.includes(importName + '.') && !code.includes(importName + '(')) {
                imports.push(importName);
            }
        }
        
        if (imports.length > 0) {
            issues.push({
                type: 'bug',
                severity: 'low',
                message: `Imports não utilizados: ${imports.join(', ')}`,
                suggestion: 'Remova imports não utilizados para limpar o código.'
            });
        }

        return issues;
    }

    async analyzeStructure(code) {
        const issues = [];
        const lines = code.split('\n');
        
        // Verificar indentação inconsistente
        const indentations = lines
            .filter(line => line.trim().length > 0)
            .map(line => line.match(/^\s*/)[0].length);
        
        const hasInconsistentIndentation = indentations.some((indent, index) => {
            if (index === 0) return false;
            const prevIndent = indentations[index - 1];
            const diff = Math.abs(indent - prevIndent);
            return diff !== 0 && diff !== 2 && diff !== 4;
        });

        if (hasInconsistentIndentation) {
            issues.push({
                type: 'structure',
                severity: 'medium',
                message: 'Indentação inconsistente detectada',
                suggestion: 'Use indentação consistente (2 ou 4 espaços).'
            });
        }

        return issues;
    }

    async analyzeDocumentation(code) {
        const issues = [];
        const loc = this.calculateLinesOfCode(code);
        
        if (loc.comments / loc.code < 0.05) {
            issues.push({
                type: 'documentation',
                severity: 'medium',
                message: 'Código com documentação insuficiente',
                suggestion: 'Adicione mais comentários e documentação.'
            });
        }

        return issues;
    }

    async analyzeNaming(code) {
        const issues = [];
        
        // Verificar nomes de variáveis muito curtos
        const shortVarRegex = /(?:var|let|const)\s+([a-z])\s*=/g;
        let match;
        while ((match = shortVarRegex.exec(code)) !== null) {
            if (!['i', 'j', 'k', 'x', 'y', 'z'].includes(match[1])) {
                issues.push({
                    type: 'naming',
                    severity: 'low',
                    message: `Nome de variável muito curto: ${match[1]}`,
                    suggestion: 'Use nomes mais descritivos para variáveis.'
                });
            }
        }

        return issues;
    }

    async analyzeContext(code, context) {
        const analysis = {
            dependencies: [],
            impacts: [],
            suggestions: []
        };

        // Analisar dependências
        if (context.relatedFiles) {
            for (const file of context.relatedFiles) {
                analysis.dependencies.push({
                    file: file.path,
                    relationship: 'import',
                    symbols: file.exports || []
                });
            }
        }

        // Analisar impactos potenciais
        if (context.symbols) {
            for (const symbol of context.symbols) {
                if (symbol.file !== context.currentFile?.path) {
                    analysis.impacts.push({
                        symbol: symbol.name,
                        file: symbol.file,
                        type: symbol.type,
                        message: `Mudanças podem afetar ${symbol.name} em ${symbol.file}`
                    });
                }
            }
        }

        return analysis;
    }

    generateSummary(analysis) {
        const totalIssues = analysis.issues.length + analysis.security.length + 
                           analysis.performance.length + analysis.maintainability.length + 
                           analysis.bugs.length;

        let summary = `Análise concluída. `;
        
        if (totalIssues === 0) {
            summary += 'Nenhum problema encontrado. Código em boa qualidade.';
        } else {
            summary += `${totalIssues} problema(s) encontrado(s). `;
            
            const highSeverity = this.countBySeverity(analysis, 'high');
            const mediumSeverity = this.countBySeverity(analysis, 'medium');
            const lowSeverity = this.countBySeverity(analysis, 'low');
            
            if (highSeverity > 0) summary += `${highSeverity} crítico(s). `;
            if (mediumSeverity > 0) summary += `${mediumSeverity} médio(s). `;
            if (lowSeverity > 0) summary += `${lowSeverity} menor(es). `;
        }

        if (analysis.metrics.cyclomaticComplexity > 10) {
            summary += 'Complexidade alta detectada. ';
        }

        if (analysis.metrics.maintainabilityIndex < 50) {
            summary += 'Índice de manutenibilidade baixo. ';
        }

        return summary;
    }

    countBySeverity(analysis, severity) {
        let count = 0;
        const allIssues = [
            ...analysis.issues,
            ...analysis.security,
            ...analysis.performance,
            ...analysis.maintainability,
            ...analysis.bugs
        ];
        
        return allIssues.filter(issue => issue.severity === severity).length;
    }

    generateSuggestions(analysis) {
        const suggestions = [];
        
        // Sugestões baseadas em métricas
        if (analysis.metrics.cyclomaticComplexity > 15) {
            suggestions.push('Refatore funções complexas em funções menores');
        }
        
        if (analysis.metrics.maintainabilityIndex < 50) {
            suggestions.push('Melhore a documentação e simplifique o código');
        }
        
        if (analysis.metrics.technicalDebt > 10) {
            suggestions.push('Resolva TODOs e elimine código duplicado');
        }

        // Sugestões baseadas em problemas
        const hasSecurityIssues = analysis.security.length > 0;
        const hasPerformanceIssues = analysis.performance.length > 0;
        
        if (hasSecurityIssues) {
            suggestions.push('Revise e corrija problemas de segurança identificados');
        }
        
        if (hasPerformanceIssues) {
            suggestions.push('Otimize código para melhor performance');
        }

        return suggestions;
    }

    async optimizeCode(code, context) {
        // Esta função será implementada para otimização automática
        // Por enquanto, retorna sugestões de otimização
        const analysis = await this.analyzeCode(code, context);
        
        let optimizedCode = code;
        let optimizations = [];

        // Aplicar otimizações básicas
        if (analysis.language === 'javascript') {
            // Substituir == por ===
            optimizedCode = optimizedCode.replace(/([^=!])={2}([^=])/g, '$1===$2');
            if (optimizedCode !== code) {
                optimizations.push('Substituído == por === para comparações estritas');
            }
        }

        return {
            code: optimizedCode,
            optimizations,
            analysis
        };
    }
}

module.exports = CodeAnalyzer;
