/**
 * Integrador do Continue Agent
 * Script para integrar o agente na configuração atual do Continue
 */

// Verificação de erro zero: módulos nativos do Node.js sempre disponíveis
const fs = require('fs').promises;
const fsSync = require('fs'); // Para verificações síncronas quando necessário
const path = require('path');
const os = require('os');

class ContinueAgentIntegrator {
    constructor() {
        this.agentPath = __dirname;
        this.agentConfigPath = path.join(this.agentPath, 'config.json');
        
        // Caminho da configuração do Continue do usuário
        this.continueConfigDir = path.join(os.homedir(), '.continue');
        this.continueConfigPath = path.join(this.continueConfigDir, 'config.json');
        
        console.log('📁 Diretório do agente:', this.agentPath);
        console.log('📁 Configuração do Continue:', this.continueConfigPath);
    }

    async integrate() {
        try {
            console.log('🚀 Iniciando integração do Continue Agent...');
            
            // Verificar se a pasta .continue existe
            await this.ensureContinueConfigDir();
            
            // Carregar configuração do agente
            const agentConfig = await this.loadAgentConfig();
            
            // Carregar configuração atual do Continue (se existir)
            const currentConfig = await this.loadCurrentContinueConfig();
            
            // Mesclar configurações
            const mergedConfig = this.mergeConfigurations(currentConfig, agentConfig);
            
            // Adicionar configurações específicas do agente
            this.addAgentSpecificConfig(mergedConfig);
            
            // Salvar configuração integrada
            await this.saveContinueConfig(mergedConfig);
            
            // Criar arquivo de extensão do agente
            await this.createAgentExtension();
            
            console.log('✅ Continue Agent integrado com sucesso!');
            console.log('');
            console.log('🎉 AGENTE ATIVADO! Agora você pode usar:');
            console.log('   • Comandos slash: /edit, /create, /web-search, etc.');
            console.log('   • Comandos personalizados no menu do Continue');
            console.log('   • Funcionalidades avançadas de edição de arquivos');
            console.log('');
            console.log('📋 Para usar o agente:');
            console.log('   1. Abra o Continue (Ctrl+Shift+L)');
            console.log('   2. Digite um comando como "/edit" ou "/web-search"');
            console.log('   3. O botão "Agent" agora deve estar ativo!');
            
            return { success: true, message: 'Integração concluída com sucesso' };
            
        } catch (error) {
            console.error('❌ Erro na integração:', error);
            return { success: false, error: error.message };
        }
    }

    async ensureContinueConfigDir() {
        try {
            // Erro zero: verificar se o diretório pai existe antes de criar
            const parentDir = path.dirname(this.continueConfigDir);
            if (!fsSync.existsSync(parentDir)) {
                throw new Error(`Diretório pai não existe: ${parentDir}`);
            }

            await fs.mkdir(this.continueConfigDir, { recursive: true });
            console.log('✅ Diretório .continue verificado');
        } catch (error) {
            throw new Error(`Erro ao criar diretório .continue: ${error.message}`);
        }
    }

    async loadAgentConfig() {
        try {
            // Erro zero: verificar se o arquivo existe antes de tentar ler
            if (!fsSync.existsSync(this.agentConfigPath)) {
                throw new Error(`Arquivo de configuração não encontrado: ${this.agentConfigPath}`);
            }

            const configContent = await fs.readFile(this.agentConfigPath, 'utf8');

            // Erro zero: validar se o conteúdo é JSON válido
            if (!configContent.trim()) {
                throw new Error('Arquivo de configuração está vazio');
            }

            const config = JSON.parse(configContent);

            // Erro zero: validar estrutura mínima necessária
            if (!config.models || !Array.isArray(config.models)) {
                throw new Error('Configuração inválida: propriedade "models" não encontrada ou não é array');
            }

            console.log('✅ Configuração do agente carregada');
            return config;
        } catch (error) {
            throw new Error(`Erro ao carregar configuração do agente: ${error.message}`);
        }
    }

    async loadCurrentContinueConfig() {
        try {
            // Erro zero: verificar se arquivo existe antes de tentar ler
            if (!fsSync.existsSync(this.continueConfigPath)) {
                console.log('ℹ️ Nenhuma configuração existente encontrada, criando nova');
                return {};
            }

            const configContent = await fs.readFile(this.continueConfigPath, 'utf8');

            // Erro zero: verificar se conteúdo não está vazio
            if (!configContent.trim()) {
                console.log('ℹ️ Arquivo de configuração vazio, criando nova configuração');
                return {};
            }

            // Erro zero: tentar parse JSON com tratamento de erro
            let config;
            try {
                config = JSON.parse(configContent);
            } catch (parseError) {
                console.log('⚠️ Configuração existente com JSON inválido, criando nova');
                return {};
            }

            console.log('✅ Configuração atual do Continue carregada');
            return config || {}; // Erro zero: garantir que sempre retorna objeto
        } catch (error) {
            console.log('ℹ️ Erro ao carregar configuração existente, criando nova:', error.message);
            return {};
        }
    }

    mergeConfigurations(currentConfig, agentConfig) {
        console.log('🔄 Mesclando configurações...');

        // Erro zero: garantir que currentConfig é um objeto válido
        const safeCurrentConfig = currentConfig && typeof currentConfig === 'object' ? currentConfig : {};
        const merged = { ...safeCurrentConfig };

        // Erro zero: validar agentConfig antes de usar
        if (!agentConfig || typeof agentConfig !== 'object') {
            console.warn('⚠️ Configuração do agente inválida, usando configuração atual');
            return merged;
        }

        // Mesclar modelos (manter existentes e adicionar do agente)
        if (agentConfig.models && Array.isArray(agentConfig.models)) {
            merged.models = Array.isArray(merged.models) ? merged.models : [];

            // Adicionar modelos do agente se não existirem
            agentConfig.models.forEach(agentModel => {
                // Erro zero: validar estrutura do modelo antes de adicionar
                if (!agentModel || typeof agentModel !== 'object' || !agentModel.title) {
                    console.warn('⚠️ Modelo inválido ignorado:', agentModel);
                    return;
                }

                const exists = merged.models.some(model =>
                    model && (model.title === agentModel.title || model.model === agentModel.model)
                );

                if (!exists) {
                    merged.models.push(agentModel);
                }
            });
        }
        
        // Mesclar provedores de contexto
        if (agentConfig.contextProviders) {
            merged.contextProviders = merged.contextProviders || [];
            
            agentConfig.contextProviders.forEach(agentProvider => {
                const exists = merged.contextProviders.some(provider => 
                    provider.name === agentProvider.name
                );
                
                if (!exists) {
                    merged.contextProviders.push(agentProvider);
                }
            });
        }
        
        // Mesclar comandos slash
        if (agentConfig.slashCommands) {
            merged.slashCommands = merged.slashCommands || [];
            
            agentConfig.slashCommands.forEach(agentCommand => {
                const exists = merged.slashCommands.some(command => 
                    command.name === agentCommand.name
                );
                
                if (!exists) {
                    merged.slashCommands.push(agentCommand);
                }
            });
        }
        
        // Mesclar comandos personalizados
        if (agentConfig.customCommands) {
            merged.customCommands = merged.customCommands || [];
            
            agentConfig.customCommands.forEach(agentCommand => {
                const exists = merged.customCommands.some(command => 
                    command.name === agentCommand.name
                );
                
                if (!exists) {
                    merged.customCommands.push(agentCommand);
                }
            });
        }
        
        // Adicionar outras configurações do agente
        if (agentConfig.tabAutocompleteModel && !merged.tabAutocompleteModel) {
            merged.tabAutocompleteModel = agentConfig.tabAutocompleteModel;
        }
        
        if (agentConfig.embeddingsProvider && !merged.embeddingsProvider) {
            merged.embeddingsProvider = agentConfig.embeddingsProvider;
        }
        
        if (agentConfig.experimentalFeatures) {
            merged.experimentalFeatures = {
                ...merged.experimentalFeatures,
                ...agentConfig.experimentalFeatures
            };
        }
        
        console.log('✅ Configurações mescladas');
        return merged;
    }

    addAgentSpecificConfig(config) {
        console.log('⚙️ Adicionando configurações específicas do agente...');
        
        // Adicionar configurações para habilitar o botão Agent
        config.agentConfig = {
            enabled: true,
            agentPath: this.agentPath,
            version: "1.0.0",
            capabilities: [
                "file-editing",
                "web-search",
                "codebase-analysis",
                "task-management",
                "code-refactoring",
                "test-generation",
                "debugging"
            ]
        };
        
        // Configurações para habilitar funcionalidades avançadas
        config.experimentalFeatures = {
            ...config.experimentalFeatures,
            agentMode: true,
            advancedFileEditing: true,
            webSearchIntegration: true,
            contextRetrieval: true,
            codebaseIndexing: true,
            smartRefactoring: true,
            errorPrevention: true
        };
        
        // Adicionar system message específico para o agente
        if (config.models && config.models.length > 0) {
            config.models[0].systemMessage = 
                "Você é o Continue Agent, um assistente de programação avançado com acesso completo ao sistema de arquivos e ferramentas de desenvolvimento. " +
                "Você pode editar arquivos, criar projetos, analisar código, buscar na web e gerenciar tarefas. " +
                "Sempre pense cuidadosamente antes de fazer mudanças e siga as melhores práticas de programação. " +
                "Use os comandos /edit, /create, /web-search, /codebase, /task e outros para interagir com o sistema.";
        }
        
        console.log('✅ Configurações específicas do agente adicionadas');
    }

    async saveContinueConfig(config) {
        try {
            const configJson = JSON.stringify(config, null, 2);
            await fs.writeFile(this.continueConfigPath, configJson, 'utf8');
            console.log('✅ Configuração do Continue salva');
        } catch (error) {
            throw new Error(`Erro ao salvar configuração: ${error.message}`);
        }
    }

    async createAgentExtension() {
        try {
            console.log('📦 Criando extensão do agente...');
            
            const extensionPath = path.join(this.continueConfigDir, 'agent-extension.js');
            
            const extensionCode = `
/**
 * Continue Agent Extension
 * Extensão para habilitar funcionalidades avançadas do agente
 */

const path = require('path');

// Caminho para o agente
const AGENT_PATH = '${this.agentPath.replace(/\\/g, '\\\\')}';

// Registrar o agente
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        agentPath: AGENT_PATH,
        enabled: true,
        
        // Função para inicializar o agente
        async initializeAgent() {
            try {
                const ContinueAgent = require(path.join(AGENT_PATH, 'core', 'agent-main.js'));
                const agent = new ContinueAgent();
                await agent.initialize();
                
                console.log('🤖 Continue Agent inicializado com sucesso!');
                return agent;
            } catch (error) {
                console.error('❌ Erro ao inicializar Continue Agent:', error);
                return null;
            }
        },
        
        // Comandos disponíveis
        commands: [
            '/edit - Editar arquivos',
            '/create - Criar arquivos',
            '/delete - Deletar arquivos',
            '/copy - Copiar arquivos',
            '/move - Mover arquivos',
            '/read - Ler arquivos',
            '/list - Listar diretórios',
            '/mkdir - Criar diretórios',
            '/web-search - Buscar na web',
            '/codebase - Buscar no codebase',
            '/task - Gerenciar tarefas'
        ]
    };
}

// Auto-inicialização
if (typeof window === 'undefined') {
    // Ambiente Node.js
    const initAgent = async () => {
        try {
            const ContinueAgent = require(path.join(AGENT_PATH, 'core', 'agent-main.js'));
            const agent = new ContinueAgent();
            await agent.initialize();
            console.log('🤖 Continue Agent auto-inicializado!');
        } catch (error) {
            console.log('ℹ️ Continue Agent será inicializado quando necessário');
        }
    };
    
    // Inicializar após um pequeno delay
    setTimeout(initAgent, 1000);
}
`;
            
            await fs.writeFile(extensionPath, extensionCode, 'utf8');
            console.log('✅ Extensão do agente criada');
            
        } catch (error) {
            console.warn('⚠️ Erro ao criar extensão do agente:', error.message);
        }
    }

    async checkIntegration() {
        try {
            console.log('🔍 Verificando integração...');
            
            // Verificar se o arquivo de configuração existe
            const configExists = await this.fileExists(this.continueConfigPath);
            if (!configExists) {
                return { success: false, message: 'Arquivo de configuração não encontrado' };
            }
            
            // Carregar e verificar configuração
            const config = await this.loadCurrentContinueConfig();
            
            const hasAgentConfig = config.agentConfig && config.agentConfig.enabled;
            const hasAgentModels = config.models && config.models.some(m => m.title.includes('Qwen3'));
            const hasAgentCommands = config.slashCommands && config.slashCommands.some(c => c.name === 'edit');
            
            console.log('📊 Status da integração:');
            console.log('   • Configuração do agente:', hasAgentConfig ? '✅' : '❌');
            console.log('   • Modelos configurados:', hasAgentModels ? '✅' : '❌');
            console.log('   • Comandos disponíveis:', hasAgentCommands ? '✅' : '❌');
            
            const isIntegrated = hasAgentConfig && hasAgentModels && hasAgentCommands;
            
            return {
                success: isIntegrated,
                message: isIntegrated ? 'Agente integrado com sucesso' : 'Integração incompleta',
                details: {
                    agentConfig: hasAgentConfig,
                    models: hasAgentModels,
                    commands: hasAgentCommands
                }
            };
            
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    async showStatus() {
        console.log('📊 Status do Continue Agent:');
        console.log('');
        
        const status = await this.checkIntegration();
        
        if (status.success) {
            console.log('✅ AGENTE ATIVO E FUNCIONANDO!');
            console.log('');
            console.log('🎯 Como usar:');
            console.log('   1. Abra o Continue (Ctrl+Shift+L)');
            console.log('   2. Digite comandos como:');
            console.log('      • /edit - para editar arquivos');
            console.log('      • /web-search - para buscar na web');
            console.log('      • /codebase - para analisar código');
            console.log('   3. O botão "Agent" deve estar ativo!');
        } else {
            console.log('❌ Agente não está integrado corretamente');
            console.log('💡 Execute: node integrate-agent.js');
        }
        
        console.log('');
        console.log('📁 Arquivos importantes:');
        console.log('   • Configuração:', this.continueConfigPath);
        console.log('   • Agente:', this.agentPath);
        
        return status;
    }
}

// Exportar para uso em outros módulos
module.exports = ContinueAgentIntegrator;

// Execução direta
if (require.main === module) {
    const integrator = new ContinueAgentIntegrator();
    
    const args = process.argv.slice(2);
    
    if (args.includes('--status')) {
        integrator.showStatus();
    } else {
        integrator.integrate().then(result => {
            if (result.success) {
                console.log('');
                console.log('🎉 INTEGRAÇÃO CONCLUÍDA!');
                console.log('');
                console.log('🔄 REINICIE O VSCODE para ativar completamente o agente.');
                console.log('');
                console.log('📋 Após reiniciar:');
                console.log('   • Abra o Continue (Ctrl+Shift+L)');
                console.log('   • O botão "Agent" deve estar ativo');
                console.log('   • Use comandos como /edit, /web-search, etc.');
            } else {
                console.error('❌ Falha na integração:', result.error);
                process.exit(1);
            }
        });
    }
}
