/**
 * Continue_agente - Agente Principal
 * Núcleo do sistema de programação avançada
 */

const vscode = require('vscode');
const path = require('path');
const fs = require('fs');

// Importar componentes
const ContextEngine = require('./context-engine');
const CodeAnalyzer = require('./code-analyzer');
const WebSearchEngine = require('./web-search');
const TaskManager = require('./task-manager');
const FileManager = require('./file-manager');
const CodeEditor = require('./code-editor');

class ContinueAgent {
    constructor() {
        this.contextEngine = new ContextEngine();
        this.codeAnalyzer = new CodeAnalyzer();
        this.webSearchEngine = new WebSearchEngine();
        this.taskManager = new TaskManager();
        this.fileManager = new FileManager();
        this.codeEditor = new CodeEditor();

        this.config = this.loadConfig();
        this.rules = this.loadRules();
        this.isInitialized = false;
        this.memorySystem = new Map();
    }

    loadConfig() {
        try {
            const configPath = path.join(__dirname, '..', 'config.json');
            const configData = fs.readFileSync(configPath, 'utf8');
            return JSON.parse(configData);
        } catch (error) {
            console.error('Erro ao carregar configuração:', error);
            return this.getDefaultConfig();
        }
    }

    loadRules() {
        try {
            const rulesPath = path.join(__dirname, '..', 'rules.md');
            return fs.readFileSync(rulesPath, 'utf8');
        } catch (error) {
            console.error('Erro ao carregar regras:', error);
            return '';
        }
    }

    getDefaultConfig() {
        return {
            models: [{
                title: "Qwen3-30B-Python-Coder",
                provider: "lmstudio",
                model: "Qwen3-30B-A3B-python-coder.i1-Q4_K_S",
                apiBase: "http://localhost:1234/v1",
                apiKey: "lm-studio",
                contextLength: 32768,
                maxTokens: 4096,
                temperature: 0.1
            }]
        };
    }

    async initialize() {
        if (this.isInitialized) return;

        console.log('🚀 Inicializando Continue_agente...');
        
        try {
            // Verificar conexão com LM Studio
            await this.verifyLMStudioConnection();
            
            // Inicializar componentes
            await this.contextEngine.initialize();
            await this.codeAnalyzer.initialize();
            await this.webSearchEngine.initialize();
            await this.taskManager.initialize();
            await this.fileManager.initialize();
            await this.codeEditor.initialize();
            
            // Registrar comandos
            this.registerCommands();
            
            // Aplicar regras
            this.applyProgrammingRules();
            
            this.isInitialized = true;
            console.log('✅ Continue_agente inicializado com sucesso!');
            
        } catch (error) {
            console.error('❌ Erro na inicialização:', error);
            vscode.window.showErrorMessage('Erro ao inicializar Continue_agente');
        }
    }

    async verifyLMStudioConnection() {
        try {
            const response = await fetch(`${this.config.models[0].apiBase}/models`, {
                headers: {
                    'Authorization': `Bearer ${this.config.models[0].apiKey}`
                }
            });
            
            if (response.ok) {
                console.log('✅ Conexão com LM Studio estabelecida');
                return true;
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Erro na conexão com LM Studio:', error);
            vscode.window.showErrorMessage(
                'Não foi possível conectar ao LM Studio. Verifique se está rodando na porta 1234.'
            );
            return false;
        }
    }

    registerCommands() {
        const commands = [
            // Comandos básicos
            { command: 'continue-agent.analyzeCode', callback: this.analyzeCode.bind(this) },
            { command: 'continue-agent.refactorCode', callback: this.refactorCode.bind(this) },
            { command: 'continue-agent.generateTests', callback: this.generateTests.bind(this) },
            { command: 'continue-agent.debugCode', callback: this.debugCode.bind(this) },
            { command: 'continue-agent.optimizeCode', callback: this.optimizeCode.bind(this) },
            
            // Comandos avançados
            { command: 'continue-agent.searchWeb', callback: this.searchWeb.bind(this) },
            { command: 'continue-agent.fetchWebContent', callback: this.fetchWebContent.bind(this) },
            { command: 'continue-agent.codebaseRetrieval', callback: this.codebaseRetrieval.bind(this) },
            { command: 'continue-agent.createDiagram', callback: this.createDiagram.bind(this) },
            { command: 'continue-agent.manageTask', callback: this.manageTask.bind(this) },

            // Comandos de edição de arquivos
            { command: 'continue-agent.editFile', callback: this.editFile.bind(this) },
            { command: 'continue-agent.createFile', callback: this.createFile.bind(this) },
            { command: 'continue-agent.deleteFile', callback: this.deleteFile.bind(this) },
            { command: 'continue-agent.copyFile', callback: this.copyFile.bind(this) },
            { command: 'continue-agent.moveFile', callback: this.moveFile.bind(this) },
            { command: 'continue-agent.readFile', callback: this.readFile.bind(this) },
            { command: 'continue-agent.listDirectory', callback: this.listDirectory.bind(this) },
            { command: 'continue-agent.createDirectory', callback: this.createDirectory.bind(this) },
            { command: 'continue-agent.undoEdit', callback: this.undoEdit.bind(this) },
            { command: 'continue-agent.redoEdit', callback: this.redoEdit.bind(this) }
        ];

        commands.forEach(cmd => {
            vscode.commands.registerCommand(cmd.command, cmd.callback);
        });
    }

    applyProgrammingRules() {
        this.programmingRules = {
            preliminaryAnalysis: true,
            contextRetrieval: true,
            taskManagement: true,
            detailedPlanning: true,
            useStrReplaceEditor: true,
            codebaseRetrievalBeforeEdit: true,
            conservativeEditing: true,
            usePackageManagers: true,
            followUserInstructions: true,
            suggestTesting: true,
            useAugmentCodeSnippet: true,
            askForHelpWhenStuck: true
        };
    }

    // Comandos básicos
    async analyzeCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Nenhum arquivo aberto para análise');
            return;
        }

        try {
            vscode.window.showInformationMessage('🔍 Analisando código...');
            
            const document = editor.document;
            const selection = editor.selection;
            const code = selection.isEmpty ? document.getText() : document.getText(selection);
            
            // Seguir regras: análise preliminar e recuperação de contexto
            const context = await this.contextEngine.getRelevantContext(document.uri, code);
            const analysis = await this.codeAnalyzer.analyzeCode(code, context);
            
            this.showAnalysisResults(analysis);
            
        } catch (error) {
            console.error('Erro na análise:', error);
            vscode.window.showErrorMessage('Erro durante a análise do código');
        }
    }

    async searchWeb(query) {
        try {
            vscode.window.showInformationMessage(`🔍 Pesquisando: ${query}`);
            const results = await this.webSearchEngine.search(query, 5);
            this.showWebSearchResults(results);
        } catch (error) {
            console.error('Erro na pesquisa web:', error);
            vscode.window.showErrorMessage('Erro durante a pesquisa web');
        }
    }

    async codebaseRetrieval(query) {
        try {
            vscode.window.showInformationMessage(`🔍 Buscando no codebase: ${query}`);
            const results = await this.contextEngine.retrieveFromCodebase(query);
            this.showCodebaseResults(results);
        } catch (error) {
            console.error('Erro na busca do codebase:', error);
            vscode.window.showErrorMessage('Erro na busca do codebase');
        }
    }

    // Comandos de edição de arquivos
    async editFile() {
        try {
            const filePath = await vscode.window.showInputBox({
                prompt: 'Caminho do arquivo para editar',
                placeHolder: 'ex: src/main.js'
            });

            if (!filePath) return;

            const editType = await vscode.window.showQuickPick([
                { label: 'Substituir texto', value: 'str_replace' },
                { label: 'Inserir texto', value: 'insert' }
            ], { placeHolder: 'Tipo de edição' });

            if (!editType) return;

            let edits = [];

            if (editType.value === 'str_replace') {
                const oldStr = await vscode.window.showInputBox({
                    prompt: 'Texto a ser substituído',
                    placeHolder: 'Texto original'
                });

                const newStr = await vscode.window.showInputBox({
                    prompt: 'Novo texto',
                    placeHolder: 'Texto de substituição'
                });

                const startLine = await vscode.window.showInputBox({
                    prompt: 'Linha inicial (número)',
                    placeHolder: '1'
                });

                const endLine = await vscode.window.showInputBox({
                    prompt: 'Linha final (número)',
                    placeHolder: '1'
                });

                if (oldStr && newStr && startLine && endLine) {
                    edits.push({
                        type: 'str_replace',
                        old_str: oldStr,
                        new_str: newStr,
                        old_str_start_line_number: parseInt(startLine),
                        old_str_end_line_number: parseInt(endLine)
                    });
                }
            } else if (editType.value === 'insert') {
                const insertLine = await vscode.window.showInputBox({
                    prompt: 'Linha para inserir (número)',
                    placeHolder: '1'
                });

                const newStr = await vscode.window.showInputBox({
                    prompt: 'Texto a inserir',
                    placeHolder: 'Novo conteúdo'
                });

                if (insertLine && newStr) {
                    edits.push({
                        type: 'insert',
                        insert_line: parseInt(insertLine),
                        new_str: newStr
                    });
                }
            }

            if (edits.length > 0) {
                vscode.window.showInformationMessage('📝 Editando arquivo...');
                const result = await this.codeEditor.editCode(filePath, edits);

                if (result.success) {
                    vscode.window.showInformationMessage(
                        `✅ Arquivo editado: ${result.editsApplied} edição(ões) aplicada(s)`
                    );
                } else {
                    vscode.window.showErrorMessage(`❌ Erro na edição: ${result.error}`);
                }
            }

        } catch (error) {
            console.error('Erro na edição de arquivo:', error);
            vscode.window.showErrorMessage('Erro na edição de arquivo');
        }
    }

    async createFile() {
        try {
            const filePath = await vscode.window.showInputBox({
                prompt: 'Caminho do novo arquivo',
                placeHolder: 'ex: src/newfile.js'
            });

            if (!filePath) return;

            const content = await vscode.window.showInputBox({
                prompt: 'Conteúdo inicial (opcional)',
                placeHolder: 'Deixe vazio para arquivo vazio'
            });

            vscode.window.showInformationMessage('📄 Criando arquivo...');
            const result = await this.codeEditor.createFile(filePath, content || '');

            if (result.success) {
                vscode.window.showInformationMessage(`✅ Arquivo criado: ${filePath}`);
            } else {
                vscode.window.showErrorMessage(`❌ Erro ao criar arquivo: ${result.error}`);
            }

        } catch (error) {
            console.error('Erro ao criar arquivo:', error);
            vscode.window.showErrorMessage('Erro ao criar arquivo');
        }
    }

    async deleteFile() {
        try {
            const filePath = await vscode.window.showInputBox({
                prompt: 'Caminho do arquivo para deletar',
                placeHolder: 'ex: src/oldfile.js'
            });

            if (!filePath) return;

            const confirm = await vscode.window.showWarningMessage(
                `Tem certeza que deseja deletar ${filePath}?`,
                { modal: true },
                'Deletar',
                'Cancelar'
            );

            if (confirm === 'Deletar') {
                vscode.window.showInformationMessage('🗑️ Deletando arquivo...');
                const result = await this.codeEditor.deleteFile(filePath);

                if (result.success) {
                    vscode.window.showInformationMessage(`✅ Arquivo deletado: ${filePath}`);
                } else {
                    vscode.window.showErrorMessage(`❌ Erro ao deletar arquivo: ${result.error}`);
                }
            }

        } catch (error) {
            console.error('Erro ao deletar arquivo:', error);
            vscode.window.showErrorMessage('Erro ao deletar arquivo');
        }
    }

    async copyFile() {
        try {
            const sourcePath = await vscode.window.showInputBox({
                prompt: 'Caminho do arquivo origem',
                placeHolder: 'ex: src/source.js'
            });

            if (!sourcePath) return;

            const destPath = await vscode.window.showInputBox({
                prompt: 'Caminho do arquivo destino',
                placeHolder: 'ex: src/copy.js'
            });

            if (!destPath) return;

            vscode.window.showInformationMessage('📋 Copiando arquivo...');
            const result = await this.codeEditor.copyFile(sourcePath, destPath);

            if (result.success) {
                vscode.window.showInformationMessage(`✅ Arquivo copiado: ${sourcePath} → ${destPath}`);
            } else {
                vscode.window.showErrorMessage(`❌ Erro ao copiar arquivo: ${result.error}`);
            }

        } catch (error) {
            console.error('Erro ao copiar arquivo:', error);
            vscode.window.showErrorMessage('Erro ao copiar arquivo');
        }
    }

    async moveFile() {
        try {
            const sourcePath = await vscode.window.showInputBox({
                prompt: 'Caminho do arquivo origem',
                placeHolder: 'ex: src/source.js'
            });

            if (!sourcePath) return;

            const destPath = await vscode.window.showInputBox({
                prompt: 'Caminho do arquivo destino',
                placeHolder: 'ex: src/moved.js'
            });

            if (!destPath) return;

            vscode.window.showInformationMessage('📦 Movendo arquivo...');
            const result = await this.codeEditor.moveFile(sourcePath, destPath);

            if (result.success) {
                vscode.window.showInformationMessage(`✅ Arquivo movido: ${sourcePath} → ${destPath}`);
            } else {
                vscode.window.showErrorMessage(`❌ Erro ao mover arquivo: ${result.error}`);
            }

        } catch (error) {
            console.error('Erro ao mover arquivo:', error);
            vscode.window.showErrorMessage('Erro ao mover arquivo');
        }
    }

    async readFile() {
        try {
            const filePath = await vscode.window.showInputBox({
                prompt: 'Caminho do arquivo para ler',
                placeHolder: 'ex: src/file.js'
            });

            if (!filePath) return;

            vscode.window.showInformationMessage('📖 Lendo arquivo...');
            const result = await this.codeEditor.readFile(filePath);

            if (result.success) {
                this.showFileContent(result, filePath);
            } else {
                vscode.window.showErrorMessage(`❌ Erro ao ler arquivo: ${result.error}`);
            }

        } catch (error) {
            console.error('Erro ao ler arquivo:', error);
            vscode.window.showErrorMessage('Erro ao ler arquivo');
        }
    }

    async listDirectory() {
        try {
            const dirPath = await vscode.window.showInputBox({
                prompt: 'Caminho do diretório para listar',
                placeHolder: 'ex: src/ (deixe vazio para raiz)'
            });

            const path = dirPath || '.';

            vscode.window.showInformationMessage('📁 Listando diretório...');
            const result = await this.fileManager.listDirectory(path);

            if (result.success) {
                this.showDirectoryListing(result, path);
            } else {
                vscode.window.showErrorMessage(`❌ Erro ao listar diretório: ${result.error}`);
            }

        } catch (error) {
            console.error('Erro ao listar diretório:', error);
            vscode.window.showErrorMessage('Erro ao listar diretório');
        }
    }

    async createDirectory() {
        try {
            const dirPath = await vscode.window.showInputBox({
                prompt: 'Caminho do novo diretório',
                placeHolder: 'ex: src/components'
            });

            if (!dirPath) return;

            vscode.window.showInformationMessage('📁 Criando diretório...');
            const result = await this.fileManager.createDirectory(dirPath);

            if (result.success) {
                vscode.window.showInformationMessage(`✅ Diretório criado: ${dirPath}`);
            } else {
                vscode.window.showErrorMessage(`❌ Erro ao criar diretório: ${result.error}`);
            }

        } catch (error) {
            console.error('Erro ao criar diretório:', error);
            vscode.window.showErrorMessage('Erro ao criar diretório');
        }
    }

    async undoEdit() {
        try {
            vscode.window.showInformationMessage('↶ Desfazendo última edição...');
            const result = await this.codeEditor.undo();

            if (result.success) {
                vscode.window.showInformationMessage(`✅ ${result.message}`);
            } else {
                vscode.window.showInformationMessage(`ℹ️ ${result.message}`);
            }

        } catch (error) {
            console.error('Erro ao desfazer edição:', error);
            vscode.window.showErrorMessage('Erro ao desfazer edição');
        }
    }

    async redoEdit() {
        try {
            vscode.window.showInformationMessage('↷ Refazendo edição...');
            const result = await this.codeEditor.redo();

            if (result.success) {
                vscode.window.showInformationMessage(`✅ ${result.message}`);
            } else {
                vscode.window.showInformationMessage(`ℹ️ ${result.message}`);
            }

        } catch (error) {
            console.error('Erro ao refazer edição:', error);
            vscode.window.showErrorMessage('Erro ao refazer edição');
        }
    }

    // Métodos auxiliares
    showAnalysisResults(analysis) {
        const panel = vscode.window.createWebviewPanel(
            'codeAnalysis',
            'Análise de Código',
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );
        
        panel.webview.html = this.getAnalysisHtml(analysis);
    }

    showWebSearchResults(results) {
        const panel = vscode.window.createWebviewPanel(
            'webSearch',
            'Resultados da Pesquisa',
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );
        
        panel.webview.html = this.getWebSearchHtml(results);
    }

    showCodebaseResults(results) {
        const panel = vscode.window.createWebviewPanel(
            'codebaseSearch',
            'Busca no Codebase',
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );
        
        panel.webview.html = this.getCodebaseHtml(results);
    }

    getAnalysisHtml(analysis) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Análise de Código</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .section { margin-bottom: 20px; }
                .code { background: #f5f5f5; padding: 10px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <h1>📊 Análise de Código</h1>
            <div class="section">
                <h2>Resumo</h2>
                <p>${analysis.summary || 'Análise em andamento...'}</p>
            </div>
            <div class="section">
                <h2>Problemas Encontrados</h2>
                <ul>
                    ${(analysis.issues || []).map(issue => `<li>${issue}</li>`).join('')}
                </ul>
            </div>
            <div class="section">
                <h2>Sugestões</h2>
                <ul>
                    ${(analysis.suggestions || []).map(suggestion => `<li>${suggestion}</li>`).join('')}
                </ul>
            </div>
        </body>
        </html>`;
    }

    getWebSearchHtml(results) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Resultados da Pesquisa</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .result { margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
                .title { font-weight: bold; color: #0066cc; }
                .url { color: #666; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <h1>🔍 Resultados da Pesquisa Web</h1>
            ${results.map(result => `
                <div class="result">
                    <div class="title">${result.title}</div>
                    <div class="url">${result.url}</div>
                    <p>${result.snippet}</p>
                </div>
            `).join('')}
        </body>
        </html>`;
    }

    getCodebaseHtml(results) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Busca no Codebase</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .result { margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
                .file { font-weight: bold; color: #0066cc; }
                .code { background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 5px; }
            </style>
        </head>
        <body>
            <h1>🔍 Resultados do Codebase</h1>
            ${results.map(result => `
                <div class="result">
                    <div class="file">${result.file}</div>
                    <div class="code"><pre>${result.code}</pre></div>
                </div>
            `).join('')}
        </body>
        </html>`;
    }

    generateFileContentHTML(result, filePath) {
        return `<!DOCTYPE html>
        <html>
        <head>
            <title>Conteúdo do Arquivo</title>
            <style>
                body { font-family: 'Courier New', monospace; padding: 20px; }
                .header { background: #f0f0f0; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
                .stats { color: #666; font-size: 14px; margin-top: 10px; }
                .content { background: #f8f8f8; padding: 15px; border-radius: 5px; white-space: pre-wrap; }
                .line-numbers { color: #999; margin-right: 10px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>📄 ${filePath}</h1>
                <div class="stats">
                    Tamanho: ${result.size} bytes | Linhas: ${result.lines}
                </div>
            </div>
            <div class="content">${this.escapeHtml(result.content)}</div>
        </body>
        </html>`;
    }

    generateDirectoryListingHTML(result, dirPath) {
        return `<!DOCTYPE html>
        <html>
        <head>
            <title>Listagem do Diretório</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .header { background: #f0f0f0; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
                .stats { color: #666; font-size: 14px; margin-top: 10px; }
                .item { padding: 8px; border-bottom: 1px solid #eee; display: flex; align-items: center; }
                .item:hover { background: #f5f5f5; }
                .icon { margin-right: 10px; font-size: 16px; }
                .name { font-weight: bold; flex: 1; }
                .size { color: #666; font-size: 12px; margin-left: 10px; }
                .date { color: #999; font-size: 12px; margin-left: 10px; }
                .directory { color: #0066cc; }
                .file { color: #333; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>📁 ${dirPath}</h1>
                <div class="stats">
                    Total: ${result.total} itens | Arquivos: ${result.files.length} | Diretórios: ${result.directories.length}
                </div>
            </div>

            ${result.directories.map(dir => `
                <div class="item">
                    <span class="icon">📁</span>
                    <span class="name directory">${dir.name}/</span>
                    <span class="date">${new Date(dir.modified).toLocaleDateString()}</span>
                </div>
            `).join('')}

            ${result.files.map(file => `
                <div class="item">
                    <span class="icon">📄</span>
                    <span class="name file">${file.name}</span>
                    <span class="size">${this.formatFileSize(file.size)}</span>
                    <span class="date">${new Date(file.modified).toLocaleDateString()}</span>
                </div>
            `).join('')}
        </body>
        </html>`;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showFileContent(result, filePath) {
        const panel = vscode.window.createWebviewPanel(
            'fileContent',
            `Conteúdo: ${filePath}`,
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );

        const html = this.generateFileContentHTML(result, filePath);
        panel.webview.html = html;
    }

    showDirectoryListing(result, dirPath) {
        const panel = vscode.window.createWebviewPanel(
            'directoryListing',
            `Diretório: ${dirPath}`,
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );

        const html = this.generateDirectoryListingHTML(result, dirPath);
        panel.webview.html = html;
    }
}

module.exports = ContinueAgent;
