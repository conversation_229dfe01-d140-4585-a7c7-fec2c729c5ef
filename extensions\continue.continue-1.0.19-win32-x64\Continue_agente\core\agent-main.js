/**
 * Continue_agente - Agente Principal
 * Núcleo do sistema de programação avançada
 */

const vscode = require('vscode');
const path = require('path');
const fs = require('fs');

// Importar componentes
const ContextEngine = require('./context-engine');
const CodeAnalyzer = require('./code-analyzer');
const WebSearchEngine = require('./web-search');
const TaskManager = require('./task-manager');

class ContinueAgent {
    constructor() {
        this.contextEngine = new ContextEngine();
        this.codeAnalyzer = new CodeAnalyzer();
        this.webSearchEngine = new WebSearchEngine();
        this.taskManager = new TaskManager();
        
        this.config = this.loadConfig();
        this.rules = this.loadRules();
        this.isInitialized = false;
    }

    loadConfig() {
        try {
            const configPath = path.join(__dirname, '..', 'config.json');
            const configData = fs.readFileSync(configPath, 'utf8');
            return JSON.parse(configData);
        } catch (error) {
            console.error('Erro ao carregar configuração:', error);
            return this.getDefaultConfig();
        }
    }

    loadRules() {
        try {
            const rulesPath = path.join(__dirname, '..', 'rules.md');
            return fs.readFileSync(rulesPath, 'utf8');
        } catch (error) {
            console.error('Erro ao carregar regras:', error);
            return '';
        }
    }

    getDefaultConfig() {
        return {
            models: [{
                title: "Qwen3-30B-Python-Coder",
                provider: "lmstudio",
                model: "Qwen3-30B-A3B-python-coder.i1-Q4_K_S",
                apiBase: "http://localhost:1234/v1",
                apiKey: "lm-studio",
                contextLength: 32768,
                maxTokens: 4096,
                temperature: 0.1
            }]
        };
    }

    async initialize() {
        if (this.isInitialized) return;

        console.log('🚀 Inicializando Continue_agente...');
        
        try {
            // Verificar conexão com LM Studio
            await this.verifyLMStudioConnection();
            
            // Inicializar componentes
            await this.contextEngine.initialize();
            await this.codeAnalyzer.initialize();
            await this.webSearchEngine.initialize();
            await this.taskManager.initialize();
            
            // Registrar comandos
            this.registerCommands();
            
            // Aplicar regras
            this.applyProgrammingRules();
            
            this.isInitialized = true;
            console.log('✅ Continue_agente inicializado com sucesso!');
            
        } catch (error) {
            console.error('❌ Erro na inicialização:', error);
            vscode.window.showErrorMessage('Erro ao inicializar Continue_agente');
        }
    }

    async verifyLMStudioConnection() {
        try {
            const response = await fetch(`${this.config.models[0].apiBase}/models`, {
                headers: {
                    'Authorization': `Bearer ${this.config.models[0].apiKey}`
                }
            });
            
            if (response.ok) {
                console.log('✅ Conexão com LM Studio estabelecida');
                return true;
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('❌ Erro na conexão com LM Studio:', error);
            vscode.window.showErrorMessage(
                'Não foi possível conectar ao LM Studio. Verifique se está rodando na porta 1234.'
            );
            return false;
        }
    }

    registerCommands() {
        const commands = [
            // Comandos básicos
            { command: 'continue-agent.analyzeCode', callback: this.analyzeCode.bind(this) },
            { command: 'continue-agent.refactorCode', callback: this.refactorCode.bind(this) },
            { command: 'continue-agent.generateTests', callback: this.generateTests.bind(this) },
            { command: 'continue-agent.debugCode', callback: this.debugCode.bind(this) },
            { command: 'continue-agent.optimizeCode', callback: this.optimizeCode.bind(this) },
            
            // Comandos avançados
            { command: 'continue-agent.searchWeb', callback: this.searchWeb.bind(this) },
            { command: 'continue-agent.fetchWebContent', callback: this.fetchWebContent.bind(this) },
            { command: 'continue-agent.codebaseRetrieval', callback: this.codebaseRetrieval.bind(this) },
            { command: 'continue-agent.createDiagram', callback: this.createDiagram.bind(this) },
            { command: 'continue-agent.manageTask', callback: this.manageTask.bind(this) }
        ];

        commands.forEach(cmd => {
            vscode.commands.registerCommand(cmd.command, cmd.callback);
        });
    }

    applyProgrammingRules() {
        this.programmingRules = {
            preliminaryAnalysis: true,
            contextRetrieval: true,
            taskManagement: true,
            detailedPlanning: true,
            useStrReplaceEditor: true,
            codebaseRetrievalBeforeEdit: true,
            conservativeEditing: true,
            usePackageManagers: true,
            followUserInstructions: true,
            suggestTesting: true,
            useAugmentCodeSnippet: true,
            askForHelpWhenStuck: true
        };
    }

    // Comandos básicos
    async analyzeCode() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('Nenhum arquivo aberto para análise');
            return;
        }

        try {
            vscode.window.showInformationMessage('🔍 Analisando código...');
            
            const document = editor.document;
            const selection = editor.selection;
            const code = selection.isEmpty ? document.getText() : document.getText(selection);
            
            // Seguir regras: análise preliminar e recuperação de contexto
            const context = await this.contextEngine.getRelevantContext(document.uri, code);
            const analysis = await this.codeAnalyzer.analyzeCode(code, context);
            
            this.showAnalysisResults(analysis);
            
        } catch (error) {
            console.error('Erro na análise:', error);
            vscode.window.showErrorMessage('Erro durante a análise do código');
        }
    }

    async searchWeb(query) {
        try {
            vscode.window.showInformationMessage(`🔍 Pesquisando: ${query}`);
            const results = await this.webSearchEngine.search(query, 5);
            this.showWebSearchResults(results);
        } catch (error) {
            console.error('Erro na pesquisa web:', error);
            vscode.window.showErrorMessage('Erro durante a pesquisa web');
        }
    }

    async codebaseRetrieval(query) {
        try {
            vscode.window.showInformationMessage(`🔍 Buscando no codebase: ${query}`);
            const results = await this.contextEngine.retrieveFromCodebase(query);
            this.showCodebaseResults(results);
        } catch (error) {
            console.error('Erro na busca do codebase:', error);
            vscode.window.showErrorMessage('Erro na busca do codebase');
        }
    }

    // Métodos auxiliares
    showAnalysisResults(analysis) {
        const panel = vscode.window.createWebviewPanel(
            'codeAnalysis',
            'Análise de Código',
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );
        
        panel.webview.html = this.getAnalysisHtml(analysis);
    }

    showWebSearchResults(results) {
        const panel = vscode.window.createWebviewPanel(
            'webSearch',
            'Resultados da Pesquisa',
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );
        
        panel.webview.html = this.getWebSearchHtml(results);
    }

    showCodebaseResults(results) {
        const panel = vscode.window.createWebviewPanel(
            'codebaseSearch',
            'Busca no Codebase',
            vscode.ViewColumn.Two,
            { enableScripts: true }
        );
        
        panel.webview.html = this.getCodebaseHtml(results);
    }

    getAnalysisHtml(analysis) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Análise de Código</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .section { margin-bottom: 20px; }
                .code { background: #f5f5f5; padding: 10px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <h1>📊 Análise de Código</h1>
            <div class="section">
                <h2>Resumo</h2>
                <p>${analysis.summary || 'Análise em andamento...'}</p>
            </div>
            <div class="section">
                <h2>Problemas Encontrados</h2>
                <ul>
                    ${(analysis.issues || []).map(issue => `<li>${issue}</li>`).join('')}
                </ul>
            </div>
            <div class="section">
                <h2>Sugestões</h2>
                <ul>
                    ${(analysis.suggestions || []).map(suggestion => `<li>${suggestion}</li>`).join('')}
                </ul>
            </div>
        </body>
        </html>`;
    }

    getWebSearchHtml(results) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Resultados da Pesquisa</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .result { margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
                .title { font-weight: bold; color: #0066cc; }
                .url { color: #666; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <h1>🔍 Resultados da Pesquisa Web</h1>
            ${results.map(result => `
                <div class="result">
                    <div class="title">${result.title}</div>
                    <div class="url">${result.url}</div>
                    <p>${result.snippet}</p>
                </div>
            `).join('')}
        </body>
        </html>`;
    }

    getCodebaseHtml(results) {
        return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Busca no Codebase</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .result { margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
                .file { font-weight: bold; color: #0066cc; }
                .code { background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 5px; }
            </style>
        </head>
        <body>
            <h1>🔍 Resultados do Codebase</h1>
            ${results.map(result => `
                <div class="result">
                    <div class="file">${result.file}</div>
                    <div class="code"><pre>${result.code}</pre></div>
                </div>
            `).join('')}
        </body>
        </html>`;
    }
}

module.exports = ContinueAgent;
