var So=Object.defineProperty;var ko=(r,t,e)=>t in r?So(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var it=(r,t,e)=>ko(r,typeof t!="symbol"?t+"":t,e);import{S as ot,i as at,s as rt,n as D,d as v,c as y,e as b,f as xt,h as w,A as kt,ao as is,ap as Fn,D as x,P as xn,t as $,q as m,o as q,p as B,E as C,R as It,aa as he,F as k,V as T,G as _,Y as I,X as ct,$ as gt,W as Y,ax as ls,N as ft,a6 as nn,J as zt,a as Mt,K as Ot,L as Zt,M as Ft,_ as cs,g as ve,a0 as ds,I as Mo,j as se,U as Ao,ay as Te,al as Et,a7 as me,ag as To,ae as us,b as le,u as Cn,v as _n,w as bn,x as Sn,H as kn,an as Hn,z as Oe,y as Pe,a3 as Ct,a4 as _t,a5 as bt,aA as No,O as Gn,ah as Wr,aw as ps,af as Kr}from"./SpinnerAugment-VfHtkDdv.js";import{G as Eo,D as cn,P as sn,C as Io,B as Jn,T as Wn,a as Yr,S as jn}from"./github-CghUBfjq.js";import"./design-system-init-BQpWKoxZ.js";import{W as ht,a as jt,e as lt,u as Ht,o as Gt,I as Le,h as St,i as Ro,b as Po,H as Rn}from"./IconButtonAugment-BlRCK7lJ.js";import{M as Xr,R as Lo}from"./message-broker-DxXjuHCW.js";import{G as zo,S as Oo,a as Zo,N as Fo,L as jo,b as At,M as Se,D as Do,F as Uo,f as Qr,R as Vo,c as qo,T as to,d as Bo,C as Ho,e as Go,g as Jo,h as Wo,i as Ko,A as Yo,j as Xo,U as Qo}from"./partner-mcp-utils-DbWhXw15.js";import{Q as Pn,a1 as Yt,O as $t,a2 as Ln,r as Dn,T as ye,D as wt,a3 as ta,C as eo,E as no,a4 as dn,h as zn,A as ms,f as ea,g as na}from"./index-C5qylk65.js";import{o as Ne}from"./keypress-DD1aQVr0.js";import{V as so}from"./VSCodeCodicon-C64qkVNR.js";import{A as sa}from"./async-messaging-Cm1y2LK7.js";import{D as ra}from"./Drawer-DRLV4is-.js";import{B as vt}from"./ButtonAugment-CRJIYorH.js";import{T as ee,a as Kn}from"./CardAugment-CMpdst0l.js";import{C as Mn}from"./CalloutAugment-jvmj3vIU.js";import{E as oa}from"./ellipsis-CQoYNkeK.js";import{P as aa}from"./pen-to-square-DiN4Ry3-.js";import{T as ro,S as ia}from"./TextAreaAugment-BnS2cUNC.js";import{C as la}from"./copy-MzH1hy8q.js";import{C as oo,a as ca,T as Yn}from"./CollapseButtonAugment-BcgZeyRI.js";import{C as Xn}from"./chevron-down-DQi0HUpw.js";import{M as da}from"./index-BsnNYDaF.js";import{M as ua,R as pa}from"./rules-model-BLO-SAZS.js";import{R as ma}from"./RulesModeSelector-Dv1BmVk6.js";import{M as ao}from"./ModalAugment-CvTiczpm.js";import"./types-CGlLNakm.js";import"./file-paths-CXmnYUii.js";import"./BaseTextInput-C9A3t790.js";import"./index-PzkfeRvH.js";import"./index-6WVCg-U8.js";import"./lodash-Cs_Exhqr.js";const Ze={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class fa{constructor(t,e=Ze){it(this,"timerId",null);it(this,"currentMS");it(this,"step",0);it(this,"params");this.callback=t;const n={...e};n.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),n.maxMS=Ze.maxMS),n.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),n.initialMS=Ze.initialMS),n.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),n.mult=Ze.mult),n.maxSteps!==void 0&&n.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),n.maxSteps=Ze.maxSteps),this.params=n,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const t=this.callback();t instanceof Promise&&t.catch(e=>console.error("Error in polling callback:",e))}catch(t){console.error("Error in polling callback:",t)}}}function $a(r){let t,e,n;return{c(){t=xt("svg"),e=xt("path"),n=xt("path"),w(e,"d","M2.43703 10.7785C2.30998 10.978 2.16478 11.2137 2.05588 11.3951C1.94698 11.5764 2.00143 11.8121 2.18293 11.921L4.66948 13.4442C4.85098 13.553 5.08695 13.4986 5.19585 13.3173C5.2866 13.1541 5.41365 12.9365 5.55885 12.7007C6.53895 11.0868 7.5372 11.2681 9.3159 12.1204L11.7843 13.281C11.9839 13.3717 12.2017 13.281 12.2925 13.0997L13.4722 10.4339C13.563 10.2526 13.4722 10.0169 13.2907 9.92619C12.7644 9.69044 11.7298 9.20084 10.8223 8.74749C7.44645 7.13354 4.59689 7.24234 2.43703 10.7785Z"),w(e,"fill","currentColor"),w(n,"d","M13.563 4.72157C13.69 4.52209 13.8352 4.28635 13.9441 4.105C14.053 3.92366 13.9985 3.68791 13.817 3.57911L11.3305 2.05583C11.149 1.94702 10.913 2.00143 10.8041 2.18277C10.7134 2.34598 10.5863 2.56359 10.4411 2.79934C9.461 4.41329 8.46275 4.23194 6.68405 3.37963L4.21563 2.21904C4.01598 2.12837 3.79818 2.21904 3.70743 2.40038L2.52767 5.0661C2.43692 5.24745 2.52767 5.4832 2.70917 5.5739C3.23552 5.80965 4.27007 6.29925 5.1776 6.7526C8.53535 8.34845 11.3849 8.25775 13.563 4.72157Z"),w(n,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 15 15"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,o){y(s,t,o),b(t,e),b(t,n)},p:D,i:D,o:D,d(s){s&&v(t)}}}class ga extends ot{constructor(t){super(),at(this,t,null,$a,rt,{})}}function ha(r){let t,e;return{c(){t=xt("svg"),e=xt("path"),w(e,"fill-rule","evenodd"),w(e,"clip-rule","evenodd"),w(e,"d","M13.5028 2H7.7257C7.7257 3.44 8.8914 4.60571 10.3314 4.60571H11.3942V5.6343C11.3942 7.0743 12.5599 8.24 14 8.24V2.49714C14 2.22285 13.7771 2 13.5028 2ZM10.6399 4.88H4.86279C4.86279 6.32 6.0285 7.4857 7.4685 7.4857H8.53135V8.5143C8.53135 9.9543 9.69705 11.12 11.137 11.12V5.37715C11.137 5.10285 10.9142 4.88 10.6399 4.88ZM2 7.75995H7.7771C8.0514 7.75995 8.27425 7.9828 8.27425 8.2571V13.9999C6.83425 13.9999 5.66855 12.8342 5.66855 11.3942V10.3656H4.6057C3.16571 10.3656 2 9.19995 2 7.75995Z"),w(e,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 15 15"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,t,s),b(t,e)},p:D,i:D,o:D,d(n){n&&v(t)}}}class va extends ot{constructor(t){super(),at(this,t,null,ha,rt,{})}}function ya(r){let t,e;return{c(){t=xt("svg"),e=xt("path"),w(e,"d","M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z"),w(e,"fill","currentColor"),w(e,"fill-rule","evenodd"),w(e,"clip-rule","evenodd"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 15 15"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,t,s),b(t,e)},p:D,i:D,o:D,d(n){n&&v(t)}}}class oe extends ot{constructor(t){super(),at(this,t,null,ya,rt,{})}}function wa(r){let t,e;return{c(){t=xt("svg"),e=xt("path"),w(e,"fill-rule","evenodd"),w(e,"clip-rule","evenodd"),w(e,"d","M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z"),w(e,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 15 15"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,t,s),b(t,e)},p:D,i:D,o:D,d(n){n&&v(t)}}}class xa extends ot{constructor(t){super(),at(this,t,null,wa,rt,{})}}class Ue{constructor(t){it(this,"configs",kt([]));it(this,"pollingManager");it(this,"_enableDebugFeatures",kt(!1));it(this,"_settingsComponentSupported",kt({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));it(this,"_enableAgentMode",kt(!1));it(this,"_enableAgentSwarmMode",kt(!1));it(this,"_enableNativeRemoteMcp",kt(!0));it(this,"_hasEverUsedRemoteAgent",kt(!1));it(this,"_enableInitialOrientation",kt(!1));it(this,"_userTier",kt("unknown"));it(this,"_guidelines",kt({}));this._host=t,this.pollingManager=new fa(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(t){const e=!t.isConfigured,n=t.oauthUrl;if(t.identifier.hostName===Pn.remoteToolHost){let s=t.identifier.toolId;switch(typeof s=="string"&&/^\d+$/.test(s)&&(s=Number(s)),s){case Yt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:Eo,requiresAuthentication:e,authUrl:n};case Yt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:jo,requiresAuthentication:e,authUrl:n};case Yt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:va,requiresAuthentication:e,authUrl:n};case Yt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:Fo,requiresAuthentication:e,authUrl:n};case Yt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:ga,requiresAuthentication:e,authUrl:n};case Yt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:Zo,requiresAuthentication:e,authUrl:n};case Yt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:Oo,requiresAuthentication:e,authUrl:n};case Yt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:zo,requiresAuthentication:e,authUrl:n};case Yt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:e,authUrl:n};default:throw new Error(`Unhandled RemoteToolId: ${s}`)}}else if(t.identifier.hostName===Pn.localToolHost){const s=t.identifier.toolId;switch(s){case $t.readFile:case $t.editFile:case $t.saveFile:case $t.launchProcess:case $t.killProcess:case $t.readProcess:case $t.writeProcess:case $t.listProcesses:case $t.waitProcess:case $t.openBrowser:case $t.clarify:case $t.onboardingSubAgent:case $t.strReplaceEditor:case $t.remember:case $t.diagnostics:case $t.setupScript:case $t.readTerminal:case $t.gitCommitRetrieval:case $t.memoryRetrieval:case $t.startWorkerAgent:case $t.readWorkerState:case $t.waitForWorkerAgent:case $t.sendInstructionToWorkerAgent:case $t.stopWorkerAgent:case $t.deleteWorkerAgent:case $t.readWorkerAgentEdits:case $t.applyWorkerAgentEdits:case $t.LocalSubAgent:return{displayName:t.definition.name.toString(),description:"Local tool",icon:oe,requiresAuthentication:e,authUrl:n};default:throw new Error(`Unhandled LocalToolType: ${s}`)}}else if(t.identifier.hostName===Pn.sidecarToolHost){const s=t.identifier.toolId;switch(s){case At.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:Se,requiresAuthentication:e,authUrl:n};case At.shell:return{displayName:"Shell",description:"Shell",icon:Se,requiresAuthentication:e,authUrl:n};case At.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:Se,requiresAuthentication:e,authUrl:n};case At.view:return{displayName:"File View",description:"File Viewer",icon:Se,requiresAuthentication:e,authUrl:n};case At.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:Se,requiresAuthentication:e,authUrl:n};case At.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:xa,requiresAuthentication:e,authUrl:n};case At.remember:return{displayName:t.definition.name.toString(),description:"Remember",icon:oe,requiresAuthentication:e,authUrl:n};case At.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Uo,requiresAuthentication:e,authUrl:n};case At.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:oe,requiresAuthentication:e,authUrl:n};case At.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:oe,requiresAuthentication:e,authUrl:n};case At.viewRangeUntruncated:return{displayName:t.definition.name.toString(),description:"View Range",icon:oe,requiresAuthentication:e,authUrl:n};case At.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:oe,requiresAuthentication:e,authUrl:n};case At.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:oe,requiresAuthentication:e,authUrl:n};case At.searchUntruncated:return{displayName:t.definition.name.toString(),description:"Search Untruncated",icon:oe,requiresAuthentication:e,authUrl:n};case At.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Do,requiresAuthentication:e,authUrl:n};case At.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:Se,requiresAuthentication:e,authUrl:n};default:throw new Error(`Unhandled SidecarToolType: ${s}`)}}return{displayName:t.definition.name.toString(),description:t.definition.description||"",requiresAuthentication:e,authUrl:n}}handleMessageFromExtension(t){const e=t.data;switch(e.type){case ht.toolConfigInitialize:return this.createConfigsFromHostTools(e.data.hostTools,e.data.toolConfigs),e.data&&e.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(e.data.enableDebugFeatures),e.data&&e.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(e.data.settingsComponentSupported),e.data.enableAgentMode!==void 0&&this._enableAgentMode.set(e.data.enableAgentMode),e.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(e.data.enableAgentSwarmMode),e.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(e.data.hasEverUsedRemoteAgent),e.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(e.data.enableInitialOrientation),e.data.userTier!==void 0&&this._userTier.set(e.data.userTier),e.data.guidelines!==void 0&&this._guidelines.set(e.data.guidelines),e.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(e.data.enableNativeRemoteMcp),!0;case ht.toolConfigDefinitionsResponse:return this.configs.update(n=>this.createConfigsFromHostTools(e.data.hostTools,[]).map(s=>{const o=n.find(a=>a.name===s.name);return o?{...o,displayName:s.displayName,description:s.description,icon:s.icon,requiresAuthentication:s.requiresAuthentication,authUrl:s.authUrl,isConfigured:s.isConfigured,toolApprovalConfig:s.toolApprovalConfig}:s})),!0}return!1}createConfigsFromHostTools(t,e){return t.map(n=>{const s=this.transformToolDisplay(n),o=e.find(l=>l.name===n.definition.name),a=(o==null?void 0:o.isConfigured)??!s.requiresAuthentication;return{config:(o==null?void 0:o.config)??{},configString:JSON.stringify((o==null?void 0:o.config)??{},null,2),isConfigured:a,name:n.definition.name.toString(),displayName:s.displayName,description:s.description,identifier:n.identifier,icon:s.icon,requiresAuthentication:s.requiresAuthentication,authUrl:s.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:n.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(t){return["github","linear","notion","jira","confluence","supabase"].includes(t.displayName.toLowerCase())}getDisplayableTools(){return is(this.configs,t=>{const e=t.filter(s=>this.isDisplayableTool(s)),n=new Map;for(const s of e)n.set(s.displayName,s);return Array.from(n.values()).sort((s,o)=>{const a={GitHub:1,Linear:2,Notion:3},l=Number.MAX_SAFE_INTEGER,i=a[s.displayName]||l,c=a[o.displayName]||l;return i<l&&c<l||i===l&&c===l?i!==c?i-c:s.displayName.localeCompare(o.displayName):i-c})})}getPretendNativeToolDefs(){return is(this.configs,t=>this.getEnableNativeRemoteMcp()?Qr(t):[])}saveConfig(t){this.startPolling()}notifyLoaded(){this._host.postMessage({type:ht.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(t=!0){this._host.postMessage({type:ht.toolConfigGetDefinitions,data:{useCache:t}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"&&this._enableNativeRemoteMcp}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(t){this._guidelines.update(e=>e.userGuidelines?{...e,userGuidelines:{...e.userGuidelines,contents:t,enabled:t.length>0}}:e)}updateToolApprovalConfig(t,e){this.configs.update(n=>n.map(s=>s.identifier.toolId===t.toolId&&s.identifier.hostName===t.hostName?{...s,toolApprovalConfig:e}:s))}getSettingsComponentSupported(){return this._settingsComponentSupported}}it(Ue,"key","toolConfigModel");var ut,Un;(function(r){r.assertEqual=t=>t,r.assertIs=function(t){},r.assertNever=function(t){throw new Error},r.arrayToEnum=t=>{const e={};for(const n of t)e[n]=n;return e},r.getValidEnumValues=t=>{const e=r.objectKeys(t).filter(s=>typeof t[t[s]]!="number"),n={};for(const s of e)n[s]=t[s];return r.objectValues(n)},r.objectValues=t=>r.objectKeys(t).map(function(e){return t[e]}),r.objectKeys=typeof Object.keys=="function"?t=>Object.keys(t):t=>{const e=[];for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.push(n);return e},r.find=(t,e)=>{for(const n of t)if(e(n))return n},r.isInteger=typeof Number.isInteger=="function"?t=>Number.isInteger(t):t=>typeof t=="number"&&isFinite(t)&&Math.floor(t)===t,r.joinValues=function(t,e=" | "){return t.map(n=>typeof n=="string"?`'${n}'`:n).join(e)},r.jsonStringifyReplacer=(t,e)=>typeof e=="bigint"?e.toString():e})(ut||(ut={})),function(r){r.mergeShapes=(t,e)=>({...t,...e})}(Un||(Un={}));const H=ut.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Xt=r=>{switch(typeof r){case"undefined":return H.undefined;case"string":return H.string;case"number":return isNaN(r)?H.nan:H.number;case"boolean":return H.boolean;case"function":return H.function;case"bigint":return H.bigint;case"symbol":return H.symbol;case"object":return Array.isArray(r)?H.array:r===null?H.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?H.promise:typeof Map<"u"&&r instanceof Map?H.map:typeof Set<"u"&&r instanceof Set?H.set:typeof Date<"u"&&r instanceof Date?H.date:H.object;default:return H.unknown}},L=ut.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class Pt extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const e=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,e):this.__proto__=e,this.name="ZodError",this.issues=t}format(t){const e=t||function(o){return o.message},n={_errors:[]},s=o=>{for(const a of o.issues)if(a.code==="invalid_union")a.unionErrors.map(s);else if(a.code==="invalid_return_type")s(a.returnTypeError);else if(a.code==="invalid_arguments")s(a.argumentsError);else if(a.path.length===0)n._errors.push(e(a));else{let l=n,i=0;for(;i<a.path.length;){const c=a.path[i];i===a.path.length-1?(l[c]=l[c]||{_errors:[]},l[c]._errors.push(e(a))):l[c]=l[c]||{_errors:[]},l=l[c],i++}}};return s(this),n}static assert(t){if(!(t instanceof Pt))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ut.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=e=>e.message){const e={},n=[];for(const s of this.issues)s.path.length>0?(e[s.path[0]]=e[s.path[0]]||[],e[s.path[0]].push(t(s))):n.push(t(s));return{formErrors:n,fieldErrors:e}}get formErrors(){return this.flatten()}}Pt.create=r=>new Pt(r);const Ee=(r,t)=>{let e;switch(r.code){case L.invalid_type:e=r.received===H.undefined?"Required":`Expected ${r.expected}, received ${r.received}`;break;case L.invalid_literal:e=`Invalid literal value, expected ${JSON.stringify(r.expected,ut.jsonStringifyReplacer)}`;break;case L.unrecognized_keys:e=`Unrecognized key(s) in object: ${ut.joinValues(r.keys,", ")}`;break;case L.invalid_union:e="Invalid input";break;case L.invalid_union_discriminator:e=`Invalid discriminator value. Expected ${ut.joinValues(r.options)}`;break;case L.invalid_enum_value:e=`Invalid enum value. Expected ${ut.joinValues(r.options)}, received '${r.received}'`;break;case L.invalid_arguments:e="Invalid function arguments";break;case L.invalid_return_type:e="Invalid function return type";break;case L.invalid_date:e="Invalid date";break;case L.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(e=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(e=`${e} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?e=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?e=`Invalid input: must end with "${r.validation.endsWith}"`:ut.assertNever(r.validation):e=r.validation!=="regex"?`Invalid ${r.validation}`:"Invalid";break;case L.too_small:e=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:"Invalid input";break;case L.too_big:e=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:"Invalid input";break;case L.custom:e="Invalid input";break;case L.invalid_intersection_types:e="Intersection results could not be merged";break;case L.not_multiple_of:e=`Number must be a multiple of ${r.multipleOf}`;break;case L.not_finite:e="Number must be finite";break;default:e=t.defaultError,ut.assertNever(r)}return{message:e}};let io=Ee;function pn(){return io}const mn=r=>{const{data:t,path:e,errorMaps:n,issueData:s}=r,o=[...e,...s.path||[]],a={...s,path:o};if(s.message!==void 0)return{...s,path:o,message:s.message};let l="";const i=n.filter(c=>!!c).slice().reverse();for(const c of i)l=c(a,{data:t,defaultError:l}).message;return{...s,path:o,message:l}};function U(r,t){const e=pn(),n=mn({issueData:t,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,e,e===Ee?void 0:Ee].filter(s=>!!s)});r.common.issues.push(n)}class Nt{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,e){const n=[];for(const s of e){if(s.status==="aborted")return Q;s.status==="dirty"&&t.dirty(),n.push(s.value)}return{status:t.value,value:n}}static async mergeObjectAsync(t,e){const n=[];for(const s of e){const o=await s.key,a=await s.value;n.push({key:o,value:a})}return Nt.mergeObjectSync(t,n)}static mergeObjectSync(t,e){const n={};for(const s of e){const{key:o,value:a}=s;if(o.status==="aborted"||a.status==="aborted")return Q;o.status==="dirty"&&t.dirty(),a.status==="dirty"&&t.dirty(),o.value==="__proto__"||a.value===void 0&&!s.alwaysSet||(n[o.value]=a.value)}return{status:t.value,value:n}}}const Q=Object.freeze({status:"aborted"}),fn=r=>({status:"dirty",value:r}),Rt=r=>({status:"valid",value:r}),Vn=r=>r.status==="aborted",qn=r=>r.status==="dirty",we=r=>r.status==="valid",Ve=r=>typeof Promise<"u"&&r instanceof Promise;function $n(r,t,e,n){if(typeof t=="function"||!t.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t.get(r)}function lo(r,t,e,n,s){if(typeof t=="function"||!t.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(r,e),e}var W,Fe,je;typeof SuppressedError=="function"&&SuppressedError,function(r){r.errToObj=t=>typeof t=="string"?{message:t}:t||{},r.toString=t=>typeof t=="string"?t:t==null?void 0:t.message}(W||(W={}));class Jt{constructor(t,e,n,s){this._cachedPath=[],this.parent=t,this.data=e,this._path=n,this._key=s}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const fs=(r,t)=>{if(we(t))return{success:!0,data:t.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const e=new Pt(r.common.issues);return this._error=e,this._error}}};function et(r){if(!r)return{};const{errorMap:t,invalid_type_error:e,required_error:n,description:s}=r;if(t&&(e||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:s}:{errorMap:(o,a)=>{var l,i;const{message:c}=r;return o.code==="invalid_enum_value"?{message:c??a.defaultError}:a.data===void 0?{message:(l=c??n)!==null&&l!==void 0?l:a.defaultError}:o.code!=="invalid_type"?{message:a.defaultError}:{message:(i=c??e)!==null&&i!==void 0?i:a.defaultError}},description:s}}class nt{get description(){return this._def.description}_getType(t){return Xt(t.data)}_getOrReturnCtx(t,e){return e||{common:t.parent.common,data:t.data,parsedType:Xt(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new Nt,ctx:{common:t.parent.common,data:t.data,parsedType:Xt(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const e=this._parse(t);if(Ve(e))throw new Error("Synchronous parse encountered promise.");return e}_parseAsync(t){const e=this._parse(t);return Promise.resolve(e)}parse(t,e){const n=this.safeParse(t,e);if(n.success)return n.data;throw n.error}safeParse(t,e){var n;const s={common:{issues:[],async:(n=e==null?void 0:e.async)!==null&&n!==void 0&&n,contextualErrorMap:e==null?void 0:e.errorMap},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xt(t)},o=this._parseSync({data:t,path:s.path,parent:s});return fs(s,o)}"~validate"(t){var e,n;const s={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xt(t)};if(!this["~standard"].async)try{const o=this._parseSync({data:t,path:[],parent:s});return we(o)?{value:o.value}:{issues:s.common.issues}}catch(o){!((n=(e=o==null?void 0:o.message)===null||e===void 0?void 0:e.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),s.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:s}).then(o=>we(o)?{value:o.value}:{issues:s.common.issues})}async parseAsync(t,e){const n=await this.safeParseAsync(t,e);if(n.success)return n.data;throw n.error}async safeParseAsync(t,e){const n={common:{issues:[],contextualErrorMap:e==null?void 0:e.errorMap,async:!0},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xt(t)},s=this._parse({data:t,path:n.path,parent:n}),o=await(Ve(s)?s:Promise.resolve(s));return fs(n,o)}refine(t,e){const n=s=>typeof e=="string"||e===void 0?{message:e}:typeof e=="function"?e(s):e;return this._refinement((s,o)=>{const a=t(s),l=()=>o.addIssue({code:L.custom,...n(s)});return typeof Promise<"u"&&a instanceof Promise?a.then(i=>!!i||(l(),!1)):!!a||(l(),!1)})}refinement(t,e){return this._refinement((n,s)=>!!t(n)||(s.addIssue(typeof e=="function"?e(n,s):e),!1))}_refinement(t){return new Vt({schema:this,typeName:X.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return Bt.create(this,this._def)}nullable(){return pe.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return qt.create(this)}promise(){return Re.create(this,this._def)}or(t){return Ge.create([this,t],this._def)}and(t){return Je.create(this,t,this._def)}transform(t){return new Vt({...et(this._def),schema:this,typeName:X.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const e=typeof t=="function"?t:()=>t;return new Xe({...et(this._def),innerType:this,defaultValue:e,typeName:X.ZodDefault})}brand(){return new Qn({typeName:X.ZodBranded,type:this,...et(this._def)})}catch(t){const e=typeof t=="function"?t:()=>t;return new Qe({...et(this._def),innerType:this,catchValue:e,typeName:X.ZodCatch})}describe(t){return new this.constructor({...this._def,description:t})}pipe(t){return rn.create(this,t)}readonly(){return tn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Ca=/^c[^\s-]{8,}$/i,_a=/^[0-9a-z]+$/,ba=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Sa=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,ka=/^[a-z0-9_-]{21}$/i,Ma=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Aa=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Ta=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let On;const Na=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ea=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Ia=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Ra=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Pa=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,La=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,co="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",za=new RegExp(`^${co}$`);function uo(r){let t="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?t=`${t}\\.\\d{${r.precision}}`:r.precision==null&&(t=`${t}(\\.\\d+)?`),t}function po(r){let t=`${co}T${uo(r)}`;const e=[];return e.push(r.local?"Z?":"Z"),r.offset&&e.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${e.join("|")})`,new RegExp(`^${t}$`)}function Oa(r,t){if(!Ma.test(r))return!1;try{const[e]=r.split("."),n=e.replace(/-/g,"+").replace(/_/g,"/").padEnd(e.length+(4-e.length%4)%4,"="),s=JSON.parse(atob(n));return typeof s=="object"&&s!==null&&!(!s.typ||!s.alg)&&(!t||s.alg===t)}catch{return!1}}function Za(r,t){return!(t!=="v4"&&t||!Ea.test(r))||!(t!=="v6"&&t||!Ra.test(r))}class Ut extends nt{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==H.string){const a=this._getOrReturnCtx(t);return U(a,{code:L.invalid_type,expected:H.string,received:a.parsedType}),Q}const e=new Nt;let n;for(const a of this._def.checks)if(a.kind==="min")t.data.length<a.value&&(n=this._getOrReturnCtx(t,n),U(n,{code:L.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),e.dirty());else if(a.kind==="max")t.data.length>a.value&&(n=this._getOrReturnCtx(t,n),U(n,{code:L.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),e.dirty());else if(a.kind==="length"){const l=t.data.length>a.value,i=t.data.length<a.value;(l||i)&&(n=this._getOrReturnCtx(t,n),l?U(n,{code:L.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):i&&U(n,{code:L.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),e.dirty())}else if(a.kind==="email")Ta.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"email",code:L.invalid_string,message:a.message}),e.dirty());else if(a.kind==="emoji")On||(On=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),On.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"emoji",code:L.invalid_string,message:a.message}),e.dirty());else if(a.kind==="uuid")Sa.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"uuid",code:L.invalid_string,message:a.message}),e.dirty());else if(a.kind==="nanoid")ka.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"nanoid",code:L.invalid_string,message:a.message}),e.dirty());else if(a.kind==="cuid")Ca.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"cuid",code:L.invalid_string,message:a.message}),e.dirty());else if(a.kind==="cuid2")_a.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"cuid2",code:L.invalid_string,message:a.message}),e.dirty());else if(a.kind==="ulid")ba.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"ulid",code:L.invalid_string,message:a.message}),e.dirty());else if(a.kind==="url")try{new URL(t.data)}catch{n=this._getOrReturnCtx(t,n),U(n,{validation:"url",code:L.invalid_string,message:a.message}),e.dirty()}else a.kind==="regex"?(a.regex.lastIndex=0,a.regex.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"regex",code:L.invalid_string,message:a.message}),e.dirty())):a.kind==="trim"?t.data=t.data.trim():a.kind==="includes"?t.data.includes(a.value,a.position)||(n=this._getOrReturnCtx(t,n),U(n,{code:L.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),e.dirty()):a.kind==="toLowerCase"?t.data=t.data.toLowerCase():a.kind==="toUpperCase"?t.data=t.data.toUpperCase():a.kind==="startsWith"?t.data.startsWith(a.value)||(n=this._getOrReturnCtx(t,n),U(n,{code:L.invalid_string,validation:{startsWith:a.value},message:a.message}),e.dirty()):a.kind==="endsWith"?t.data.endsWith(a.value)||(n=this._getOrReturnCtx(t,n),U(n,{code:L.invalid_string,validation:{endsWith:a.value},message:a.message}),e.dirty()):a.kind==="datetime"?po(a).test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{code:L.invalid_string,validation:"datetime",message:a.message}),e.dirty()):a.kind==="date"?za.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{code:L.invalid_string,validation:"date",message:a.message}),e.dirty()):a.kind==="time"?new RegExp(`^${uo(a)}$`).test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{code:L.invalid_string,validation:"time",message:a.message}),e.dirty()):a.kind==="duration"?Aa.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"duration",code:L.invalid_string,message:a.message}),e.dirty()):a.kind==="ip"?(s=t.data,((o=a.version)!=="v4"&&o||!Na.test(s))&&(o!=="v6"&&o||!Ia.test(s))&&(n=this._getOrReturnCtx(t,n),U(n,{validation:"ip",code:L.invalid_string,message:a.message}),e.dirty())):a.kind==="jwt"?Oa(t.data,a.alg)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"jwt",code:L.invalid_string,message:a.message}),e.dirty()):a.kind==="cidr"?Za(t.data,a.version)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"cidr",code:L.invalid_string,message:a.message}),e.dirty()):a.kind==="base64"?Pa.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"base64",code:L.invalid_string,message:a.message}),e.dirty()):a.kind==="base64url"?La.test(t.data)||(n=this._getOrReturnCtx(t,n),U(n,{validation:"base64url",code:L.invalid_string,message:a.message}),e.dirty()):ut.assertNever(a);var s,o;return{status:e.value,value:t.data}}_regex(t,e,n){return this.refinement(s=>t.test(s),{validation:e,code:L.invalid_string,...W.errToObj(n)})}_addCheck(t){return new Ut({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...W.errToObj(t)})}url(t){return this._addCheck({kind:"url",...W.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...W.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...W.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...W.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...W.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...W.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...W.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...W.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...W.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...W.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...W.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...W.errToObj(t)})}datetime(t){var e,n;return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:(t==null?void 0:t.precision)===void 0?null:t==null?void 0:t.precision,offset:(e=t==null?void 0:t.offset)!==null&&e!==void 0&&e,local:(n=t==null?void 0:t.local)!==null&&n!==void 0&&n,...W.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:(t==null?void 0:t.precision)===void 0?null:t==null?void 0:t.precision,...W.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...W.errToObj(t)})}regex(t,e){return this._addCheck({kind:"regex",regex:t,...W.errToObj(e)})}includes(t,e){return this._addCheck({kind:"includes",value:t,position:e==null?void 0:e.position,...W.errToObj(e==null?void 0:e.message)})}startsWith(t,e){return this._addCheck({kind:"startsWith",value:t,...W.errToObj(e)})}endsWith(t,e){return this._addCheck({kind:"endsWith",value:t,...W.errToObj(e)})}min(t,e){return this._addCheck({kind:"min",value:t,...W.errToObj(e)})}max(t,e){return this._addCheck({kind:"max",value:t,...W.errToObj(e)})}length(t,e){return this._addCheck({kind:"length",value:t,...W.errToObj(e)})}nonempty(t){return this.min(1,W.errToObj(t))}trim(){return new Ut({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Ut({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Ut({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const e of this._def.checks)e.kind==="min"&&(t===null||e.value>t)&&(t=e.value);return t}get maxLength(){let t=null;for(const e of this._def.checks)e.kind==="max"&&(t===null||e.value<t)&&(t=e.value);return t}}function Fa(r,t){const e=(r.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,s=e>n?e:n;return parseInt(r.toFixed(s).replace(".",""))%parseInt(t.toFixed(s).replace(".",""))/Math.pow(10,s)}Ut.create=r=>{var t;return new Ut({checks:[],typeName:X.ZodString,coerce:(t=r==null?void 0:r.coerce)!==null&&t!==void 0&&t,...et(r)})};class ce extends nt{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==H.number){const s=this._getOrReturnCtx(t);return U(s,{code:L.invalid_type,expected:H.number,received:s.parsedType}),Q}let e;const n=new Nt;for(const s of this._def.checks)s.kind==="int"?ut.isInteger(t.data)||(e=this._getOrReturnCtx(t,e),U(e,{code:L.invalid_type,expected:"integer",received:"float",message:s.message}),n.dirty()):s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(e=this._getOrReturnCtx(t,e),U(e,{code:L.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),n.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(e=this._getOrReturnCtx(t,e),U(e,{code:L.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),n.dirty()):s.kind==="multipleOf"?Fa(t.data,s.value)!==0&&(e=this._getOrReturnCtx(t,e),U(e,{code:L.not_multiple_of,multipleOf:s.value,message:s.message}),n.dirty()):s.kind==="finite"?Number.isFinite(t.data)||(e=this._getOrReturnCtx(t,e),U(e,{code:L.not_finite,message:s.message}),n.dirty()):ut.assertNever(s);return{status:n.value,value:t.data}}gte(t,e){return this.setLimit("min",t,!0,W.toString(e))}gt(t,e){return this.setLimit("min",t,!1,W.toString(e))}lte(t,e){return this.setLimit("max",t,!0,W.toString(e))}lt(t,e){return this.setLimit("max",t,!1,W.toString(e))}setLimit(t,e,n,s){return new ce({...this._def,checks:[...this._def.checks,{kind:t,value:e,inclusive:n,message:W.toString(s)}]})}_addCheck(t){return new ce({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:W.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:W.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:W.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:W.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:W.toString(t)})}multipleOf(t,e){return this._addCheck({kind:"multipleOf",value:t,message:W.toString(e)})}finite(t){return this._addCheck({kind:"finite",message:W.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:W.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:W.toString(t)})}get minValue(){let t=null;for(const e of this._def.checks)e.kind==="min"&&(t===null||e.value>t)&&(t=e.value);return t}get maxValue(){let t=null;for(const e of this._def.checks)e.kind==="max"&&(t===null||e.value<t)&&(t=e.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&ut.isInteger(t.value))}get isFinite(){let t=null,e=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(e===null||n.value>e)&&(e=n.value):n.kind==="max"&&(t===null||n.value<t)&&(t=n.value)}return Number.isFinite(e)&&Number.isFinite(t)}}ce.create=r=>new ce({checks:[],typeName:X.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...et(r)});class de extends nt{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==H.bigint)return this._getInvalidInput(t);let e;const n=new Nt;for(const s of this._def.checks)s.kind==="min"?(s.inclusive?t.data<s.value:t.data<=s.value)&&(e=this._getOrReturnCtx(t,e),U(e,{code:L.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),n.dirty()):s.kind==="max"?(s.inclusive?t.data>s.value:t.data>=s.value)&&(e=this._getOrReturnCtx(t,e),U(e,{code:L.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),n.dirty()):s.kind==="multipleOf"?t.data%s.value!==BigInt(0)&&(e=this._getOrReturnCtx(t,e),U(e,{code:L.not_multiple_of,multipleOf:s.value,message:s.message}),n.dirty()):ut.assertNever(s);return{status:n.value,value:t.data}}_getInvalidInput(t){const e=this._getOrReturnCtx(t);return U(e,{code:L.invalid_type,expected:H.bigint,received:e.parsedType}),Q}gte(t,e){return this.setLimit("min",t,!0,W.toString(e))}gt(t,e){return this.setLimit("min",t,!1,W.toString(e))}lte(t,e){return this.setLimit("max",t,!0,W.toString(e))}lt(t,e){return this.setLimit("max",t,!1,W.toString(e))}setLimit(t,e,n,s){return new de({...this._def,checks:[...this._def.checks,{kind:t,value:e,inclusive:n,message:W.toString(s)}]})}_addCheck(t){return new de({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:W.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:W.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:W.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:W.toString(t)})}multipleOf(t,e){return this._addCheck({kind:"multipleOf",value:t,message:W.toString(e)})}get minValue(){let t=null;for(const e of this._def.checks)e.kind==="min"&&(t===null||e.value>t)&&(t=e.value);return t}get maxValue(){let t=null;for(const e of this._def.checks)e.kind==="max"&&(t===null||e.value<t)&&(t=e.value);return t}}de.create=r=>{var t;return new de({checks:[],typeName:X.ZodBigInt,coerce:(t=r==null?void 0:r.coerce)!==null&&t!==void 0&&t,...et(r)})};class qe extends nt{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==H.boolean){const e=this._getOrReturnCtx(t);return U(e,{code:L.invalid_type,expected:H.boolean,received:e.parsedType}),Q}return Rt(t.data)}}qe.create=r=>new qe({typeName:X.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...et(r)});class xe extends nt{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==H.date){const s=this._getOrReturnCtx(t);return U(s,{code:L.invalid_type,expected:H.date,received:s.parsedType}),Q}if(isNaN(t.data.getTime()))return U(this._getOrReturnCtx(t),{code:L.invalid_date}),Q;const e=new Nt;let n;for(const s of this._def.checks)s.kind==="min"?t.data.getTime()<s.value&&(n=this._getOrReturnCtx(t,n),U(n,{code:L.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),e.dirty()):s.kind==="max"?t.data.getTime()>s.value&&(n=this._getOrReturnCtx(t,n),U(n,{code:L.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),e.dirty()):ut.assertNever(s);return{status:e.value,value:new Date(t.data.getTime())}}_addCheck(t){return new xe({...this._def,checks:[...this._def.checks,t]})}min(t,e){return this._addCheck({kind:"min",value:t.getTime(),message:W.toString(e)})}max(t,e){return this._addCheck({kind:"max",value:t.getTime(),message:W.toString(e)})}get minDate(){let t=null;for(const e of this._def.checks)e.kind==="min"&&(t===null||e.value>t)&&(t=e.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const e of this._def.checks)e.kind==="max"&&(t===null||e.value<t)&&(t=e.value);return t!=null?new Date(t):null}}xe.create=r=>new xe({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:X.ZodDate,...et(r)});class gn extends nt{_parse(t){if(this._getType(t)!==H.symbol){const e=this._getOrReturnCtx(t);return U(e,{code:L.invalid_type,expected:H.symbol,received:e.parsedType}),Q}return Rt(t.data)}}gn.create=r=>new gn({typeName:X.ZodSymbol,...et(r)});class Be extends nt{_parse(t){if(this._getType(t)!==H.undefined){const e=this._getOrReturnCtx(t);return U(e,{code:L.invalid_type,expected:H.undefined,received:e.parsedType}),Q}return Rt(t.data)}}Be.create=r=>new Be({typeName:X.ZodUndefined,...et(r)});class He extends nt{_parse(t){if(this._getType(t)!==H.null){const e=this._getOrReturnCtx(t);return U(e,{code:L.invalid_type,expected:H.null,received:e.parsedType}),Q}return Rt(t.data)}}He.create=r=>new He({typeName:X.ZodNull,...et(r)});class Ie extends nt{constructor(){super(...arguments),this._any=!0}_parse(t){return Rt(t.data)}}Ie.create=r=>new Ie({typeName:X.ZodAny,...et(r)});class ge extends nt{constructor(){super(...arguments),this._unknown=!0}_parse(t){return Rt(t.data)}}ge.create=r=>new ge({typeName:X.ZodUnknown,...et(r)});class ne extends nt{_parse(t){const e=this._getOrReturnCtx(t);return U(e,{code:L.invalid_type,expected:H.never,received:e.parsedType}),Q}}ne.create=r=>new ne({typeName:X.ZodNever,...et(r)});class hn extends nt{_parse(t){if(this._getType(t)!==H.undefined){const e=this._getOrReturnCtx(t);return U(e,{code:L.invalid_type,expected:H.void,received:e.parsedType}),Q}return Rt(t.data)}}hn.create=r=>new hn({typeName:X.ZodVoid,...et(r)});class qt extends nt{_parse(t){const{ctx:e,status:n}=this._processInputParams(t),s=this._def;if(e.parsedType!==H.array)return U(e,{code:L.invalid_type,expected:H.array,received:e.parsedType}),Q;if(s.exactLength!==null){const a=e.data.length>s.exactLength.value,l=e.data.length<s.exactLength.value;(a||l)&&(U(e,{code:a?L.too_big:L.too_small,minimum:l?s.exactLength.value:void 0,maximum:a?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),n.dirty())}if(s.minLength!==null&&e.data.length<s.minLength.value&&(U(e,{code:L.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),n.dirty()),s.maxLength!==null&&e.data.length>s.maxLength.value&&(U(e,{code:L.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),n.dirty()),e.common.async)return Promise.all([...e.data].map((a,l)=>s.type._parseAsync(new Jt(e,a,e.path,l)))).then(a=>Nt.mergeArray(n,a));const o=[...e.data].map((a,l)=>s.type._parseSync(new Jt(e,a,e.path,l)));return Nt.mergeArray(n,o)}get element(){return this._def.type}min(t,e){return new qt({...this._def,minLength:{value:t,message:W.toString(e)}})}max(t,e){return new qt({...this._def,maxLength:{value:t,message:W.toString(e)}})}length(t,e){return new qt({...this._def,exactLength:{value:t,message:W.toString(e)}})}nonempty(t){return this.min(1,t)}}function ke(r){if(r instanceof yt){const t={};for(const e in r.shape){const n=r.shape[e];t[e]=Bt.create(ke(n))}return new yt({...r._def,shape:()=>t})}return r instanceof qt?new qt({...r._def,type:ke(r.element)}):r instanceof Bt?Bt.create(ke(r.unwrap())):r instanceof pe?pe.create(ke(r.unwrap())):r instanceof Wt?Wt.create(r.items.map(t=>ke(t))):r}qt.create=(r,t)=>new qt({type:r,minLength:null,maxLength:null,exactLength:null,typeName:X.ZodArray,...et(t)});class yt extends nt{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),e=ut.objectKeys(t);return this._cached={shape:t,keys:e}}_parse(t){if(this._getType(t)!==H.object){const i=this._getOrReturnCtx(t);return U(i,{code:L.invalid_type,expected:H.object,received:i.parsedType}),Q}const{status:e,ctx:n}=this._processInputParams(t),{shape:s,keys:o}=this._getCached(),a=[];if(!(this._def.catchall instanceof ne&&this._def.unknownKeys==="strip"))for(const i in n.data)o.includes(i)||a.push(i);const l=[];for(const i of o){const c=s[i],d=n.data[i];l.push({key:{status:"valid",value:i},value:c._parse(new Jt(n,d,n.path,i)),alwaysSet:i in n.data})}if(this._def.catchall instanceof ne){const i=this._def.unknownKeys;if(i==="passthrough")for(const c of a)l.push({key:{status:"valid",value:c},value:{status:"valid",value:n.data[c]}});else if(i==="strict")a.length>0&&(U(n,{code:L.unrecognized_keys,keys:a}),e.dirty());else if(i!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const i=this._def.catchall;for(const c of a){const d=n.data[c];l.push({key:{status:"valid",value:c},value:i._parse(new Jt(n,d,n.path,c)),alwaysSet:c in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const i=[];for(const c of l){const d=await c.key,u=await c.value;i.push({key:d,value:u,alwaysSet:c.alwaysSet})}return i}).then(i=>Nt.mergeObjectSync(e,i)):Nt.mergeObjectSync(e,l)}get shape(){return this._def.shape()}strict(t){return W.errToObj,new yt({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(e,n)=>{var s,o,a,l;const i=(a=(o=(s=this._def).errorMap)===null||o===void 0?void 0:o.call(s,e,n).message)!==null&&a!==void 0?a:n.defaultError;return e.code==="unrecognized_keys"?{message:(l=W.errToObj(t).message)!==null&&l!==void 0?l:i}:{message:i}}}:{}})}strip(){return new yt({...this._def,unknownKeys:"strip"})}passthrough(){return new yt({...this._def,unknownKeys:"passthrough"})}extend(t){return new yt({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new yt({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:X.ZodObject})}setKey(t,e){return this.augment({[t]:e})}catchall(t){return new yt({...this._def,catchall:t})}pick(t){const e={};return ut.objectKeys(t).forEach(n=>{t[n]&&this.shape[n]&&(e[n]=this.shape[n])}),new yt({...this._def,shape:()=>e})}omit(t){const e={};return ut.objectKeys(this.shape).forEach(n=>{t[n]||(e[n]=this.shape[n])}),new yt({...this._def,shape:()=>e})}deepPartial(){return ke(this)}partial(t){const e={};return ut.objectKeys(this.shape).forEach(n=>{const s=this.shape[n];t&&!t[n]?e[n]=s:e[n]=s.optional()}),new yt({...this._def,shape:()=>e})}required(t){const e={};return ut.objectKeys(this.shape).forEach(n=>{if(t&&!t[n])e[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof Bt;)s=s._def.innerType;e[n]=s}}),new yt({...this._def,shape:()=>e})}keyof(){return mo(ut.objectKeys(this.shape))}}yt.create=(r,t)=>new yt({shape:()=>r,unknownKeys:"strip",catchall:ne.create(),typeName:X.ZodObject,...et(t)}),yt.strictCreate=(r,t)=>new yt({shape:()=>r,unknownKeys:"strict",catchall:ne.create(),typeName:X.ZodObject,...et(t)}),yt.lazycreate=(r,t)=>new yt({shape:r,unknownKeys:"strip",catchall:ne.create(),typeName:X.ZodObject,...et(t)});class Ge extends nt{_parse(t){const{ctx:e}=this._processInputParams(t),n=this._def.options;if(e.common.async)return Promise.all(n.map(async s=>{const o={...e,common:{...e.common,issues:[]},parent:null};return{result:await s._parseAsync({data:e.data,path:e.path,parent:o}),ctx:o}})).then(function(s){for(const a of s)if(a.result.status==="valid")return a.result;for(const a of s)if(a.result.status==="dirty")return e.common.issues.push(...a.ctx.common.issues),a.result;const o=s.map(a=>new Pt(a.ctx.common.issues));return U(e,{code:L.invalid_union,unionErrors:o}),Q});{let s;const o=[];for(const l of n){const i={...e,common:{...e.common,issues:[]},parent:null},c=l._parseSync({data:e.data,path:e.path,parent:i});if(c.status==="valid")return c;c.status!=="dirty"||s||(s={result:c,ctx:i}),i.common.issues.length&&o.push(i.common.issues)}if(s)return e.common.issues.push(...s.ctx.common.issues),s.result;const a=o.map(l=>new Pt(l));return U(e,{code:L.invalid_union,unionErrors:a}),Q}}get options(){return this._def.options}}Ge.create=(r,t)=>new Ge({options:r,typeName:X.ZodUnion,...et(t)});const ae=r=>r instanceof We?ae(r.schema):r instanceof Vt?ae(r.innerType()):r instanceof Ke?[r.value]:r instanceof ue?r.options:r instanceof Ye?ut.objectValues(r.enum):r instanceof Xe?ae(r._def.innerType):r instanceof Be?[void 0]:r instanceof He?[null]:r instanceof Bt?[void 0,...ae(r.unwrap())]:r instanceof pe?[null,...ae(r.unwrap())]:r instanceof Qn||r instanceof tn?ae(r.unwrap()):r instanceof Qe?ae(r._def.innerType):[];class An extends nt{_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==H.object)return U(e,{code:L.invalid_type,expected:H.object,received:e.parsedType}),Q;const n=this.discriminator,s=e.data[n],o=this.optionsMap.get(s);return o?e.common.async?o._parseAsync({data:e.data,path:e.path,parent:e}):o._parseSync({data:e.data,path:e.path,parent:e}):(U(e,{code:L.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),Q)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(t,e,n){const s=new Map;for(const o of e){const a=ae(o.shape[t]);if(!a.length)throw new Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);for(const l of a){if(s.has(l))throw new Error(`Discriminator property ${String(t)} has duplicate value ${String(l)}`);s.set(l,o)}}return new An({typeName:X.ZodDiscriminatedUnion,discriminator:t,options:e,optionsMap:s,...et(n)})}}function Bn(r,t){const e=Xt(r),n=Xt(t);if(r===t)return{valid:!0,data:r};if(e===H.object&&n===H.object){const s=ut.objectKeys(t),o=ut.objectKeys(r).filter(l=>s.indexOf(l)!==-1),a={...r,...t};for(const l of o){const i=Bn(r[l],t[l]);if(!i.valid)return{valid:!1};a[l]=i.data}return{valid:!0,data:a}}if(e===H.array&&n===H.array){if(r.length!==t.length)return{valid:!1};const s=[];for(let o=0;o<r.length;o++){const a=Bn(r[o],t[o]);if(!a.valid)return{valid:!1};s.push(a.data)}return{valid:!0,data:s}}return e===H.date&&n===H.date&&+r==+t?{valid:!0,data:r}:{valid:!1}}class Je extends nt{_parse(t){const{status:e,ctx:n}=this._processInputParams(t),s=(o,a)=>{if(Vn(o)||Vn(a))return Q;const l=Bn(o.value,a.value);return l.valid?((qn(o)||qn(a))&&e.dirty(),{status:e.value,value:l.data}):(U(n,{code:L.invalid_intersection_types}),Q)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([o,a])=>s(o,a)):s(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Je.create=(r,t,e)=>new Je({left:r,right:t,typeName:X.ZodIntersection,...et(e)});class Wt extends nt{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==H.array)return U(n,{code:L.invalid_type,expected:H.array,received:n.parsedType}),Q;if(n.data.length<this._def.items.length)return U(n,{code:L.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Q;!this._def.rest&&n.data.length>this._def.items.length&&(U(n,{code:L.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),e.dirty());const s=[...n.data].map((o,a)=>{const l=this._def.items[a]||this._def.rest;return l?l._parse(new Jt(n,o,n.path,a)):null}).filter(o=>!!o);return n.common.async?Promise.all(s).then(o=>Nt.mergeArray(e,o)):Nt.mergeArray(e,s)}get items(){return this._def.items}rest(t){return new Wt({...this._def,rest:t})}}Wt.create=(r,t)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Wt({items:r,typeName:X.ZodTuple,rest:null,...et(t)})};class Tn extends nt{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==H.object)return U(n,{code:L.invalid_type,expected:H.object,received:n.parsedType}),Q;const s=[],o=this._def.keyType,a=this._def.valueType;for(const l in n.data)s.push({key:o._parse(new Jt(n,l,n.path,l)),value:a._parse(new Jt(n,n.data[l],n.path,l)),alwaysSet:l in n.data});return n.common.async?Nt.mergeObjectAsync(e,s):Nt.mergeObjectSync(e,s)}get element(){return this._def.valueType}static create(t,e,n){return new Tn(e instanceof nt?{keyType:t,valueType:e,typeName:X.ZodRecord,...et(n)}:{keyType:Ut.create(),valueType:t,typeName:X.ZodRecord,...et(e)})}}class vn extends nt{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==H.map)return U(n,{code:L.invalid_type,expected:H.map,received:n.parsedType}),Q;const s=this._def.keyType,o=this._def.valueType,a=[...n.data.entries()].map(([l,i],c)=>({key:s._parse(new Jt(n,l,n.path,[c,"key"])),value:o._parse(new Jt(n,i,n.path,[c,"value"]))}));if(n.common.async){const l=new Map;return Promise.resolve().then(async()=>{for(const i of a){const c=await i.key,d=await i.value;if(c.status==="aborted"||d.status==="aborted")return Q;c.status!=="dirty"&&d.status!=="dirty"||e.dirty(),l.set(c.value,d.value)}return{status:e.value,value:l}})}{const l=new Map;for(const i of a){const c=i.key,d=i.value;if(c.status==="aborted"||d.status==="aborted")return Q;c.status!=="dirty"&&d.status!=="dirty"||e.dirty(),l.set(c.value,d.value)}return{status:e.value,value:l}}}}vn.create=(r,t,e)=>new vn({valueType:t,keyType:r,typeName:X.ZodMap,...et(e)});class Ce extends nt{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==H.set)return U(n,{code:L.invalid_type,expected:H.set,received:n.parsedType}),Q;const s=this._def;s.minSize!==null&&n.data.size<s.minSize.value&&(U(n,{code:L.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),e.dirty()),s.maxSize!==null&&n.data.size>s.maxSize.value&&(U(n,{code:L.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),e.dirty());const o=this._def.valueType;function a(i){const c=new Set;for(const d of i){if(d.status==="aborted")return Q;d.status==="dirty"&&e.dirty(),c.add(d.value)}return{status:e.value,value:c}}const l=[...n.data.values()].map((i,c)=>o._parse(new Jt(n,i,n.path,c)));return n.common.async?Promise.all(l).then(i=>a(i)):a(l)}min(t,e){return new Ce({...this._def,minSize:{value:t,message:W.toString(e)}})}max(t,e){return new Ce({...this._def,maxSize:{value:t,message:W.toString(e)}})}size(t,e){return this.min(t,e).max(t,e)}nonempty(t){return this.min(1,t)}}Ce.create=(r,t)=>new Ce({valueType:r,minSize:null,maxSize:null,typeName:X.ZodSet,...et(t)});class Ae extends nt{constructor(){super(...arguments),this.validate=this.implement}_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==H.function)return U(e,{code:L.invalid_type,expected:H.function,received:e.parsedType}),Q;function n(l,i){return mn({data:l,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,pn(),Ee].filter(c=>!!c),issueData:{code:L.invalid_arguments,argumentsError:i}})}function s(l,i){return mn({data:l,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,pn(),Ee].filter(c=>!!c),issueData:{code:L.invalid_return_type,returnTypeError:i}})}const o={errorMap:e.common.contextualErrorMap},a=e.data;if(this._def.returns instanceof Re){const l=this;return Rt(async function(...i){const c=new Pt([]),d=await l._def.args.parseAsync(i,o).catch(f=>{throw c.addIssue(n(i,f)),c}),u=await Reflect.apply(a,this,d);return await l._def.returns._def.type.parseAsync(u,o).catch(f=>{throw c.addIssue(s(u,f)),c})})}{const l=this;return Rt(function(...i){const c=l._def.args.safeParse(i,o);if(!c.success)throw new Pt([n(i,c.error)]);const d=Reflect.apply(a,this,c.data),u=l._def.returns.safeParse(d,o);if(!u.success)throw new Pt([s(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...t){return new Ae({...this._def,args:Wt.create(t).rest(ge.create())})}returns(t){return new Ae({...this._def,returns:t})}implement(t){return this.parse(t)}strictImplement(t){return this.parse(t)}static create(t,e,n){return new Ae({args:t||Wt.create([]).rest(ge.create()),returns:e||ge.create(),typeName:X.ZodFunction,...et(n)})}}class We extends nt{get schema(){return this._def.getter()}_parse(t){const{ctx:e}=this._processInputParams(t);return this._def.getter()._parse({data:e.data,path:e.path,parent:e})}}We.create=(r,t)=>new We({getter:r,typeName:X.ZodLazy,...et(t)});class Ke extends nt{_parse(t){if(t.data!==this._def.value){const e=this._getOrReturnCtx(t);return U(e,{received:e.data,code:L.invalid_literal,expected:this._def.value}),Q}return{status:"valid",value:t.data}}get value(){return this._def.value}}function mo(r,t){return new ue({values:r,typeName:X.ZodEnum,...et(t)})}Ke.create=(r,t)=>new Ke({value:r,typeName:X.ZodLiteral,...et(t)});class ue extends nt{constructor(){super(...arguments),Fe.set(this,void 0)}_parse(t){if(typeof t.data!="string"){const e=this._getOrReturnCtx(t),n=this._def.values;return U(e,{expected:ut.joinValues(n),received:e.parsedType,code:L.invalid_type}),Q}if($n(this,Fe)||lo(this,Fe,new Set(this._def.values)),!$n(this,Fe).has(t.data)){const e=this._getOrReturnCtx(t),n=this._def.values;return U(e,{received:e.data,code:L.invalid_enum_value,options:n}),Q}return Rt(t.data)}get options(){return this._def.values}get enum(){const t={};for(const e of this._def.values)t[e]=e;return t}get Values(){const t={};for(const e of this._def.values)t[e]=e;return t}get Enum(){const t={};for(const e of this._def.values)t[e]=e;return t}extract(t,e=this._def){return ue.create(t,{...this._def,...e})}exclude(t,e=this._def){return ue.create(this.options.filter(n=>!t.includes(n)),{...this._def,...e})}}Fe=new WeakMap,ue.create=mo;class Ye extends nt{constructor(){super(...arguments),je.set(this,void 0)}_parse(t){const e=ut.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(t);if(n.parsedType!==H.string&&n.parsedType!==H.number){const s=ut.objectValues(e);return U(n,{expected:ut.joinValues(s),received:n.parsedType,code:L.invalid_type}),Q}if($n(this,je)||lo(this,je,new Set(ut.getValidEnumValues(this._def.values))),!$n(this,je).has(t.data)){const s=ut.objectValues(e);return U(n,{received:n.data,code:L.invalid_enum_value,options:s}),Q}return Rt(t.data)}get enum(){return this._def.values}}je=new WeakMap,Ye.create=(r,t)=>new Ye({values:r,typeName:X.ZodNativeEnum,...et(t)});class Re extends nt{unwrap(){return this._def.type}_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==H.promise&&e.common.async===!1)return U(e,{code:L.invalid_type,expected:H.promise,received:e.parsedType}),Q;const n=e.parsedType===H.promise?e.data:Promise.resolve(e.data);return Rt(n.then(s=>this._def.type.parseAsync(s,{path:e.path,errorMap:e.common.contextualErrorMap})))}}Re.create=(r,t)=>new Re({type:r,typeName:X.ZodPromise,...et(t)});class Vt extends nt{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===X.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:e,ctx:n}=this._processInputParams(t),s=this._def.effect||null,o={addIssue:a=>{U(n,a),a.fatal?e.abort():e.dirty()},get path(){return n.path}};if(o.addIssue=o.addIssue.bind(o),s.type==="preprocess"){const a=s.transform(n.data,o);if(n.common.async)return Promise.resolve(a).then(async l=>{if(e.value==="aborted")return Q;const i=await this._def.schema._parseAsync({data:l,path:n.path,parent:n});return i.status==="aborted"?Q:i.status==="dirty"||e.value==="dirty"?fn(i.value):i});{if(e.value==="aborted")return Q;const l=this._def.schema._parseSync({data:a,path:n.path,parent:n});return l.status==="aborted"?Q:l.status==="dirty"||e.value==="dirty"?fn(l.value):l}}if(s.type==="refinement"){const a=l=>{const i=s.refinement(l,o);if(n.common.async)return Promise.resolve(i);if(i instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return l};if(n.common.async===!1){const l=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return l.status==="aborted"?Q:(l.status==="dirty"&&e.dirty(),a(l.value),{status:e.value,value:l.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(l=>l.status==="aborted"?Q:(l.status==="dirty"&&e.dirty(),a(l.value).then(()=>({status:e.value,value:l.value}))))}if(s.type==="transform"){if(n.common.async===!1){const a=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!we(a))return a;const l=s.transform(a.value,o);if(l instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:e.value,value:l}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(a=>we(a)?Promise.resolve(s.transform(a.value,o)).then(l=>({status:e.value,value:l})):a)}ut.assertNever(s)}}Vt.create=(r,t,e)=>new Vt({schema:r,typeName:X.ZodEffects,effect:t,...et(e)}),Vt.createWithPreprocess=(r,t,e)=>new Vt({schema:t,effect:{type:"preprocess",transform:r},typeName:X.ZodEffects,...et(e)});class Bt extends nt{_parse(t){return this._getType(t)===H.undefined?Rt(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Bt.create=(r,t)=>new Bt({innerType:r,typeName:X.ZodOptional,...et(t)});class pe extends nt{_parse(t){return this._getType(t)===H.null?Rt(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}pe.create=(r,t)=>new pe({innerType:r,typeName:X.ZodNullable,...et(t)});class Xe extends nt{_parse(t){const{ctx:e}=this._processInputParams(t);let n=e.data;return e.parsedType===H.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:e.path,parent:e})}removeDefault(){return this._def.innerType}}Xe.create=(r,t)=>new Xe({innerType:r,typeName:X.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...et(t)});class Qe extends nt{_parse(t){const{ctx:e}=this._processInputParams(t),n={...e,common:{...e.common,issues:[]}},s=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Ve(s)?s.then(o=>({status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new Pt(n.common.issues)},input:n.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new Pt(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Qe.create=(r,t)=>new Qe({innerType:r,typeName:X.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...et(t)});class yn extends nt{_parse(t){if(this._getType(t)!==H.nan){const e=this._getOrReturnCtx(t);return U(e,{code:L.invalid_type,expected:H.nan,received:e.parsedType}),Q}return{status:"valid",value:t.data}}}yn.create=r=>new yn({typeName:X.ZodNaN,...et(r)});const ja=Symbol("zod_brand");class Qn extends nt{_parse(t){const{ctx:e}=this._processInputParams(t),n=e.data;return this._def.type._parse({data:n,path:e.path,parent:e})}unwrap(){return this._def.type}}class rn extends nt{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.common.async)return(async()=>{const s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?Q:s.status==="dirty"?(e.dirty(),fn(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{const s=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?Q:s.status==="dirty"?(e.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:n.path,parent:n})}}static create(t,e){return new rn({in:t,out:e,typeName:X.ZodPipeline})}}class tn extends nt{_parse(t){const e=this._def.innerType._parse(t),n=s=>(we(s)&&(s.value=Object.freeze(s.value)),s);return Ve(e)?e.then(s=>n(s)):n(e)}unwrap(){return this._def.innerType}}function $s(r,t={},e){return r?Ie.create().superRefine((n,s)=>{var o,a;if(!r(n)){const l=typeof t=="function"?t(n):typeof t=="string"?{message:t}:t,i=(a=(o=l.fatal)!==null&&o!==void 0?o:e)===null||a===void 0||a,c=typeof l=="string"?{message:l}:l;s.addIssue({code:"custom",...c,fatal:i})}}):Ie.create()}tn.create=(r,t)=>new tn({innerType:r,typeName:X.ZodReadonly,...et(t)});const Da={object:yt.lazycreate};var X;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(X||(X={}));const gs=Ut.create,hs=ce.create,Ua=yn.create,Va=de.create,vs=qe.create,qa=xe.create,Ba=gn.create,Ha=Be.create,Ga=He.create,Ja=Ie.create,Wa=ge.create,Ka=ne.create,Ya=hn.create,Xa=qt.create,Qa=yt.create,ti=yt.strictCreate,ei=Ge.create,ni=An.create,si=Je.create,ri=Wt.create,oi=Tn.create,ai=vn.create,ii=Ce.create,li=Ae.create,ci=We.create,di=Ke.create,ui=ue.create,pi=Ye.create,mi=Re.create,ys=Vt.create,fi=Bt.create,$i=pe.create,gi=Vt.createWithPreprocess,hi=rn.create,vi={string:r=>Ut.create({...r,coerce:!0}),number:r=>ce.create({...r,coerce:!0}),boolean:r=>qe.create({...r,coerce:!0}),bigint:r=>de.create({...r,coerce:!0}),date:r=>xe.create({...r,coerce:!0})},yi=Q;var mt=Object.freeze({__proto__:null,defaultErrorMap:Ee,setErrorMap:function(r){io=r},getErrorMap:pn,makeIssue:mn,EMPTY_PATH:[],addIssueToContext:U,ParseStatus:Nt,INVALID:Q,DIRTY:fn,OK:Rt,isAborted:Vn,isDirty:qn,isValid:we,isAsync:Ve,get util(){return ut},get objectUtil(){return Un},ZodParsedType:H,getParsedType:Xt,ZodType:nt,datetimeRegex:po,ZodString:Ut,ZodNumber:ce,ZodBigInt:de,ZodBoolean:qe,ZodDate:xe,ZodSymbol:gn,ZodUndefined:Be,ZodNull:He,ZodAny:Ie,ZodUnknown:ge,ZodNever:ne,ZodVoid:hn,ZodArray:qt,ZodObject:yt,ZodUnion:Ge,ZodDiscriminatedUnion:An,ZodIntersection:Je,ZodTuple:Wt,ZodRecord:Tn,ZodMap:vn,ZodSet:Ce,ZodFunction:Ae,ZodLazy:We,ZodLiteral:Ke,ZodEnum:ue,ZodNativeEnum:Ye,ZodPromise:Re,ZodEffects:Vt,ZodTransformer:Vt,ZodOptional:Bt,ZodNullable:pe,ZodDefault:Xe,ZodCatch:Qe,ZodNaN:yn,BRAND:ja,ZodBranded:Qn,ZodPipeline:rn,ZodReadonly:tn,custom:$s,Schema:nt,ZodSchema:nt,late:Da,get ZodFirstPartyTypeKind(){return X},coerce:vi,any:Ja,array:Xa,bigint:Va,boolean:vs,date:qa,discriminatedUnion:ni,effect:ys,enum:ui,function:li,instanceof:(r,t={message:`Input not instance of ${r.name}`})=>$s(e=>e instanceof r,t),intersection:si,lazy:ci,literal:di,map:ai,nan:Ua,nativeEnum:pi,never:Ka,null:Ga,nullable:$i,number:hs,object:Qa,oboolean:()=>vs().optional(),onumber:()=>hs().optional(),optional:fi,ostring:()=>gs().optional(),pipeline:hi,preprocess:gi,promise:mi,record:oi,set:ii,strictObject:ti,string:gs,symbol:Ba,transformer:ys,tuple:ri,undefined:Ha,union:ei,unknown:Wa,void:Ya,NEVER:yi,ZodIssueCode:L,quotelessJson:r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:Pt});class Tt extends Error{constructor(t){super(t),this.name="MCPServerError",Object.setPrototypeOf(this,Tt.prototype)}}const Qt=mt.object({name:mt.string().optional(),title:mt.string().optional(),type:mt.enum(["stdio","http","sse"]).optional(),command:mt.string().optional(),args:mt.array(mt.union([mt.string(),mt.number(),mt.boolean()])).optional(),env:mt.record(mt.union([mt.string(),mt.number(),mt.boolean(),mt.null(),mt.undefined()])).optional(),url:mt.string().optional()}).passthrough();function Dt(r){return(r==null?void 0:r.type)==="http"||(r==null?void 0:r.type)==="sse"}function Me(r){return(r==null?void 0:r.type)==="stdio"}function wn(r){return Dt(r)?r.url:Me(r)?r.command:""}const wi=mt.array(Qt),xi=mt.object({servers:mt.array(Qt)}),Ci=mt.object({mcpServers:mt.array(Qt)}),_i=mt.object({servers:mt.record(mt.unknown())}),bi=mt.object({mcpServers:mt.record(mt.unknown())}),Si=mt.record(mt.unknown()),ki=Qt.refine(r=>{const t=r.command!==void 0,e=r.url!==void 0;if(!t&&!e)return!1;const n=new Set(["name","title","type","command","args","env","url"]);return Object.keys(r).every(s=>n.has(s))},{message:"Single server object must have valid server properties"});function $e(r){try{const t=Qt.transform(e=>{let n;if(e.type)n=e.type;else if(e.url)n="http";else{if(!e.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");n="stdio"}if(n==="http"||n==="sse"){if(!e.url)throw new Error(`${n.toUpperCase()} server must have a 'url' property`);return{type:n,name:e.name||e.title||e.url,url:e.url}}{const s=e.command||"",o=e.args?e.args.map(c=>String(c)):[];if(!s)throw new Error("Stdio server must have a 'command' property");const a=o.length>0?`${s} ${o.join(" ")}`:s,l=e.name||e.title||(s?s.split(" ")[0]:""),i=e.env?Object.fromEntries(Object.entries(e.env).filter(([c,d])=>d!=null).map(([c,d])=>[c,String(d)])):void 0;return{type:"stdio",name:l,command:a,arguments:"",useShellInterpolation:!0,env:Object.keys(i||{}).length>0?i:void 0}}}).refine(e=>!!e.name,{message:"Server must have a name",path:["name"]}).refine(e=>e.type==="http"||e.type==="sse"?!!e.url:!!e.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(r);if(!t.success)throw new Tt(t.error.message);return t.data}catch(t){throw t instanceof Error?new Tt(`Invalid server configuration: ${t.message}`):new Tt("Invalid server configuration")}}class on{constructor(t){it(this,"servers",kt([]));this.host=t,this.loadServersFromStorage()}handleMessageFromExtension(t){const e=t.data;if(e.type===ht.getStoredMCPServersResponse){const n=e.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(t){return this.importFromJSON(t)}loadServersFromStorage(){try{this.host.postMessage({type:ht.getStoredMCPServers})}catch(t){console.error("Failed to load MCP servers:",t),this.servers.set([])}}saveServers(t){try{this.host.postMessage({type:ht.setStoredMCPServers,data:t})}catch(e){throw console.error("Failed to save MCP servers:",e),new Tt("Failed to save MCP servers")}}getServers(){return this.servers}addServer(t){this.checkExistingServerName(t.name),this.servers.update(e=>{const n=[...e,{...t,id:crypto.randomUUID()}];return this.saveServers(n),n})}addServers(t){for(const e of t)this.checkExistingServerName(e.name);this.servers.update(e=>{const n=[...e,...t.map(s=>({...s,id:crypto.randomUUID()}))];return this.saveServers(n),n})}checkExistingServerName(t,e){const n=Fn(this.servers).find(s=>s.name===t);if(n&&(n==null?void 0:n.id)!==e)throw new Tt(`Server name '${t}' already exists`)}updateServer(t){this.checkExistingServerName(t.name,t.id),this.servers.update(e=>{const n=e.map(s=>s.id===t.id?t:s);return this.saveServers(n),n})}deleteServer(t){this.servers.update(e=>{const n=e.filter(s=>s.id!==t);return this.saveServers(n),n})}toggleDisabledServer(t){this.servers.update(e=>{const n=e.map(s=>s.id===t?{...s,disabled:!s.disabled}:s);return this.saveServers(n),n})}static convertServerToJSON(t){if(Dt(t))return JSON.stringify({mcpServers:{[t.name]:{url:t.url,type:t.type}}},null,2);{const e=t;return JSON.stringify({mcpServers:{[e.name]:{command:e.command.split(" ")[0],args:e.command.split(" ").slice(1),env:e.env}}},null,2)}}static parseServerValidationMessages(t){const e=new Map,n=new Map;t.forEach(o=>{var l,i;const a=(l=o.tools)==null?void 0:l.filter(c=>!c.enabled).map(c=>c.definition.mcp_tool_name);o.disabled?e.set(o.id,"MCP server has been manually disabled"):o.tools&&o.tools.length===0?e.set(o.id,"No tools are available for this MCP server"):a&&a.length===((i=o.tools)==null?void 0:i.length)?e.set(o.id,"All tools for this MCP server have validation errors: "+a.join(", ")):a&&a.length>0&&n.set(o.id,"MCP server has validation errors in the following tools which have been disabled: "+a.join(", "))});const s=this.parseDuplicateServerIds(t);return{errors:new Map([...e,...s]),warnings:n}}static parseDuplicateServerIds(t){const e=new Map;for(const s of t)e.has(s.name)||e.set(s.name,[]),e.get(s.name).push(s.id);const n=new Map;for(const[,s]of e)if(s.length>1)for(let o=1;o<s.length;o++)n.set(s[o],"MCP server is disabled due to duplicate server names");return n}static convertParsedServerToWebview(t){const{tools:e,...n}=t;return{...n,tools:void 0}}static parseServerConfigFromJSON(t){return function(n){try{const s=JSON.parse(n),o=mt.union([wi.transform(a=>a.map(l=>$e(l))),xi.transform(a=>a.servers.map(l=>$e(l))),Ci.transform(a=>a.mcpServers.map(l=>$e(l))),_i.transform(a=>Object.entries(a.servers).map(([l,i])=>{const c=Qt.parse(i);return $e({...c,name:c.name||l})})),bi.transform(a=>Object.entries(a.mcpServers).map(([l,i])=>{const c=Qt.parse(i);return $e({...c,name:c.name||l})})),ki.transform(a=>[$e(a)]),Si.transform(a=>{if(!Object.values(a).some(l=>{const i=Qt.safeParse(l);return i.success&&(i.data.command!==void 0||i.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(a).map(([l,i])=>{const c=Qt.parse(i);return $e({...c,name:c.name||l})})})]).safeParse(s);if(o.success)return o.data;throw new Tt("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(s){throw s instanceof Tt?s:new Tt("Failed to parse MCP servers from JSON. Please check the format.")}}(t).map(n=>this.convertParsedServerToWebview(n))}importFromJSON(t){try{const e=on.parseServerConfigFromJSON(t),n=Fn(this.servers),s=new Set(n.map(o=>o.name));for(const o of e){if(!o.name)throw new Tt("All servers must have a name.");if(s.has(o.name))throw new Tt(`A server with the name '${o.name}' already exists.`);s.add(o.name)}return this.servers.update(o=>{const a=[...o,...e.map(l=>({...l,id:crypto.randomUUID()}))];return this.saveServers(a),a}),e.length}catch(e){throw e instanceof Tt?e:new Tt("Failed to import MCP servers from JSON. Please check the format.")}}}class Mi{constructor(t){it(this,"_terminalSettings",kt({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=t,this.requestTerminalSettings()}handleMessageFromExtension(t){const e=t.data;return e.type===ht.terminalSettingsResponse&&(this._terminalSettings.set(e.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:ht.getTerminalSettings})}updateSelectedShell(t){this._terminalSettings.update(e=>({...e,selectedShell:t})),this._host.postMessage({type:ht.updateTerminalSettings,data:{selectedShell:t}})}updateStartupScript(t){this._terminalSettings.update(e=>({...e,startupScript:t})),this._host.postMessage({type:ht.updateTerminalSettings,data:{startupScript:t}})}}const De=class De{constructor(t){it(this,"_swarmModeSettings",kt(cn));it(this,"_isLoaded",!1);it(this,"_pollInterval",null);it(this,"_lastKnownSettingsHash","");it(this,"dispose",()=>{this.stopPolling()});this._msgBroker=t,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const t=await this._msgBroker.sendToSidecar({type:Ln.getSwarmModeSettings});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data)),this._isLoaded=!0}catch(t){console.warn("Failed to load swarm mode settings, using defaults:",t),this._swarmModeSettings.set(cn),this._lastKnownSettingsHash=JSON.stringify(cn),this._isLoaded=!0}}async updateSettings(t){try{const e=await this._msgBroker.sendToSidecar({type:Ln.updateSwarmModeSettings,data:t});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data))}catch(e){throw console.error("Failed to update swarm mode settings:",e),e}}async setEnabled(t){await this.updateSettings({enabled:t})}async resetToDefaults(){await this.updateSettings(cn)}updateEnabled(t){this.setEnabled(t).catch(e=>{console.error("Failed to update enabled setting:",e)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},De.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const t=await this._msgBroker.sendToSidecar({type:Ln.getSwarmModeSettings}),e=JSON.stringify(t.data);this._lastKnownSettingsHash&&e!==this._lastKnownSettingsHash&&t.data&&this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=e}catch(t){console.warn("Failed to check for swarm mode settings updates:",t)}}};it(De,"key","swarmModeModel"),it(De,"POLLING_INTERVAL_MS",5e3);let en=De;var te=(r=>(r.file="file",r.folder="folder",r))(te||{});class ie{constructor(t,e){it(this,"subscribe");it(this,"set");it(this,"update");it(this,"handleMessageFromExtension",async t=>{const e=t.data;switch(e.type){case ht.wsContextSourceFoldersChanged:case ht.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case ht.sourceFoldersSyncStatus:this.update(n=>({...n,syncStatus:e.data.status}))}});it(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:ht.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);it(this,"getChildren",async t=>(await this.asyncMsgSender.send({type:ht.wsContextGetChildrenRequest,data:{fileId:t}},1e4)).data.children.map(e=>e.type==="folder"?{...e,children:[],expanded:!1}:{...e}).sort((e,n)=>e.type===n.type?e.name.localeCompare(n.name):e.type==="folder"?-1:1));this.host=t,this.asyncMsgSender=e;const{subscribe:n,set:s,update:o}=kt({sourceFolders:[],sourceTree:[],syncStatus:Dn.done});this.subscribe=n,this.set=s,this.update=o,this.getSourceFolders().then(a=>{this.update(l=>({...l,sourceFolders:a,sourceTree:ie.sourceFoldersToSourceNodes(a)}))})}async expandNode(t){t.children=await this.getChildren(t.fileId),t.expanded=!0,this.update(e=>e)}collapseNode(t){this.update(e=>(t.children=[],t.expanded=!1,e))}toggleNode(t){t.type==="folder"&&t.inclusionState!==jt.excluded&&(t.expanded?this.collapseNode(t):this.expandNode(t))}addMoreSourceFolders(){this.host.postMessage({type:ht.wsContextAddMoreSourceFolders})}removeSourceFolder(t){this.host.postMessage({type:ht.wsContextRemoveSourceFolder,data:t})}requestRefresh(){this.host.postMessage({type:ht.wsContextUserRequestedRefresh})}async updateSourceFolders(t){let e=Fn(this);const n=await this.getRefreshedSourceTree(e.sourceTree,t);this.update(s=>({...s,sourceFolders:t,sourceTree:n}))}async getRefreshedSourceTree(t,e){const n=ie.sourceFoldersToSourceNodes(e);return this.getRefreshedSourceTreeRecurse(t,n)}async getRefreshedSourceTreeRecurse(t,e){const n=new Map(t.map(s=>[JSON.stringify([s.fileId.folderRoot,s.fileId.relPath]),s]));for(let s of e){const o=ie.fileIdToString(s.fileId);if(s.type==="folder"){const a=n.get(o);a&&(s.expanded=a.type==="folder"&&a.expanded,s.expanded&&(s.children=await this.getChildren(s.fileId),s.children=await this.getRefreshedSourceTreeRecurse(a.children,s.children)))}}return e}static fileIdToString(t){return JSON.stringify([t.folderRoot,t.relPath])}static sourceFoldersToSourceNodes(t){return t.filter(e=>!e.isNestedFolder&&!e.isPending).sort((e,n)=>e.name.localeCompare(n.name)).map(e=>({name:e.name,fileId:e.fileId,children:[],expanded:!1,type:"folder",inclusionState:e.inclusionState,reason:"",trackedFileCount:e.trackedFileCount}))}}function ws(r,t,e){const n=r.slice();return n[6]=t[e],n}function xs(r){let t,e;function n(){return r[5](r[6])}return t=new Le({props:{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$slots:{default:[Ai]},$$scope:{ctx:r}}}),t.$on("click",function(){return r[4](r[6])}),t.$on("keyup",function(){he(Ne("Enter",n))&&Ne("Enter",n).apply(this,arguments)}),{c(){_(t.$$.fragment)},m(s,o){C(t,s,o),e=!0},p(s,o){r=s;const a={};512&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a)},i(s){e||(m(t.$$.fragment,s),e=!0)},o(s){$(t.$$.fragment,s),e=!1},d(s){x(t,s)}}}function Ai(r){let t,e;return t=new Io({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Cs(r){let t,e;return t=new Y({props:{size:1,class:"file-count",$$slots:{default:[Ti]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};513&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Ti(r){let t,e=r[6].trackedFileCount.toLocaleString()+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){1&s&&e!==(e=n[6].trackedFileCount.toLocaleString()+"")&&ct(t,e)},d(n){n&&v(t)}}}function _s(r,t){let e,n,s,o,a,l,i,c,d,u,f,p=t[6].name+"",g=(t[6].isPending?"(pending)":t[6].fileId.folderRoot)+"",h=!t[6].isWorkspaceFolder&&xs(t);s=new so({props:{class:"source-folder-v-adjust",icon:t[3](t[6])}});let S=t[6].trackedFileCount&&Cs(t);return{key:r,first:null,c(){e=k("div"),h&&h.c(),n=T(),_(s.$$.fragment),o=T(),a=k("span"),l=I(p),i=T(),c=k("span"),d=I(g),u=T(),S&&S.c(),w(c,"class","folderRoot svelte-1skknri"),w(a,"class","name svelte-1skknri"),w(e,"class","item svelte-1skknri"),gt(e,"workspace-folder",t[6].isWorkspaceFolder),this.first=e},m(P,N){y(P,e,N),h&&h.m(e,null),b(e,n),C(s,e,null),b(e,o),b(e,a),b(a,l),b(a,i),b(a,c),b(c,d),b(e,u),S&&S.m(e,null),f=!0},p(P,N){(t=P)[6].isWorkspaceFolder?h&&(q(),$(h,1,1,()=>{h=null}),B()):h?(h.p(t,N),1&N&&m(h,1)):(h=xs(t),h.c(),m(h,1),h.m(e,n));const M={};1&N&&(M.icon=t[3](t[6])),s.$set(M),(!f||1&N)&&p!==(p=t[6].name+"")&&ct(l,p),(!f||1&N)&&g!==(g=(t[6].isPending?"(pending)":t[6].fileId.folderRoot)+"")&&ct(d,g),t[6].trackedFileCount?S?(S.p(t,N),1&N&&m(S,1)):(S=Cs(t),S.c(),m(S,1),S.m(e,null)):S&&(q(),$(S,1,1,()=>{S=null}),B()),(!f||1&N)&&gt(e,"workspace-folder",t[6].isWorkspaceFolder)},i(P){f||(m(h),m(s.$$.fragment,P),m(S),f=!0)},o(P){$(h),$(s.$$.fragment,P),$(S),f=!1},d(P){P&&v(e),h&&h.d(),x(s),S&&S.d()}}}function Ni(r){let t,e,n,s,o,a,l,i,c=[],d=new Map,u=lt(r[0]);const f=p=>ie.fileIdToString(p[6].fileId);for(let p=0;p<u.length;p+=1){let g=ws(r,u,p),h=f(g);d.set(h,c[p]=_s(h,g))}return s=new sn({}),{c(){t=k("div");for(let p=0;p<c.length;p+=1)c[p].c();e=T(),n=k("div"),_(s.$$.fragment),o=I(" Add more..."),w(n,"role","button"),w(n,"tabindex","0"),w(n,"class","add-more svelte-1skknri"),w(t,"class","source-folder svelte-1skknri")},m(p,g){y(p,t,g);for(let h=0;h<c.length;h+=1)c[h]&&c[h].m(t,null);b(t,e),b(t,n),C(s,n,null),b(n,o),a=!0,l||(i=[It(n,"keyup",function(){he(Ne("Enter",r[1]))&&Ne("Enter",r[1]).apply(this,arguments)}),It(n,"click",function(){he(r[1])&&r[1].apply(this,arguments)})],l=!0)},p(p,[g]){r=p,13&g&&(u=lt(r[0]),q(),c=Ht(c,g,f,1,r,u,d,t,Gt,_s,e,ws),B())},i(p){if(!a){for(let g=0;g<u.length;g+=1)m(c[g]);m(s.$$.fragment,p),a=!0}},o(p){for(let g=0;g<c.length;g+=1)$(c[g]);$(s.$$.fragment,p),a=!1},d(p){p&&v(t);for(let g=0;g<c.length;g+=1)c[g].d();x(s),l=!1,xn(i)}}}function Ei(r,t,e){let{folders:n=[]}=t,{onAddMore:s}=t,{onRemove:o}=t;return r.$$set=a=>{"folders"in a&&e(0,n=a.folders),"onAddMore"in a&&e(1,s=a.onAddMore),"onRemove"in a&&e(2,o=a.onRemove)},[n,s,o,a=>a.isWorkspaceFolder?"root-folder":"folder",a=>o(a.fileId.folderRoot),a=>o(a.fileId.folderRoot)]}class Ii extends ot{constructor(t){super(),at(this,t,Ei,Ni,rt,{folders:0,onAddMore:1,onRemove:2})}}function bs(r,t,e){const n=r.slice();return n[10]=t[e],n}function Ss(r){let t,e;return t=new Y({props:{size:1,class:"file-count",$$slots:{default:[Ri]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};8193&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Ri(r){let t,e=r[0].trackedFileCount.toLocaleString()+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){1&s&&e!==(e=n[0].trackedFileCount.toLocaleString()+"")&&ct(t,e)},d(n){n&&v(t)}}}function ks(r){let t,e,n=[],s=new Map,o=lt(r[5].children);const a=l=>ie.fileIdToString(l[10].fileId);for(let l=0;l<o.length;l+=1){let i=bs(r,o,l),c=a(i);s.set(c,n[l]=Ms(c,i))}return{c(){t=k("div");for(let l=0;l<n.length;l+=1)n[l].c();w(t,"class","children-container")},m(l,i){y(l,t,i);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(t,null);e=!0},p(l,i){38&i&&(o=lt(l[5].children),q(),n=Ht(n,i,a,1,l,o,s,t,Gt,Ms,null,bs),B())},i(l){if(!e){for(let i=0;i<o.length;i+=1)m(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)$(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d()}}}function Ms(r,t){let e,n,s;return n=new fo({props:{data:t[10],wsContextModel:t[1],indentLevel:t[2]+1}}),{key:r,first:null,c(){e=ft(),_(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),C(n,o,a),s=!0},p(o,a){t=o;const l={};32&a&&(l.data=t[10]),2&a&&(l.wsContextModel=t[1]),4&a&&(l.indentLevel=t[2]+1),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(n,o)}}}function Pi(r){let t,e,n,s,o,a,l,i,c,d,u,f,p,g,h,S,P,N,M=r[0].name+"";n=new so({props:{icon:r[4]}});let A=r[0].type===te.folder&&r[0].inclusionState!==jt.excluded&&typeof r[0].trackedFileCount=="number"&&Ss(r),E=r[5]&&ks(r);return{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),o=k("span"),a=I(M),l=T(),A&&A.c(),i=T(),c=k("img"),h=T(),E&&E.c(),w(o,"class","name svelte-sympus"),ls(c.src,d=r[7][r[0].inclusionState])||w(c,"src",d),w(c,"alt",u=r[8][r[0].inclusionState]),w(e,"class","tree-item svelte-sympus"),w(e,"role","treeitem"),w(e,"aria-selected","false"),w(e,"tabindex","0"),w(e,"title",f=r[0].reason),w(e,"aria-expanded",p=r[0].type===te.folder&&r[0].expanded),w(e,"aria-level",r[2]),w(e,"style",g=`padding-left: ${10*r[2]+20}px;`),gt(e,"included-folder",r[3])},m(z,G){y(z,t,G),b(t,e),C(n,e,null),b(e,s),b(e,o),b(o,a),b(e,l),A&&A.m(e,null),b(e,i),b(e,c),b(t,h),E&&E.m(t,null),S=!0,P||(N=[It(e,"click",r[6]),It(e,"keyup",Ne("Enter",r[6]))],P=!0)},p(z,[G]){const tt={};16&G&&(tt.icon=z[4]),n.$set(tt),(!S||1&G)&&M!==(M=z[0].name+"")&&ct(a,M),z[0].type===te.folder&&z[0].inclusionState!==jt.excluded&&typeof z[0].trackedFileCount=="number"?A?(A.p(z,G),1&G&&m(A,1)):(A=Ss(z),A.c(),m(A,1),A.m(e,i)):A&&(q(),$(A,1,1,()=>{A=null}),B()),(!S||1&G&&!ls(c.src,d=z[7][z[0].inclusionState]))&&w(c,"src",d),(!S||1&G&&u!==(u=z[8][z[0].inclusionState]))&&w(c,"alt",u),(!S||1&G&&f!==(f=z[0].reason))&&w(e,"title",f),(!S||1&G&&p!==(p=z[0].type===te.folder&&z[0].expanded))&&w(e,"aria-expanded",p),(!S||4&G)&&w(e,"aria-level",z[2]),(!S||4&G&&g!==(g=`padding-left: ${10*z[2]+20}px;`))&&w(e,"style",g),(!S||8&G)&&gt(e,"included-folder",z[3]),z[5]?E?(E.p(z,G),32&G&&m(E,1)):(E=ks(z),E.c(),m(E,1),E.m(t,null)):E&&(q(),$(E,1,1,()=>{E=null}),B())},i(z){S||(m(n.$$.fragment,z),m(A),m(E),S=!0)},o(z){$(n.$$.fragment,z),$(A),$(E),S=!1},d(z){z&&v(t),x(n),A&&A.d(),E&&E.d(),P=!1,xn(N)}}}function Li(r,t,e){let{data:n}=t,{wsContextModel:s}=t,{indentLevel:o}=t;const a={[jt.included]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",[jt.excluded]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",[jt.partial]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e"},l={[jt.included]:"included",[jt.excluded]:"excluded",[jt.partial]:"partially included"};let i,c,d;return r.$$set=u=>{"data"in u&&e(0,n=u.data),"wsContextModel"in u&&e(1,s=u.wsContextModel),"indentLevel"in u&&e(2,o=u.indentLevel)},r.$$.update=()=>{var u;1&r.$$.dirty&&e(4,c=(u=n).type===te.folder&&u.inclusionState!==jt.excluded?u.expanded?"chevron-down":"chevron-right":u.type===te.folder?"folder":"file"),1&r.$$.dirty&&e(3,i=n.type===te.folder&&n.inclusionState!==jt.excluded),1&r.$$.dirty&&e(5,d=n.type===te.folder&&n.expanded&&n.children&&n.children.length>0?n:null)},[n,s,o,i,c,d,()=>{s.toggleNode(n)},a,l]}class fo extends ot{constructor(t){super(),at(this,t,Li,Pi,rt,{data:0,wsContextModel:1,indentLevel:2})}}function As(r,t,e){const n=r.slice();return n[3]=t[e],n}function Ts(r,t){let e,n,s;return n=new fo({props:{wsContextModel:t[0],data:t[3],indentLevel:0}}),{key:r,first:null,c(){e=ft(),_(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),C(n,o,a),s=!0},p(o,a){t=o;const l={};1&a&&(l.wsContextModel=t[0]),2&a&&(l.data=t[3]),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(n,o)}}}function zi(r){let t,e,n=[],s=new Map,o=lt(r[1]);const a=l=>ie.fileIdToString(l[3].fileId);for(let l=0;l<o.length;l+=1){let i=As(r,o,l),c=a(i);s.set(c,n[l]=Ts(c,i))}return{c(){t=k("div");for(let l=0;l<n.length;l+=1)n[l].c();w(t,"class","files-container svelte-8hfqhl")},m(l,i){y(l,t,i);for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(t,null);e=!0},p(l,[i]){3&i&&(o=lt(l[1]),q(),n=Ht(n,i,a,1,l,o,s,t,Gt,Ts,null,As),B())},i(l){if(!e){for(let i=0;i<o.length;i+=1)m(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)$(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d()}}}function Oi(r,t,e){let n,s=D,o=()=>(s(),s=nn(l,i=>e(2,n=i)),l);r.$$.on_destroy.push(()=>s());let a,{wsContextModel:l}=t;return o(),r.$$set=i=>{"wsContextModel"in i&&o(e(0,l=i.wsContextModel))},r.$$.update=()=>{4&r.$$.dirty&&e(1,a=n.sourceTree)},[l,a,n]}class Zi extends ot{constructor(t){super(),at(this,t,Oi,zi,rt,{wsContextModel:0})}}function Fi(r){let t,e,n;return{c(){t=xt("svg"),e=xt("rect"),n=xt("path"),w(e,"width","16"),w(e,"height","16"),w(e,"transform","matrix(-1 0 0 -1 16 16)"),w(e,"fill","currentColor"),w(e,"fill-opacity","0.01"),w(n,"fill-rule","evenodd"),w(n,"clip-rule","evenodd"),w(n,"d","M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z"),w(n,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 16 16"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,o){y(s,t,o),b(t,e),b(t,n)},p:D,i:D,o:D,d(s){s&&v(t)}}}class ji extends ot{constructor(t){super(),at(this,t,null,Fi,rt,{})}}const Di=r=>({}),Ns=r=>({}),Ui=r=>({}),Es=r=>({});function Vi(r){let t;const e=r[8]["header-left"],n=zt(e,r,r[10],Es);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),t=!0},p(s,o){n&&n.p&&(!t||1024&o)&&Ot(n,e,s,s[10],t?Ft(e,s[10],o,Ui):Zt(s[10]),Es)},i(s){t||(m(n,s),t=!0)},o(s){$(n,s),t=!1},d(s){n&&n.d(s)}}}function qi(r){let t,e,n,s=r[0]&&Is(r),o=r[1]&&Rs(r);return{c(){s&&s.c(),t=T(),o&&o.c(),e=ft()},m(a,l){s&&s.m(a,l),y(a,t,l),o&&o.m(a,l),y(a,e,l),n=!0},p(a,l){a[0]?s?(s.p(a,l),1&l&&m(s,1)):(s=Is(a),s.c(),m(s,1),s.m(t.parentNode,t)):s&&(q(),$(s,1,1,()=>{s=null}),B()),a[1]?o?(o.p(a,l),2&l&&m(o,1)):(o=Rs(a),o.c(),m(o,1),o.m(e.parentNode,e)):o&&(q(),$(o,1,1,()=>{o=null}),B())},i(a){n||(m(s),m(o),n=!0)},o(a){$(s),$(o),n=!1},d(a){a&&(v(t),v(e)),s&&s.d(a),o&&o.d(a)}}}function Is(r){let t,e,n;var s=r[0];return s&&(e=Te(s,{})),{c(){t=k("div"),e&&_(e.$$.fragment),w(t,"class","icon-wrapper svelte-13uht7n")},m(o,a){y(o,t,a),e&&C(e,t,null),n=!0},p(o,a){if(1&a&&s!==(s=o[0])){if(e){q();const l=e;$(l.$$.fragment,1,0,()=>{x(l,1)}),B()}s?(e=Te(s,{}),_(e.$$.fragment),m(e.$$.fragment,1),C(e,t,null)):e=null}},i(o){n||(e&&m(e.$$.fragment,o),n=!0)},o(o){e&&$(e.$$.fragment,o),n=!1},d(o){o&&v(t),e&&x(e)}}}function Rs(r){let t,e;return t=new Y({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Bi]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};1026&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Bi(r){let t;return{c(){t=I(r[1])},m(e,n){y(e,t,n)},p(e,n){2&n&&ct(t,e[1])},d(e){e&&v(t)}}}function Ps(r){let t,e;const n=r[8].default,s=zt(n,r,r[10],null);return{c(){t=k("div"),s&&s.c(),w(t,"class","settings-card-body")},m(o,a){y(o,t,a),s&&s.m(t,null),e=!0},p(o,a){s&&s.p&&(!e||1024&a)&&Ot(s,n,o,o[10],e?Ft(n,o[10],a,null):Zt(o[10]),null)},i(o){e||(m(s,o),e=!0)},o(o){$(s,o),e=!1},d(o){o&&v(t),s&&s.d(o)}}}function Hi(r){let t,e,n,s,o,a,l,i,c,d,u;const f=[qi,Vi],p=[];function g(A,E){return A[0]||A[1]?0:1}s=g(r),o=p[s]=f[s](r);const h=r[8]["header-right"],S=zt(h,r,r[10],Ns);let P=r[5].default&&Ps(r),N=[{role:"button"},{class:r[3]},r[4]],M={};for(let A=0;A<N.length;A+=1)M=Mt(M,N[A]);return{c(){t=k("div"),e=k("div"),n=k("div"),o.c(),a=T(),l=k("div"),S&&S.c(),i=T(),P&&P.c(),w(n,"class","settings-card-left svelte-13uht7n"),w(l,"class","settings-card-right svelte-13uht7n"),w(e,"class","settings-card-content svelte-13uht7n"),cs(t,M),gt(t,"clickable",r[2]),gt(t,"svelte-13uht7n",!0)},m(A,E){y(A,t,E),b(t,e),b(e,n),p[s].m(n,null),b(e,a),b(e,l),S&&S.m(l,null),b(t,i),P&&P.m(t,null),c=!0,d||(u=It(t,"click",r[9]),d=!0)},p(A,[E]){let z=s;s=g(A),s===z?p[s].p(A,E):(q(),$(p[z],1,1,()=>{p[z]=null}),B(),o=p[s],o?o.p(A,E):(o=p[s]=f[s](A),o.c()),m(o,1),o.m(n,null)),S&&S.p&&(!c||1024&E)&&Ot(S,h,A,A[10],c?Ft(h,A[10],E,Di):Zt(A[10]),Ns),A[5].default?P?(P.p(A,E),32&E&&m(P,1)):(P=Ps(A),P.c(),m(P,1),P.m(t,null)):P&&(q(),$(P,1,1,()=>{P=null}),B()),cs(t,M=ve(N,[{role:"button"},(!c||8&E)&&{class:A[3]},16&E&&A[4]])),gt(t,"clickable",A[2]),gt(t,"svelte-13uht7n",!0)},i(A){c||(m(o),m(S,A),m(P),c=!0)},o(A){$(o),$(S,A),$(P),c=!1},d(A){A&&v(t),p[s].d(),S&&S.d(A),P&&P.d(),d=!1,u()}}}function Gi(r,t,e){let n,s,o;const a=["class","icon","title","isClickable"];let l=ds(t,a),{$$slots:i={},$$scope:c}=t;const d=Mo(i);let{class:u=""}=t,{icon:f}=t,{title:p}=t,{isClickable:g=!1}=t;return r.$$set=h=>{t=Mt(Mt({},t),se(h)),e(11,l=ds(t,a)),"class"in h&&e(6,u=h.class),"icon"in h&&e(0,f=h.icon),"title"in h&&e(1,p=h.title),"isClickable"in h&&e(2,g=h.isClickable),"$$scope"in h&&e(10,c=h.$$scope)},r.$$.update=()=>{e(7,{class:n,...s}=l,n,(e(4,s),e(11,l))),192&r.$$.dirty&&e(3,o=`settings-card ${u} ${n||""}`)},[f,p,g,o,s,d,u,n,i,function(h){Ao.call(this,r,h)},c]}class Kt extends ot{constructor(t){super(),at(this,t,Gi,Hi,rt,{class:6,icon:0,title:1,isClickable:2})}}function Ji(r){let t;return{c(){t=I("SOURCE FOLDERS")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Wi(r){let t;return{c(){t=I("FILES")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Ki(r){let t,e=r[2].toLocaleString()+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){4&s&&e!==(e=n[2].toLocaleString()+"")&&ct(t,e)},d(n){n&&v(t)}}}function Yi(r){let t,e,n,s,o,a,l,i,c,d,u,f,p,g;return n=new Y({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Ji]},$$scope:{ctx:r}}}),o=new Ii({props:{folders:r[0],onRemove:r[7],onAddMore:r[8]}}),c=new Y({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Wi]},$$scope:{ctx:r}}}),u=new Y({props:{size:1,class:"file-count",$$slots:{default:[Ki]},$$scope:{ctx:r}}}),p=new Zi({props:{wsContextModel:r[3]}}),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),_(o.$$.fragment),a=T(),l=k("div"),i=k("div"),_(c.$$.fragment),d=T(),_(u.$$.fragment),f=T(),_(p.$$.fragment),w(i,"class","files-header svelte-qsnirf"),w(t,"class","context-list svelte-qsnirf")},m(h,S){y(h,t,S),b(t,e),C(n,e,null),b(e,s),C(o,e,null),b(t,a),b(t,l),b(l,i),C(c,i,null),b(i,d),C(u,i,null),b(l,f),C(p,l,null),g=!0},p(h,S){const P={};512&S&&(P.$$scope={dirty:S,ctx:h}),n.$set(P);const N={};1&S&&(N.folders=h[0]),o.$set(N);const M={};512&S&&(M.$$scope={dirty:S,ctx:h}),c.$set(M);const A={};516&S&&(A.$$scope={dirty:S,ctx:h}),u.$set(A)},i(h){g||(m(n.$$.fragment,h),m(o.$$.fragment,h),m(c.$$.fragment,h),m(u.$$.fragment,h),m(p.$$.fragment,h),g=!0)},o(h){$(n.$$.fragment,h),$(o.$$.fragment,h),$(c.$$.fragment,h),$(u.$$.fragment,h),$(p.$$.fragment,h),g=!1},d(h){h&&v(t),x(n),x(o),x(c),x(u),x(p)}}}function Ls(r){let t,e;return t=new Le({props:{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[Xi]},$$scope:{ctx:r}}}),t.$on("click",r[5]),t.$on("keyup",Ne("Enter",r[6])),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};512&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Xi(r){let t,e;return t=new Vo({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Qi(r){let t,e,n=r[1]===Dn.done&&Ls(r);return{c(){t=k("div"),n&&n.c(),w(t,"slot","header-right")},m(s,o){y(s,t,o),n&&n.m(t,null),e=!0},p(s,o){s[1]===Dn.done?n?(n.p(s,o),2&o&&m(n,1)):(n=Ls(s),n.c(),m(n,1),n.m(t,null)):n&&(q(),$(n,1,1,()=>{n=null}),B())},i(s){e||(m(n),e=!0)},o(s){$(n),e=!1},d(s){s&&v(t),n&&n.d()}}}function tl(r){let t,e,n,s;return t=new Kt({props:{icon:ji,title:"Context",$$slots:{"header-right":[Qi],default:[Yi]},$$scope:{ctx:r}}}),t.$on("contextmenu",el),{c(){_(t.$$.fragment)},m(o,a){C(t,o,a),e=!0,n||(s=It(window,"message",r[3].handleMessageFromExtension),n=!0)},p(o,[a]){const l={};519&a&&(l.$$scope={dirty:a,ctx:o}),t.$set(l)},i(o){e||(m(t.$$.fragment,o),e=!0)},o(o){$(t.$$.fragment,o),e=!1},d(o){x(t,o),n=!1,s()}}}const el=r=>r.preventDefault();function nl(r,t,e){let n,s,o,a,l=new ie(St,new sa(St.postMessage));return Et(r,l,i=>e(4,s=i)),r.$$.update=()=>{16&r.$$.dirty&&e(0,o=s.sourceFolders.sort((i,c)=>i.isWorkspaceFolder!==c.isWorkspaceFolder?i.isWorkspaceFolder?-1:1:i.fileId.folderRoot.localeCompare(c.fileId.folderRoot))),16&r.$$.dirty&&e(1,a=s.syncStatus),1&r.$$.dirty&&e(2,n=o.reduce((i,c)=>i+(c.trackedFileCount??0),0))},[o,a,n,l,s,()=>l.requestRefresh(),()=>l.requestRefresh(),i=>l.removeSourceFolder(i),()=>l.addMoreSourceFolders()]}class sl extends ot{constructor(t){super(),at(this,t,nl,tl,rt,{})}}function $o(r){return function(t){switch(typeof t){case"object":return t!=null;case"function":return!0;default:return!1}}(r)&&"name"in r}function zs(r){return $o(r)&&"component"in r}function rl(r){let t,e;return{c(){t=xt("svg"),e=xt("path"),w(e,"d","M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z"),w(e,"fill","currentColor"),w(t,"width","16"),w(t,"height","15"),w(t,"viewBox","0 0 16 15"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,t,s),b(t,e)},p:D,i:D,o:D,d(n){n&&v(t)}}}class go extends ot{constructor(t){super(),at(this,t,null,rl,rt,{})}}const ol=r=>({item:1&r}),Os=r=>({item:r[0]}),al=r=>({}),Zs=r=>({});function Fs(r){var c;let t,e,n,s,o;t=new Y({props:{size:4,weight:"medium",color:"neutral",$$slots:{default:[il]},$$scope:{ctx:r}}});let a=((c=r[0])==null?void 0:c.description)&&js(r);const l=r[1].content,i=zt(l,r,r[2],Os);return{c(){_(t.$$.fragment),e=T(),a&&a.c(),n=T(),s=k("div"),i&&i.c(),w(s,"class","c-navigation__content-container svelte-z0ijuz")},m(d,u){C(t,d,u),y(d,e,u),a&&a.m(d,u),y(d,n,u),y(d,s,u),i&&i.m(s,null),o=!0},p(d,u){var p;const f={};5&u&&(f.$$scope={dirty:u,ctx:d}),t.$set(f),(p=d[0])!=null&&p.description?a?(a.p(d,u),1&u&&m(a,1)):(a=js(d),a.c(),m(a,1),a.m(n.parentNode,n)):a&&(q(),$(a,1,1,()=>{a=null}),B()),i&&i.p&&(!o||5&u)&&Ot(i,l,d,d[2],o?Ft(l,d[2],u,ol):Zt(d[2]),Os)},i(d){o||(m(t.$$.fragment,d),m(a),m(i,d),o=!0)},o(d){$(t.$$.fragment,d),$(a),$(i,d),o=!1},d(d){d&&(v(e),v(n),v(s)),x(t,d),a&&a.d(d),i&&i.d(d)}}}function il(r){var s;let t,e,n=((s=r[0])==null?void 0:s.name)+"";return{c(){t=k("div"),e=I(n),w(t,"class","c-navigation__content-header svelte-z0ijuz")},m(o,a){y(o,t,a),b(t,e)},p(o,a){var l;1&a&&n!==(n=((l=o[0])==null?void 0:l.name)+"")&&ct(e,n)},d(o){o&&v(t)}}}function js(r){let t,e;return t=new Y({props:{color:"secondary",size:1,weight:"light",$$slots:{default:[ll]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};5&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function ll(r){var s;let t,e,n=((s=r[0])==null?void 0:s.description)+"";return{c(){t=k("div"),e=I(n),w(t,"class","c-navigation__content-description svelte-z0ijuz")},m(o,a){y(o,t,a),b(t,e)},p(o,a){var l;1&a&&n!==(n=((l=o[0])==null?void 0:l.description)+"")&&ct(e,n)},d(o){o&&v(t)}}}function cl(r){let t,e,n,s,o;const a=r[1].header,l=zt(a,r,r[2],Zs);let i=r[0]!=null&&Fs(r);return{c(){var c;t=k("div"),l&&l.c(),e=T(),n=k("div"),i&&i.c(),w(t,"class","c-navigation__content svelte-z0ijuz"),w(t,"id",s=(c=r[0])==null?void 0:c.id)},m(c,d){y(c,t,d),l&&l.m(t,null),b(t,e),b(t,n),i&&i.m(n,null),o=!0},p(c,[d]){var u;l&&l.p&&(!o||4&d)&&Ot(l,a,c,c[2],o?Ft(a,c[2],d,al):Zt(c[2]),Zs),c[0]!=null?i?(i.p(c,d),1&d&&m(i,1)):(i=Fs(c),i.c(),m(i,1),i.m(n,null)):i&&(q(),$(i,1,1,()=>{i=null}),B()),(!o||1&d&&s!==(s=(u=c[0])==null?void 0:u.id))&&w(t,"id",s)},i(c){o||(m(l,c),m(i),o=!0)},o(c){$(l,c),$(i),o=!1},d(c){c&&v(t),l&&l.d(c),i&&i.d()}}}function dl(r,t,e){let{$$slots:n={},$$scope:s}=t,{item:o}=t;return r.$$set=a=>{"item"in a&&e(0,o=a.item),"$$scope"in a&&e(2,s=a.$$scope)},[o,n,s]}class ho extends ot{constructor(t){super(),at(this,t,dl,cl,rt,{item:0})}}function ul(r,t){let e;function n({scrollTo:s,delay:o,options:a}){clearTimeout(e),s&&(e=setTimeout(()=>{r.scrollIntoView(a)},o))}return n(t),{update:n,destroy(){clearTimeout(e)}}}function Ds(r,t,e){const n=r.slice();return n[13]=t[e][0],n[14]=t[e][1],n}function Us(r,t,e){const n=r.slice();return n[22]=t[e],n}const pl=r=>({item:32&r}),Vs=r=>({slot:"content",item:r[22]}),ml=r=>({label:32&r,mode:4&r}),qs=r=>({label:r[13],mode:r[2]}),fl=r=>({item:1&r}),Bs=r=>({item:r[0]});function Hs(r,t,e){const n=r.slice();return n[13]=t[e][0],n[14]=t[e][1],n}function Gs(r,t,e){const n=r.slice();return n[17]=t[e],n}const $l=r=>({label:32&r,mode:4&r}),Js=r=>({label:r[13],mode:r[2]}),gl=r=>({item:1&r,selectedId:2&r}),Ws=r=>({slot:"header",item:r[0],selectedId:r[1]}),hl=r=>({item:1&r,isSelected:3&r}),Ks=r=>{var t;return{slot:"content",item:r[0],isSelected:((t=r[0])==null?void 0:t.id)===r[1]}};function vl(r){let t,e,n;const s=r[10].header,o=zt(s,r,r[12],Bs);let a=lt(r[5]),l=[];for(let c=0;c<a.length;c+=1)l[c]=Xs(Ds(r,a,c));const i=c=>$(l[c],1,1,()=>{l[c]=null});return{c(){t=k("div"),o&&o.c(),e=T();for(let c=0;c<l.length;c+=1)l[c].c();w(t,"class","c-navigation__flat svelte-n5ccbo")},m(c,d){y(c,t,d),o&&o.m(t,null),b(t,e);for(let u=0;u<l.length;u+=1)l[u]&&l[u].m(t,null);n=!0},p(c,d){if(o&&o.p&&(!n||4097&d)&&Ot(o,s,c,c[12],n?Ft(s,c[12],d,fl):Zt(c[12]),Bs),4134&d){let u;for(a=lt(c[5]),u=0;u<a.length;u+=1){const f=Ds(c,a,u);l[u]?(l[u].p(f,d),m(l[u],1)):(l[u]=Xs(f),l[u].c(),m(l[u],1),l[u].m(t,null))}for(q(),u=a.length;u<l.length;u+=1)i(u);B()}},i(c){if(!n){m(o,c);for(let d=0;d<a.length;d+=1)m(l[d]);n=!0}},o(c){$(o,c),l=l.filter(Boolean);for(let d=0;d<l.length;d+=1)$(l[d]);n=!1},d(c){c&&v(t),o&&o.d(c),me(l,c)}}}function yl(r){let t,e;return t=new ra({props:{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,showButton:r[3],minimized:!1,$$slots:{right:[Ml],left:[bl]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};8&s&&(o.showButton=n[3]),4135&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function wl(r){let t,e,n,s,o,a,l=r[13]+"";return e=new go({}),{c(){t=k("span"),_(e.$$.fragment),n=T(),s=k("span"),o=I(l),w(t,"class","c-navigation__head-icon")},m(i,c){y(i,t,c),C(e,t,null),y(i,n,c),y(i,s,c),b(s,o),a=!0},p(i,c){(!a||32&c)&&l!==(l=i[13]+"")&&ct(o,l)},i(i){a||(m(e.$$.fragment,i),a=!0)},o(i){$(e.$$.fragment,i),a=!1},d(i){i&&(v(t),v(n),v(s)),x(e)}}}function xl(r){let t;const e=r[10].content,n=zt(e,r,r[12],Vs);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),t=!0},p(s,o){n&&n.p&&(!t||4128&o)&&Ot(n,e,s,s[12],t?Ft(e,s[12],o,pl):Zt(s[12]),Vs)},i(s){t||(m(n,s),t=!0)},o(s){$(n,s),t=!1},d(s){n&&n.d(s)}}}function Ys(r){let t,e,n,s,o,a,l;return e=new ho({props:{item:r[22],$$slots:{content:[xl]},$$scope:{ctx:r}}}),{c(){t=k("span"),_(e.$$.fragment),n=T()},m(i,c){y(i,t,c),C(e,t,null),b(t,n),o=!0,a||(l=To(s=ul.call(null,t,{scrollTo:r[2]==="flat"&&r[22].id===r[1],delay:300,options:{behavior:"smooth"}})),a=!0)},p(i,c){r=i;const d={};32&c&&(d.item=r[22]),4128&c&&(d.$$scope={dirty:c,ctx:r}),e.$set(d),s&&he(s.update)&&38&c&&s.update.call(null,{scrollTo:r[2]==="flat"&&r[22].id===r[1],delay:300,options:{behavior:"smooth"}})},i(i){o||(m(e.$$.fragment,i),o=!0)},o(i){$(e.$$.fragment,i),o=!1},d(i){i&&v(t),x(e),a=!1,l()}}}function Xs(r){let t,e,n,s;const o=r[10].group,a=zt(o,r,r[12],qs),l=a||function(u){let f,p;return f=new Y({props:{color:"secondary",size:2,weight:"medium",$$slots:{default:[wl]},$$scope:{ctx:u}}}),{c(){_(f.$$.fragment)},m(g,h){C(f,g,h),p=!0},p(g,h){const S={};4128&h&&(S.$$scope={dirty:h,ctx:g}),f.$set(S)},i(g){p||(m(f.$$.fragment,g),p=!0)},o(g){$(f.$$.fragment,g),p=!1},d(g){x(f,g)}}}(r);let i=lt(r[14]),c=[];for(let u=0;u<i.length;u+=1)c[u]=Ys(Us(r,i,u));const d=u=>$(c[u],1,1,()=>{c[u]=null});return{c(){t=k("div"),l&&l.c(),e=T();for(let u=0;u<c.length;u+=1)c[u].c();n=ft(),w(t,"class","c-navigation__head svelte-n5ccbo")},m(u,f){y(u,t,f),l&&l.m(t,null),y(u,e,f);for(let p=0;p<c.length;p+=1)c[p]&&c[p].m(u,f);y(u,n,f),s=!0},p(u,f){if(a?a.p&&(!s||4132&f)&&Ot(a,o,u,u[12],s?Ft(o,u[12],f,ml):Zt(u[12]),qs):l&&l.p&&(!s||32&f)&&l.p(u,s?f:-1),4134&f){let p;for(i=lt(u[14]),p=0;p<i.length;p+=1){const g=Us(u,i,p);c[p]?(c[p].p(g,f),m(c[p],1)):(c[p]=Ys(g),c[p].c(),m(c[p],1),c[p].m(n.parentNode,n))}for(q(),p=i.length;p<c.length;p+=1)d(p);B()}},i(u){if(!s){m(l,u);for(let f=0;f<i.length;f+=1)m(c[f]);s=!0}},o(u){$(l,u),c=c.filter(Boolean);for(let f=0;f<c.length;f+=1)$(c[f]);s=!1},d(u){u&&(v(t),v(e),v(n)),l&&l.d(u),me(c,u)}}}function Cl(r){let t,e=r[13]+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){32&s&&e!==(e=n[13]+"")&&ct(t,e)},d(n){n&&v(t)}}}function _l(r){let t,e,n,s,o,a=r[17].name+"";var l=r[17].icon;return l&&(e=Te(l,{})),{c(){t=k("span"),e&&_(e.$$.fragment),n=T(),s=I(a),w(t,"class","c-navigation__head-icon")},m(i,c){y(i,t,c),e&&C(e,t,null),y(i,n,c),y(i,s,c),o=!0},p(i,c){if(32&c&&l!==(l=i[17].icon)){if(e){q();const d=e;$(d.$$.fragment,1,0,()=>{x(d,1)}),B()}l?(e=Te(l,{}),_(e.$$.fragment),m(e.$$.fragment,1),C(e,t,null)):e=null}(!o||32&c)&&a!==(a=i[17].name+"")&&ct(s,a)},i(i){o||(e&&m(e.$$.fragment,i),o=!0)},o(i){e&&$(e.$$.fragment,i),o=!1},d(i){i&&(v(t),v(n),v(s)),e&&x(e)}}}function Qs(r){let t,e,n,s,o,a;function l(){return r[11](r[17])}return e=new Y({props:{size:2,weight:"regular",color:"primary",$$slots:{default:[_l]},$$scope:{ctx:r}}}),{c(){t=k("button"),_(e.$$.fragment),n=T(),w(t,"class","c-navigation__item svelte-n5ccbo"),gt(t,"is-active",r[17].id===r[1])},m(i,c){y(i,t,c),C(e,t,null),b(t,n),s=!0,o||(a=It(t,"click",l),o=!0)},p(i,c){r=i;const d={};4128&c&&(d.$$scope={dirty:c,ctx:r}),e.$set(d),(!s||34&c)&&gt(t,"is-active",r[17].id===r[1])},i(i){s||(m(e.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),s=!1},d(i){i&&v(t),x(e),o=!1,a()}}}function tr(r){let t,e,n,s,o;const a=r[10].group,l=zt(a,r,r[12],Js),i=l||function(f){let p,g,h,S,P;return g=new go({}),S=new Y({props:{size:2,color:"primary",$$slots:{default:[Cl]},$$scope:{ctx:f}}}),{c(){p=k("div"),_(g.$$.fragment),h=T(),_(S.$$.fragment),w(p,"class","c-navigation__head svelte-n5ccbo")},m(N,M){y(N,p,M),C(g,p,null),b(p,h),C(S,p,null),P=!0},p(N,M){const A={};4128&M&&(A.$$scope={dirty:M,ctx:N}),S.$set(A)},i(N){P||(m(g.$$.fragment,N),m(S.$$.fragment,N),P=!0)},o(N){$(g.$$.fragment,N),$(S.$$.fragment,N),P=!1},d(N){N&&v(p),x(g),x(S)}}}(r);let c=lt(r[14]),d=[];for(let f=0;f<c.length;f+=1)d[f]=Qs(Gs(r,c,f));const u=f=>$(d[f],1,1,()=>{d[f]=null});return{c(){t=k("div"),i&&i.c(),e=T(),n=k("div");for(let f=0;f<d.length;f+=1)d[f].c();s=T(),w(n,"class","c-navigation__items svelte-n5ccbo"),w(t,"class","c-navigation__group")},m(f,p){y(f,t,p),i&&i.m(t,null),b(t,e),b(t,n);for(let g=0;g<d.length;g+=1)d[g]&&d[g].m(n,null);b(t,s),o=!0},p(f,p){if(l?l.p&&(!o||4132&p)&&Ot(l,a,f,f[12],o?Ft(a,f[12],p,$l):Zt(f[12]),Js):i&&i.p&&(!o||32&p)&&i.p(f,o?p:-1),98&p){let g;for(c=lt(f[14]),g=0;g<c.length;g+=1){const h=Gs(f,c,g);d[g]?(d[g].p(h,p),m(d[g],1)):(d[g]=Qs(h),d[g].c(),m(d[g],1),d[g].m(n,null))}for(q(),g=c.length;g<d.length;g+=1)u(g);B()}},i(f){if(!o){m(i,f);for(let p=0;p<c.length;p+=1)m(d[p]);o=!0}},o(f){$(i,f),d=d.filter(Boolean);for(let p=0;p<d.length;p+=1)$(d[p]);o=!1},d(f){f&&v(t),i&&i.d(f),me(d,f)}}}function er(r){let t,e,n=lt(r[5]),s=[];for(let a=0;a<n.length;a+=1)s[a]=tr(Hs(r,n,a));const o=a=>$(s[a],1,1,()=>{s[a]=null});return{c(){for(let a=0;a<s.length;a+=1)s[a].c();t=ft()},m(a,l){for(let i=0;i<s.length;i+=1)s[i]&&s[i].m(a,l);y(a,t,l),e=!0},p(a,l){if(4198&l){let i;for(n=lt(a[5]),i=0;i<n.length;i+=1){const c=Hs(a,n,i);s[i]?(s[i].p(c,l),m(s[i],1)):(s[i]=tr(c),s[i].c(),m(s[i],1),s[i].m(t.parentNode,t))}for(q(),i=n.length;i<s.length;i+=1)o(i);B()}},i(a){if(!e){for(let l=0;l<n.length;l+=1)m(s[l]);e=!0}},o(a){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)$(s[l]);e=!1},d(a){a&&v(t),me(s,a)}}}function bl(r){let t,e,n=r[1],s=er(r);return{c(){t=k("nav"),s.c(),w(t,"class","c-navigation__nav svelte-n5ccbo"),w(t,"slot","left")},m(o,a){y(o,t,a),s.m(t,null),e=!0},p(o,a){2&a&&rt(n,n=o[1])?(q(),$(s,1,1,D),B(),s=er(o),s.c(),m(s,1),s.m(t,null)):s.p(o,a)},i(o){e||(m(s),e=!0)},o(o){$(s),e=!1},d(o){o&&v(t),s.d(o)}}}function Sl(r){let t;const e=r[10].header,n=zt(e,r,r[12],Ws);return{c(){n&&n.c()},m(s,o){n&&n.m(s,o),t=!0},p(s,o){n&&n.p&&(!t||4099&o)&&Ot(n,e,s,s[12],t?Ft(e,s[12],o,gl):Zt(s[12]),Ws)},i(s){t||(m(n,s),t=!0)},o(s){$(n,s),t=!1},d(s){n&&n.d(s)}}}function nr(r){let t,e,n;const s=[r[0].props];var o=r[0].component;function a(l,i){let c={};for(let d=0;d<s.length;d+=1)c=Mt(c,s[d]);return i!==void 0&&1&i&&(c=Mt(c,ve(s,[us(l[0].props)]))),{props:c}}return o&&(t=Te(o,a(r))),{c(){t&&_(t.$$.fragment),e=ft()},m(l,i){t&&C(t,l,i),y(l,e,i),n=!0},p(l,i){if(1&i&&o!==(o=l[0].component)){if(t){q();const c=t;$(c.$$.fragment,1,0,()=>{x(c,1)}),B()}o?(t=Te(o,a(l,i)),_(t.$$.fragment),m(t.$$.fragment,1),C(t,e.parentNode,e)):t=null}else if(o){const c=1&i?ve(s,[us(l[0].props)]):{};t.$set(c)}},i(l){n||(t&&m(t.$$.fragment,l),n=!0)},o(l){t&&$(t.$$.fragment,l),n=!1},d(l){l&&v(e),t&&x(t,l)}}}function kl(r){let t;const e=r[10].content,n=zt(e,r,r[12],Ks),s=n||function(o){let a,l,i=zs(o[0])&&sr(o[0],o[2],o[1]),c=i&&nr(o);return{c(){c&&c.c(),a=ft()},m(d,u){c&&c.m(d,u),y(d,a,u),l=!0},p(d,u){7&u&&(i=zs(d[0])&&sr(d[0],d[2],d[1])),i?c?(c.p(d,u),7&u&&m(c,1)):(c=nr(d),c.c(),m(c,1),c.m(a.parentNode,a)):c&&(q(),$(c,1,1,()=>{c=null}),B())},i(d){l||(m(c),l=!0)},o(d){$(c),l=!1},d(d){d&&v(a),c&&c.d(d)}}}(r);return{c(){s&&s.c()},m(o,a){s&&s.m(o,a),t=!0},p(o,a){n?n.p&&(!t||4099&a)&&Ot(n,e,o,o[12],t?Ft(e,o[12],a,hl):Zt(o[12]),Ks):s&&s.p&&(!t||7&a)&&s.p(o,t?a:-1)},i(o){t||(m(s,o),t=!0)},o(o){$(s,o),t=!1},d(o){s&&s.d(o)}}}function Ml(r){let t,e;return t=new ho({props:{item:r[0],slot:"right",$$slots:{content:[kl],header:[Sl]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};1&s&&(o.item=n[0]),4103&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Al(r){let t,e,n,s,o;const a=[yl,vl],l=[];function i(c,d){return c[2]==="tree"?0:1}return e=i(r),n=l[e]=a[e](r),{c(){t=k("div"),n.c(),w(t,"class",s="c-navigation c-navigation--mode__"+r[2]+" "+r[4]+" svelte-n5ccbo")},m(c,d){y(c,t,d),l[e].m(t,null),o=!0},p(c,[d]){let u=e;e=i(c),e===u?l[e].p(c,d):(q(),$(l[u],1,1,()=>{l[u]=null}),B(),n=l[e],n?n.p(c,d):(n=l[e]=a[e](c),n.c()),m(n,1),n.m(t,null)),(!o||20&d&&s!==(s="c-navigation c-navigation--mode__"+c[2]+" "+c[4]+" svelte-n5ccbo"))&&w(t,"class",s)},i(c){o||(m(n),o=!0)},o(c){$(n),o=!1},d(c){c&&v(t),l[e].d()}}}function un(r,t,e,n,s,o){return{name:r,description:t,icon:e,id:n}}function sr(r,t,e){return t!=="tree"||(r==null?void 0:r.id)===e}function Tl(r,t,e){let{$$slots:n={},$$scope:s}=t,{group:o="Workspace Settings"}=t,{items:a=[]}=t,{item:l}=t,{mode:i="tree"}=t,{selectedId:c}=t,{onNavigationChangeItem:d=h=>{}}=t,{showButton:u=!0}=t,{class:f=""}=t,p=new Map;function g(h){e(0,l=h),e(1,c=h==null?void 0:h.id)}return r.$$set=h=>{"group"in h&&e(7,o=h.group),"items"in h&&e(8,a=h.items),"item"in h&&e(0,l=h.item),"mode"in h&&e(2,i=h.mode),"selectedId"in h&&e(1,c=h.selectedId),"onNavigationChangeItem"in h&&e(9,d=h.onNavigationChangeItem),"showButton"in h&&e(3,u=h.showButton),"class"in h&&e(4,f=h.class),"$$scope"in h&&e(12,s=h.$$scope)},r.$$.update=()=>{259&r.$$.dirty&&(c?e(0,l=a.find(h=>(h==null?void 0:h.id)===c)):e(1,c=l==null?void 0:l.id)),384&r.$$.dirty&&e(5,p=a.reduce((h,S)=>{if(!S)return h;const P=S.group??o,N=h.get(P)??[];return N.push(S),h.set(P,N),h},new Map)),257&r.$$.dirty&&(l||e(0,l=a[0])),514&r.$$.dirty&&d(c)},[l,c,i,u,f,p,g,o,a,d,n,h=>g(h),s]}class Nl extends ot{constructor(t){super(),at(this,t,Tl,Al,rt,{group:7,items:8,item:0,mode:2,selectedId:1,onNavigationChangeItem:9,showButton:3,class:4})}}function El(r){let t,e;return{c(){t=xt("svg"),e=xt("path"),w(e,"d","M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z"),w(e,"fill","currentColor"),w(t,"width","16"),w(t,"height","16"),w(t,"viewBox","0 0 16 16"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,t,s),b(t,e)},p:D,i:D,o:D,d(n){n&&v(t)}}}class Il extends ot{constructor(t){super(),at(this,t,null,El,rt,{})}}function Rl(r){let t,e;return{c(){t=xt("svg"),e=xt("path"),w(e,"d","M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z"),w(e,"fill","currentColor"),w(t,"width","16"),w(t,"height","16"),w(t,"viewBox","0 0 16 16"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(n,s){y(n,t,s),b(t,e)},p:D,i:D,o:D,d(n){n&&v(t)}}}class Pl extends ot{constructor(t){super(),at(this,t,null,Rl,rt,{})}}function Ll(r){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=Mt(s,n[o]);return{c(){t=xt("svg"),e=new kn(!0),this.h()},l(o){t=_n(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=bn(t);e=Sn(a,!0),a.forEach(v),this.h()},h(){e.a=null,le(t,s)},m(o,a){Cn(o,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.57.57 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.57.57 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.57.57 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.57.57 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.57.57 0 0 1-.06-.734zm3.759-3.759a.57.57 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.57.57 0 0 1-.804 0L7.31 4.204a.57.57 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',t)},p(o,[a]){le(t,s=ve(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},1&a&&o[0]]))},i:D,o:D,d(o){o&&v(t)}}}function zl(r,t,e){return r.$$set=n=>{e(0,t=Mt(Mt({},t),se(n)))},[t=se(t)]}class vo extends ot{constructor(t){super(),at(this,t,zl,Ll,rt,{})}}function Ol(r){let t,e,n,s,o,a,l,i;return o=new ee({props:{triggerOn:[Kn.Hover],content:"Revoke Access",$$slots:{default:[jl]},$$scope:{ctx:r}}}),l=new Jn.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Dl]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),n=k("div"),s=k("div"),_(o.$$.fragment),a=T(),_(l.$$.fragment),w(s,"class","icon-button-wrapper svelte-js5lik"),gt(s,"active",r[3]),w(n,"class","connection-status svelte-js5lik"),w(e,"class","icon-container svelte-js5lik"),w(t,"class","status-controls svelte-js5lik")},m(c,d){y(c,t,d),b(t,e),b(e,n),b(n,s),C(o,s,null),b(e,a),C(l,e,null),i=!0},p(c,d){const u={};2051&d&&(u.$$scope={dirty:d,ctx:c}),o.$set(u),(!i||8&d)&&gt(s,"active",c[3]);const f={};2048&d&&(f.$$scope={dirty:d,ctx:c}),l.$set(f)},i(c){i||(m(o.$$.fragment,c),m(l.$$.fragment,c),i=!0)},o(c){$(o.$$.fragment,c),$(l.$$.fragment,c),i=!1},d(c){c&&v(t),x(o),x(l)}}}function Zl(r){let t,e;return t=new vt({props:{variant:"ghost-block",color:r[2]?"neutral":"accent",size:1,$$slots:{default:[ql]},$$scope:{ctx:r}}}),t.$on("click",r[4]),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};4&s&&(o.color=n[2]?"neutral":"accent"),2052&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Fl(r){let t,e;return t=new vo({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function jl(r){let t,e;return t=new Le({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[Fl]},$$scope:{ctx:r}}}),t.$on("click",r[8]),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};2048&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Dl(r){let t;return{c(){t=I("Connected")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Ul(r){let t;return{c(){t=k("span"),t.textContent="Connect"},m(e,n){y(e,t,n)},i:D,o:D,d(e){e&&v(t)}}}function Vl(r){let t,e,n,s,o;return e=new Hn({props:{size:1,useCurrentColor:!0}}),{c(){t=k("div"),_(e.$$.fragment),n=T(),s=k("span"),s.textContent="Cancel",w(t,"class","connect-button-spinner svelte-js5lik")},m(a,l){y(a,t,l),C(e,t,null),y(a,n,l),y(a,s,l),o=!0},i(a){o||(m(e.$$.fragment,a),o=!0)},o(a){$(e.$$.fragment,a),o=!1},d(a){a&&(v(t),v(n),v(s)),x(e)}}}function ql(r){let t,e,n,s;const o=[Vl,Ul],a=[];function l(i,c){return i[2]?0:1}return e=l(r),n=a[e]=o[e](r),{c(){t=k("div"),n.c(),w(t,"class","connect-button-content svelte-js5lik")},m(i,c){y(i,t,c),a[e].m(t,null),s=!0},p(i,c){let d=e;e=l(i),e!==d&&(q(),$(a[d],1,1,()=>{a[d]=null}),B(),n=a[e],n||(n=a[e]=o[e](i),n.c()),m(n,1),n.m(t,null))},i(i){s||(m(n),s=!0)},o(i){$(n),s=!1},d(i){i&&v(t),a[e].d()}}}function Bl(r){let t,e,n,s;const o=[Zl,Ol],a=[];function l(i,c){return!i[0].isConfigured&&i[0].authUrl?0:i[0].isConfigured?1:-1}return~(e=l(r))&&(n=a[e]=o[e](r)),{c(){t=k("div"),n&&n.c(),w(t,"slot","header-right")},m(i,c){y(i,t,c),~e&&a[e].m(t,null),s=!0},p(i,c){let d=e;e=l(i),e===d?~e&&a[e].p(i,c):(n&&(q(),$(a[d],1,1,()=>{a[d]=null}),B()),~e?(n=a[e],n?n.p(i,c):(n=a[e]=o[e](i),n.c()),m(n,1),n.m(t,null)):n=null)},i(i){s||(m(n),s=!0)},o(i){$(n),s=!1},d(i){i&&v(t),~e&&a[e].d()}}}function rr(r){let t,e,n,s=r[0].statusMessage+"";return{c(){t=k("div"),e=I(s),w(t,"class",n="status-message "+r[0].statusType+" svelte-js5lik")},m(o,a){y(o,t,a),b(t,e)},p(o,a){1&a&&s!==(s=o[0].statusMessage+"")&&ct(e,s),1&a&&n!==(n="status-message "+o[0].statusType+" svelte-js5lik")&&w(t,"class",n)},d(o){o&&v(t)}}}function Hl(r){let t,e,n,s,o,a;e=new Kt({props:{icon:r[0].icon,title:r[0].displayName,$$slots:{"header-right":[Bl]},$$scope:{ctx:r}}});let l=r[0].showStatus&&rr(r);return{c(){t=k("div"),_(e.$$.fragment),n=T(),l&&l.c(),w(t,"class","config-wrapper"),w(t,"role","group"),w(t,"aria-label","Connection status controls")},m(i,c){y(i,t,c),C(e,t,null),b(t,n),l&&l.m(t,null),s=!0,o||(a=[It(t,"mouseenter",r[9]),It(t,"mouseleave",r[10])],o=!0)},p(i,[c]){const d={};1&c&&(d.icon=i[0].icon),1&c&&(d.title=i[0].displayName),2063&c&&(d.$$scope={dirty:c,ctx:i}),e.$set(d),i[0].showStatus?l?l.p(i,c):(l=rr(i),l.c(),l.m(t,null)):l&&(l.d(1),l=null)},i(i){s||(m(e.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),s=!1},d(i){i&&v(t),x(e),l&&l.d(),o=!1,xn(a)}}}function Gl(r,t,e){let{config:n}=t,{onAuthenticate:s}=t,{onRevokeAccess:o}=t,a=!1,l=null,i=!1;return r.$$set=c=>{"config"in c&&e(0,n=c.config),"onAuthenticate"in c&&e(5,s=c.onAuthenticate),"onRevokeAccess"in c&&e(1,o=c.onRevokeAccess)},r.$$.update=()=>{133&r.$$.dirty&&n.isConfigured&&a&&(e(2,a=!1),l&&(clearTimeout(l),e(7,l=null)))},[n,o,a,i,function(){if(a)e(2,a=!1),l&&(clearTimeout(l),e(7,l=null));else{e(2,a=!0);const c=n.authUrl||"";s(c),e(7,l=setTimeout(()=>{e(2,a=!1),e(7,l=null)},6e4))}},s,()=>{},l,()=>o(n),()=>e(3,i=!0),()=>e(3,i=!1)]}class Jl extends ot{constructor(t){super(),at(this,t,Gl,Hl,rt,{config:0,onAuthenticate:5,onRevokeAccess:1,onToolApprovalConfigChange:6})}get onToolApprovalConfigChange(){return this.$$.ctx[6]}}function Wl(r){let t;return{c(){t=I(r[0])},m(e,n){y(e,t,n)},p(e,n){1&n&&ct(t,e[0])},d(e){e&&v(t)}}}function Kl(r){let t,e;const n=r[2].default,s=zt(n,r,r[3],null);return{c(){t=k("div"),s&&s.c(),w(t,"class","category-content")},m(o,a){y(o,t,a),s&&s.m(t,null),e=!0},p(o,a){s&&s.p&&(!e||8&a)&&Ot(s,n,o,o[3],e?Ft(n,o[3],a,null):Zt(o[3]),null)},i(o){e||(m(s,o),e=!0)},o(o){$(s,o),e=!1},d(o){o&&v(t),s&&s.d(o)}}}function Yl(r){let t,e,n,s,o;return e=new Hn({props:{size:1}}),s=new Y({props:{size:1,color:"secondary",$$slots:{default:[Xl]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),n=T(),_(s.$$.fragment),w(t,"class","loading-container svelte-2bsejd")},m(a,l){y(a,t,l),C(e,t,null),b(t,n),C(s,t,null),o=!0},p(a,l){const i={};8&l&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(m(e.$$.fragment,a),m(s.$$.fragment,a),o=!0)},o(a){$(e.$$.fragment,a),$(s.$$.fragment,a),o=!1},d(a){a&&v(t),x(e),x(s)}}}function Xl(r){let t;return{c(){t=I("Loading...")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Ql(r){let t,e,n,s,o,a,l;n=new Y({props:{size:1,color:"secondary",weight:"regular",$$slots:{default:[Wl]},$$scope:{ctx:r}}});const i=[Yl,Kl],c=[];function d(u,f){return u[1]?0:1}return o=d(r),a=c[o]=i[o](r),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),a.c(),w(e,"class","category-heading"),w(t,"class","category")},m(u,f){y(u,t,f),b(t,e),C(n,e,null),b(t,s),c[o].m(t,null),l=!0},p(u,[f]){const p={};9&f&&(p.$$scope={dirty:f,ctx:u}),n.$set(p);let g=o;o=d(u),o===g?c[o].p(u,f):(q(),$(c[g],1,1,()=>{c[g]=null}),B(),a=c[o],a?a.p(u,f):(a=c[o]=i[o](u),a.c()),m(a,1),a.m(t,null))},i(u){l||(m(n.$$.fragment,u),m(a),l=!0)},o(u){$(n.$$.fragment,u),$(a),l=!1},d(u){u&&v(t),x(n),c[o].d()}}}function tc(r,t,e){let{$$slots:n={},$$scope:s}=t,{title:o}=t,{loading:a=!1}=t;return r.$$set=l=>{"title"in l&&e(0,o=l.title),"loading"in l&&e(1,a=l.loading),"$$scope"in l&&e(3,s=l.$$scope)},[o,a,n,s]}class ec extends ot{constructor(t){super(),at(this,t,tc,Ql,rt,{title:0,loading:1})}}const yo="extensionClient",wo="mcpServerModel";function ts(){const r=Pe(wo);if(!r)throw new Error("MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.");return r}function nc(r){let t,e,n,s,o,a;return n=new ee({props:{triggerOn:[Kn.Hover],content:"Revoke Access",$$slots:{default:[oc]},$$scope:{ctx:r}}}),o=new Jn.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[ac]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),_(o.$$.fragment),w(e,"class","disconnect-button svelte-e3a21z"),gt(e,"active",r[2]),w(t,"class","status-controls svelte-e3a21z")},m(l,i){y(l,t,i),b(t,e),C(n,e,null),b(t,s),C(o,t,null),a=!0},p(l,i){const c={};2048&i&&(c.$$scope={dirty:i,ctx:l}),n.$set(c),(!a||4&i)&&gt(e,"active",l[2]);const d={};2048&i&&(d.$$scope={dirty:i,ctx:l}),o.$set(d)},i(l){a||(m(n.$$.fragment,l),m(o.$$.fragment,l),a=!0)},o(l){$(n.$$.fragment,l),$(o.$$.fragment,l),a=!1},d(l){l&&v(t),x(n),x(o)}}}function sc(r){let t,e;return t=new vt({props:{variant:"ghost-block",color:r[1]?"neutral":"accent",size:1,$$slots:{default:[cc]},$$scope:{ctx:r}}}),t.$on("click",r[3]),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};2&s&&(o.color=n[1]?"neutral":"accent"),2050&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function rc(r){let t,e;return t=new vo({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function oc(r){let t,e;return t=new Le({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[rc]},$$scope:{ctx:r}}}),t.$on("click",r[4]),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};2048&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function ac(r){let t;return{c(){t=I("Connected")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function ic(r){let t;return{c(){t=k("span"),t.textContent="Connect"},m(e,n){y(e,t,n)},i:D,o:D,d(e){e&&v(t)}}}function lc(r){let t,e,n,s,o;return e=new Hn({props:{size:1,useCurrentColor:!0}}),{c(){t=k("div"),_(e.$$.fragment),n=T(),s=k("span"),s.textContent="Cancel",w(t,"class","connect-button-spinner svelte-e3a21z")},m(a,l){y(a,t,l),C(e,t,null),y(a,n,l),y(a,s,l),o=!0},i(a){o||(m(e.$$.fragment,a),o=!0)},o(a){$(e.$$.fragment,a),o=!1},d(a){a&&(v(t),v(n),v(s)),x(e)}}}function cc(r){let t,e,n,s;const o=[lc,ic],a=[];function l(i,c){return i[1]?0:1}return e=l(r),n=a[e]=o[e](r),{c(){t=k("div"),n.c(),w(t,"class","connect-button-content svelte-e3a21z")},m(i,c){y(i,t,c),a[e].m(t,null),s=!0},p(i,c){let d=e;e=l(i),e!==d&&(q(),$(a[d],1,1,()=>{a[d]=null}),B(),n=a[e],n||(n=a[e]=o[e](i),n.c()),m(n,1),n.m(t,null))},i(i){s||(m(n),s=!0)},o(i){$(n),s=!1},d(i){i&&v(t),a[e].d()}}}function dc(r){let t,e,n,s;const o=[sc,nc],a=[];function l(i,c){return i[0].isConfigured?i[0].isConfigured?1:-1:0}return~(e=l(r))&&(n=a[e]=o[e](r)),{c(){t=k("div"),n&&n.c(),w(t,"slot","header-right")},m(i,c){y(i,t,c),~e&&a[e].m(t,null),s=!0},p(i,c){let d=e;e=l(i),e===d?~e&&a[e].p(i,c):(n&&(q(),$(a[d],1,1,()=>{a[d]=null}),B()),~e?(n=a[e],n?n.p(i,c):(n=a[e]=o[e](i),n.c()),m(n,1),n.m(t,null)):n=null)},i(i){s||(m(n),s=!0)},o(i){$(n),s=!1},d(i){i&&v(t),~e&&a[e].d()}}}function or(r){let t,e,n,s=r[0].statusMessage+"";return{c(){t=k("div"),e=I(s),w(t,"class",n="status-message "+r[0].statusType+" svelte-e3a21z")},m(o,a){y(o,t,a),b(t,e)},p(o,a){1&a&&s!==(s=o[0].statusMessage+"")&&ct(e,s),1&a&&n!==(n="status-message "+o[0].statusType+" svelte-e3a21z")&&w(t,"class",n)},d(o){o&&v(t)}}}function uc(r){let t,e,n,s,o,a;e=new Kt({props:{icon:r[0].icon,title:r[0].displayName,$$slots:{"header-right":[dc]},$$scope:{ctx:r}}});let l=r[0].showStatus&&or(r);return{c(){t=k("div"),_(e.$$.fragment),n=T(),l&&l.c(),w(t,"class","config-wrapper"),w(t,"role","group"),w(t,"aria-label","Connection status controls")},m(i,c){y(i,t,c),C(e,t,null),b(t,n),l&&l.m(t,null),s=!0,o||(a=[It(t,"mouseenter",r[6]),It(t,"mouseleave",r[7])],o=!0)},p(i,[c]){const d={};1&c&&(d.icon=i[0].icon),1&c&&(d.title=i[0].displayName),2055&c&&(d.$$scope={dirty:c,ctx:i}),e.$set(d),i[0].showStatus?l?l.p(i,c):(l=or(i),l.c(),l.m(t,null)):l&&(l.d(1),l=null)},i(i){s||(m(e.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),s=!1},d(i){i&&v(t),x(e),l&&l.d(),o=!1,xn(a)}}}function pc(r,t,e){let{config:n}=t,{mcpTool:s}=t;const o=ts(),a=function(){const d=Pe(yo);if(!d)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return d}();let l=!1,i=!1,c=null;return r.$$set=d=>{"config"in d&&e(0,n=d.config),"mcpTool"in d&&e(5,s=d.mcpTool)},r.$$.update=()=>{1&r.$$.dirty&&e(0,n=qo(n)),32&r.$$.dirty&&e(0,n.isConfigured=!!s,n)},[n,l,i,async function(){if(l)return c&&(clearTimeout(c),c=null),void e(1,l=!1);a.startRemoteMCPAuth(n.name),e(1,l=!0);const d=new Promise(u=>{c=setTimeout(()=>{u(),c=null},6e4)});await Promise.race([d]),e(1,l=!1)},async function(){await a.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${n.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&(s&&o.deleteServer(s==null?void 0:s.id),e(1,l=!1))},s,()=>e(2,i=!0),()=>e(2,i=!1)]}class mc extends ot{constructor(t){super(),at(this,t,pc,uc,rt,{config:0,mcpTool:5})}}function ar(r,t,e){const n=r.slice();return n[13]=t[e],n}function ir(r,t,e){const n=r.slice();return n[13]=t[e],n}function lr(r,t){let e,n,s;return n=new Jl({props:{config:t[13],onAuthenticate:t[2],onRevokeAccess:t[3],onToolApprovalConfigChange:t[4]}}),{key:r,first:null,c(){e=ft(),_(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),C(n,o,a),s=!0},p(o,a){t=o;const l={};2&a&&(l.config=t[13]),4&a&&(l.onAuthenticate=t[2]),8&a&&(l.onRevokeAccess=t[3]),16&a&&(l.onToolApprovalConfigChange=t[4]),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(n,o)}}}function cr(r,t){let e,n,s;function o(...a){return t[10](t[13],...a)}return n=new mc({props:{mcpTool:t[5].find(o),config:t[13]}}),{key:r,first:null,c(){e=ft(),_(n.$$.fragment),this.first=e},m(a,l){y(a,e,l),C(n,a,l),s=!0},p(a,l){t=a;const i={};96&l&&(i.mcpTool=t[5].find(o)),64&l&&(i.config=t[13]),n.$set(i)},i(a){s||(m(n.$$.fragment,a),s=!0)},o(a){$(n.$$.fragment,a),s=!1},d(a){a&&v(e),x(n,a)}}}function fc(r){let t,e,n,s=[],o=new Map,a=[],l=new Map,i=lt(r[1]);const c=f=>f[13].name;for(let f=0;f<i.length;f+=1){let p=ir(r,i,f),g=c(p);o.set(g,s[f]=lr(g,p))}let d=lt(r[6]);const u=f=>f[13].name;for(let f=0;f<d.length;f+=1){let p=ar(r,d,f),g=u(p);l.set(g,a[f]=cr(g,p))}return{c(){t=k("div");for(let f=0;f<s.length;f+=1)s[f].c();e=T();for(let f=0;f<a.length;f+=1)a[f].c();w(t,"class","tool-category-list svelte-on3wl5")},m(f,p){y(f,t,p);for(let g=0;g<s.length;g+=1)s[g]&&s[g].m(t,null);b(t,e);for(let g=0;g<a.length;g+=1)a[g]&&a[g].m(t,null);n=!0},p(f,p){30&p&&(i=lt(f[1]),q(),s=Ht(s,p,c,1,f,i,o,t,Gt,lr,e,ir),B()),96&p&&(d=lt(f[6]),q(),a=Ht(a,p,u,1,f,d,l,t,Gt,cr,null,ar),B())},i(f){if(!n){for(let p=0;p<i.length;p+=1)m(s[p]);for(let p=0;p<d.length;p+=1)m(a[p]);n=!0}},o(f){for(let p=0;p<s.length;p+=1)$(s[p]);for(let p=0;p<a.length;p+=1)$(a[p]);n=!1},d(f){f&&v(t);for(let p=0;p<s.length;p+=1)s[p].d();for(let p=0;p<a.length;p+=1)a[p].d()}}}function $c(r){let t,e,n;return e=new ec({props:{title:r[0],loading:r[1].length===0,$$slots:{default:[fc]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment)},m(s,o){y(s,t,o),C(e,t,null),n=!0},p(s,[o]){const a={};1&o&&(a.title=s[0]),2&o&&(a.loading=s[1].length===0),262270&o&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(m(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&v(t),x(e)}}}function gc(r,t,e){let n,s,o,{title:a}=t,{tools:l=[]}=t,{onAuthenticate:i}=t,{onRevokeAccess:c}=t,{onToolApprovalConfigChange:d=()=>{}}=t;const u=Pe(Ue.key),f=ts(),p=u.getPretendNativeToolDefs();Et(r,p,h=>e(6,o=h));const g=f.getServers();return Et(r,g,h=>e(9,s=h)),r.$$set=h=>{"title"in h&&e(0,a=h.title),"tools"in h&&e(1,l=h.tools),"onAuthenticate"in h&&e(2,i=h.onAuthenticate),"onRevokeAccess"in h&&e(3,c=h.onRevokeAccess),"onToolApprovalConfigChange"in h&&e(4,d=h.onToolApprovalConfigChange)},r.$$.update=()=>{512&r.$$.dirty&&e(5,n=u.getEnableNativeRemoteMcp()?Qr(s):[])},[a,l,i,c,d,n,o,p,g,s,(h,S)=>S.name===h.name]}class hc extends ot{constructor(t){super(),at(this,t,gc,$c,rt,{title:0,tools:1,onAuthenticate:2,onRevokeAccess:3,onToolApprovalConfigChange:4})}}function dr(r,t,e){const n=r.slice();return n[11]=t[e],n[12]=t,n[13]=e,n}function vc(r){let t;return{c(){t=I("Environment Variables")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function ur(r){let t,e,n=[],s=new Map,o=lt(r[0]);const a=l=>l[11].id;for(let l=0;l<o.length;l+=1){let i=dr(r,o,l),c=a(i);s.set(c,n[l]=pr(c,i))}return{c(){for(let l=0;l<n.length;l+=1)n[l].c();t=ft()},m(l,i){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(l,i);y(l,t,i),e=!0},p(l,i){59&i&&(o=lt(l[0]),q(),n=Ht(n,i,a,1,l,o,s,t.parentNode,Gt,pr,t,dr),B())},i(l){if(!e){for(let i=0;i<o.length;i+=1)m(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)$(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d(l)}}}function yc(r){let t,e;return t=new to({props:{slot:"iconLeft"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function wc(r){let t,e;return t=new vt({props:{variant:"ghost",color:"neutral",type:"button",size:1,$$slots:{iconLeft:[yc]},$$scope:{ctx:r}}}),t.$on("focus",function(){he(r[1])&&r[1].apply(this,arguments)}),t.$on("click",function(){return r[10](r[11])}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){r=n;const o={};16384&s&&(o.$$scope={dirty:s,ctx:r}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function pr(r,t){let e,n,s,o,a,l,i,c,d,u,f,p,g;function h(M){t[6](M,t[11])}let S={size:1,placeholder:"Name",class:"full-width"};function P(M){t[8](M,t[11])}t[11].key!==void 0&&(S.value=t[11].key),s=new ye({props:S}),Ct.push(()=>_t(s,"value",h)),s.$on("focus",function(){he(t[1])&&t[1].apply(this,arguments)}),s.$on("change",function(){return t[7](t[11])});let N={size:1,placeholder:"Value",class:"full-width"};return t[11].value!==void 0&&(N.value=t[11].value),i=new ye({props:N}),Ct.push(()=>_t(i,"value",P)),i.$on("focus",function(){he(t[1])&&t[1].apply(this,arguments)}),i.$on("change",function(){return t[9](t[11])}),f=new ee({props:{content:"Remove",$$slots:{default:[wc]},$$scope:{ctx:t}}}),{key:r,first:null,c(){e=k("tr"),n=k("td"),_(s.$$.fragment),a=T(),l=k("td"),_(i.$$.fragment),d=T(),u=k("td"),_(f.$$.fragment),p=T(),w(n,"class","name-cell svelte-1mazg1z"),w(l,"class","value-cell svelte-1mazg1z"),w(u,"class","action-cell svelte-1mazg1z"),w(e,"class","env-var-row svelte-1mazg1z"),this.first=e},m(M,A){y(M,e,A),b(e,n),C(s,n,null),b(e,a),b(e,l),C(i,l,null),b(e,d),b(e,u),C(f,u,null),b(e,p),g=!0},p(M,A){t=M;const E={};!o&&1&A&&(o=!0,E.value=t[11].key,bt(()=>o=!1)),s.$set(E);const z={};!c&&1&A&&(c=!0,z.value=t[11].value,bt(()=>c=!1)),i.$set(z);const G={};16387&A&&(G.$$scope={dirty:A,ctx:t}),f.$set(G)},i(M){g||(m(s.$$.fragment,M),m(i.$$.fragment,M),m(f.$$.fragment,M),g=!0)},o(M){$(s.$$.fragment,M),$(i.$$.fragment,M),$(f.$$.fragment,M),g=!1},d(M){M&&v(e),x(s),x(i),x(f)}}}function xc(r){let t;return{c(){t=I("Variable")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Cc(r){let t,e;return t=new sn({props:{slot:"iconLeft"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function _c(r){let t,e,n,s,o,a,l,i;t=new Y({props:{size:1,weight:"medium",$$slots:{default:[vc]},$$scope:{ctx:r}}});let c=r[0].length>0&&ur(r);return l=new vt({props:{size:1,variant:"soft",color:"neutral",type:"button",$$slots:{iconLeft:[Cc],default:[xc]},$$scope:{ctx:r}}}),l.$on("click",r[2]),{c(){_(t.$$.fragment),e=T(),n=k("table"),s=k("tbody"),c&&c.c(),o=T(),a=k("div"),_(l.$$.fragment),w(n,"class","env-vars-table svelte-1mazg1z"),w(a,"class","new-var-button-container svelte-1mazg1z")},m(d,u){C(t,d,u),y(d,e,u),y(d,n,u),b(n,s),c&&c.m(s,null),y(d,o,u),y(d,a,u),C(l,a,null),i=!0},p(d,[u]){const f={};16384&u&&(f.$$scope={dirty:u,ctx:d}),t.$set(f),d[0].length>0?c?(c.p(d,u),1&u&&m(c,1)):(c=ur(d),c.c(),m(c,1),c.m(s,null)):c&&(q(),$(c,1,1,()=>{c=null}),B());const p={};16384&u&&(p.$$scope={dirty:u,ctx:d}),l.$set(p)},i(d){i||(m(t.$$.fragment,d),m(c),m(l.$$.fragment,d),i=!0)},o(d){$(t.$$.fragment,d),$(c),$(l.$$.fragment,d),i=!1},d(d){d&&(v(e),v(n),v(o),v(a)),x(t,d),c&&c.d(),x(l)}}}function bc(r,t,e){let{handleEnterEditMode:n}=t,{envVarEntries:s=[]}=t;function o(i){n(),e(0,s=s.filter(c=>c.id!==i))}function a(i,c){const d=s.findIndex(u=>u.id===i);d!==-1&&(e(0,s[d].key=c,s),e(0,s))}function l(i,c){const d=s.findIndex(u=>u.id===i);d!==-1&&(e(0,s[d].value=c,s),e(0,s))}return r.$$set=i=>{"handleEnterEditMode"in i&&e(1,n=i.handleEnterEditMode),"envVarEntries"in i&&e(0,s=i.envVarEntries)},[s,n,function(){n(),e(0,s=[...s,{id:crypto.randomUUID(),key:"",value:""}])},o,a,l,function(i,c){r.$$.not_equal(c.key,i)&&(c.key=i,e(0,s))},i=>a(i.id,i.key),function(i,c){r.$$.not_equal(c.value,i)&&(c.value=i,e(0,s))},i=>l(i.id,i.value),i=>o(i.id)]}class Sc extends ot{constructor(t){super(),at(this,t,bc,_c,rt,{handleEnterEditMode:1,envVarEntries:0})}}function mr(r,t,e){const n=r.slice();return n[45]=t[e],n}function kc(r){let t,e,n,s,o,a,l,i,c,d,u,f,p,g,h,S,P,N,M,A,E,z,G,tt,O,F=(r[0]==="add"||r[0]==="edit")&&!Dt(r[3]);a=new Bo({}),i=new Y({props:{color:"secondary",size:1,weight:"medium",$$slots:{default:[Ac]},$$scope:{ctx:r}}});const R=[Nc,Tc],V=[];function st(J,j){return J[0]==="addJson"?0:J[0]==="add"||J[0]==="addRemote"||J[0]==="edit"?1:-1}~(d=st(r))&&(u=V[d]=R[d](r));let Z=F&&$r(r);return S=new Mn({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[qc],default:[Vc]},$$scope:{ctx:r}}}),M=new vt({props:{size:1,variant:"ghost",color:"neutral",type:"button",$$slots:{default:[Bc]},$$scope:{ctx:r}}}),M.$on("click",r[22]),E=new vt({props:{size:1,variant:"solid",color:"accent",loading:r[2],type:"submit",disabled:r[17],$$slots:{default:[Kc]},$$scope:{ctx:r}}}),{c(){t=k("form"),e=k("div"),n=k("div"),s=k("div"),o=k("div"),_(a.$$.fragment),l=T(),_(i.$$.fragment),c=T(),u&&u.c(),f=T(),Z&&Z.c(),p=T(),g=k("div"),h=k("div"),_(S.$$.fragment),P=T(),N=k("div"),_(M.$$.fragment),A=T(),_(E.$$.fragment),w(o,"class","server-icon svelte-igdbzh"),w(s,"class","server-title svelte-igdbzh"),w(n,"class","server-header svelte-igdbzh"),w(h,"class","error-container svelte-igdbzh"),gt(h,"is-error",!!r[1]),w(N,"class","form-actions svelte-igdbzh"),w(g,"class","form-actions-row svelte-igdbzh"),w(e,"class","server-edit-form svelte-igdbzh"),w(t,"class",z="c-mcp-server-card "+(r[0]==="add"||r[0]==="addJson"||r[0]==="addRemote"?"add-server-section":"server-item")+" svelte-igdbzh")},m(J,j){y(J,t,j),b(t,e),b(e,n),b(n,s),b(s,o),C(a,o,null),b(s,l),C(i,s,null),b(e,c),~d&&V[d].m(e,null),b(e,f),Z&&Z.m(e,null),b(e,p),b(e,g),b(g,h),C(S,h,null),b(g,P),b(g,N),C(M,N,null),b(N,A),C(E,N,null),G=!0,tt||(O=It(t,"submit",No(r[21])),tt=!0)},p(J,j){const pt={};65536&j[0]|131072&j[1]&&(pt.$$scope={dirty:j,ctx:J}),i.$set(pt);let Lt=d;d=st(J),d===Lt?~d&&V[d].p(J,j):(u&&(q(),$(V[Lt],1,1,()=>{V[Lt]=null}),B()),~d?(u=V[d],u?u.p(J,j):(u=V[d]=R[d](J),u.c()),m(u,1),u.m(e,f)):u=null),9&j[0]&&(F=(J[0]==="add"||J[0]==="edit")&&!Dt(J[3])),F?Z?(Z.p(J,j),9&j[0]&&m(Z,1)):(Z=$r(J),Z.c(),m(Z,1),Z.m(e,p)):Z&&(q(),$(Z,1,1,()=>{Z=null}),B());const _e={};2&j[0]|131072&j[1]&&(_e.$$scope={dirty:j,ctx:J}),S.$set(_e),(!G||2&j[0])&&gt(h,"is-error",!!J[1]);const ze={};131072&j[1]&&(ze.$$scope={dirty:j,ctx:J}),M.$set(ze);const fe={};4&j[0]&&(fe.loading=J[2]),131072&j[0]&&(fe.disabled=J[17]),1&j[0]|131072&j[1]&&(fe.$$scope={dirty:j,ctx:J}),E.$set(fe),(!G||1&j[0]&&z!==(z="c-mcp-server-card "+(J[0]==="add"||J[0]==="addJson"||J[0]==="addRemote"?"add-server-section":"server-item")+" svelte-igdbzh"))&&w(t,"class",z)},i(J){G||(m(a.$$.fragment,J),m(i.$$.fragment,J),m(u),m(Z),m(S.$$.fragment,J),m(M.$$.fragment,J),m(E.$$.fragment,J),G=!0)},o(J){$(a.$$.fragment,J),$(i.$$.fragment,J),$(u),$(Z),$(S.$$.fragment,J),$(M.$$.fragment,J),$(E.$$.fragment,J),G=!1},d(J){J&&v(t),x(a),x(i),~d&&V[d].d(),Z&&Z.d(),x(S),x(M),x(E),tt=!1,O()}}}function Mc(r){let t,e,n;function s(a){r[36](a)}let o={$$slots:{footer:[xd],header:[hd]},$$scope:{ctx:r}};return r[14]!==void 0&&(o.collapsed=r[14]),t=new oo({props:o}),Ct.push(()=>_t(t,"collapsed",s)),{c(){_(t.$$.fragment)},m(a,l){C(t,a,l),n=!0},p(a,l){const i={};311544&l[0]|131072&l[1]&&(i.$$scope={dirty:l,ctx:a}),!e&&16384&l[0]&&(e=!0,i.collapsed=a[14],bt(()=>e=!1)),t.$set(i)},i(a){n||(m(t.$$.fragment,a),n=!0)},o(a){$(t.$$.fragment,a),n=!1},d(a){x(t,a)}}}function Ac(r){let t;return{c(){t=I(r[16])},m(e,n){y(e,t,n)},p(e,n){65536&n[0]&&ct(t,e[16])},d(e){e&&v(t)}}}function Tc(r){var P,N;let t,e,n,s,o,a,l,i,c,d,u=(r[0]==="addRemote"||r[0]==="edit"&&(((P=r[3])==null?void 0:P.type)==="http"||((N=r[3])==null?void 0:N.type)==="sse"))&&fr(r);function f(M){r[40](M)}let p={size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",$$slots:{label:[Lc]},$$scope:{ctx:r}};r[8]!==void 0&&(p.value=r[8]),s=new ye({props:p}),Ct.push(()=>_t(s,"value",f)),s.$on("focus",r[19]);const g=[Oc,zc],h=[];function S(M,A){var E,z;return M[0]==="addRemote"||((E=M[3])==null?void 0:E.type)==="http"||((z=M[3])==null?void 0:z.type)==="sse"?0:1}return l=S(r),i=h[l]=g[l](r),{c(){u&&u.c(),t=T(),e=k("div"),n=k("div"),_(s.$$.fragment),a=T(),i.c(),c=ft(),w(n,"class","input-field svelte-igdbzh"),w(e,"class","form-row svelte-igdbzh")},m(M,A){u&&u.m(M,A),y(M,t,A),y(M,e,A),b(e,n),C(s,n,null),y(M,a,A),h[l].m(M,A),y(M,c,A),d=!0},p(M,A){var G,tt;M[0]==="addRemote"||M[0]==="edit"&&(((G=M[3])==null?void 0:G.type)==="http"||((tt=M[3])==null?void 0:tt.type)==="sse")?u?(u.p(M,A),9&A[0]&&m(u,1)):(u=fr(M),u.c(),m(u,1),u.m(t.parentNode,t)):u&&(q(),$(u,1,1,()=>{u=null}),B());const E={};131072&A[1]&&(E.$$scope={dirty:A,ctx:M}),!o&&256&A[0]&&(o=!0,E.value=M[8],bt(()=>o=!1)),s.$set(E);let z=l;l=S(M),l===z?h[l].p(M,A):(q(),$(h[z],1,1,()=>{h[z]=null}),B(),i=h[l],i?i.p(M,A):(i=h[l]=g[l](M),i.c()),m(i,1),i.m(c.parentNode,c))},i(M){d||(m(u),m(s.$$.fragment,M),m(i),d=!0)},o(M){$(u),$(s.$$.fragment,M),$(i),d=!1},d(M){M&&(v(t),v(e),v(a),v(c)),u&&u.d(M),x(s),h[l].d(M)}}}function Nc(r){let t,e,n,s,o,a,l,i,c;function d(f){r[37](f)}n=new Y({props:{size:1,weight:"medium",$$slots:{default:[Uc]},$$scope:{ctx:r}}});let u={size:1,placeholder:"Paste JSON here..."};return r[11]!==void 0&&(u.value=r[11]),l=new ro({props:u}),Ct.push(()=>_t(l,"value",d)),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),o=k("div"),a=k("div"),_(l.$$.fragment),w(e,"class","input-field svelte-igdbzh"),w(t,"class","form-row svelte-igdbzh"),w(a,"class","input-field svelte-igdbzh"),w(o,"class","form-row svelte-igdbzh")},m(f,p){y(f,t,p),b(t,e),C(n,e,null),y(f,s,p),y(f,o,p),b(o,a),C(l,a,null),c=!0},p(f,p){const g={};131072&p[1]&&(g.$$scope={dirty:p,ctx:f}),n.$set(g);const h={};!i&&2048&p[0]&&(i=!0,h.value=f[11],bt(()=>i=!1)),l.$set(h)},i(f){c||(m(n.$$.fragment,f),m(l.$$.fragment,f),c=!0)},o(f){$(n.$$.fragment,f),$(l.$$.fragment,f),c=!1},d(f){f&&(v(t),v(s),v(o)),x(n),x(l)}}}function fr(r){let t,e,n,s,o,a,l,i,c;return n=new Y({props:{size:1,weight:"medium",$$slots:{default:[Ec]},$$scope:{ctx:r}}}),a=new vt({props:{size:1,variant:r[12]==="http"?"solid":"ghost",color:r[12]==="http"?"accent":"neutral",type:"button",$$slots:{default:[Ic]},$$scope:{ctx:r}}}),a.$on("click",r[38]),i=new vt({props:{size:1,variant:r[12]==="sse"?"solid":"ghost",color:r[12]==="sse"?"accent":"neutral",type:"button",$$slots:{default:[Rc]},$$scope:{ctx:r}}}),i.$on("click",r[39]),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),o=k("div"),_(a.$$.fragment),l=T(),_(i.$$.fragment),w(o,"class","connection-type-buttons svelte-igdbzh"),w(e,"class","input-field svelte-igdbzh"),w(t,"class","form-row svelte-igdbzh")},m(d,u){y(d,t,u),b(t,e),C(n,e,null),b(e,s),b(e,o),C(a,o,null),b(o,l),C(i,o,null),c=!0},p(d,u){const f={};131072&u[1]&&(f.$$scope={dirty:u,ctx:d}),n.$set(f);const p={};4096&u[0]&&(p.variant=d[12]==="http"?"solid":"ghost"),4096&u[0]&&(p.color=d[12]==="http"?"accent":"neutral"),131072&u[1]&&(p.$$scope={dirty:u,ctx:d}),a.$set(p);const g={};4096&u[0]&&(g.variant=d[12]==="sse"?"solid":"ghost"),4096&u[0]&&(g.color=d[12]==="sse"?"accent":"neutral"),131072&u[1]&&(g.$$scope={dirty:u,ctx:d}),i.$set(g)},i(d){c||(m(n.$$.fragment,d),m(a.$$.fragment,d),m(i.$$.fragment,d),c=!0)},o(d){$(n.$$.fragment,d),$(a.$$.fragment,d),$(i.$$.fragment,d),c=!1},d(d){d&&v(t),x(n),x(a),x(i)}}}function Ec(r){let t;return{c(){t=I("Connection Type")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Ic(r){let t;return{c(){t=I("HTTP")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Rc(r){let t;return{c(){t=I("SSE")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Pc(r){let t;return{c(){t=I("Name")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Lc(r){let t,e;return t=new Y({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[Pc]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function zc(r){let t,e,n,s,o;function a(i){r[42](i)}let l={size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",$$slots:{label:[Fc]},$$scope:{ctx:r}};return r[9]!==void 0&&(l.value=r[9]),n=new ye({props:l}),Ct.push(()=>_t(n,"value",a)),n.$on("focus",r[19]),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),w(e,"class","input-field svelte-igdbzh"),w(t,"class","form-row svelte-igdbzh")},m(i,c){y(i,t,c),b(t,e),C(n,e,null),o=!0},p(i,c){const d={};131072&c[1]&&(d.$$scope={dirty:c,ctx:i}),!s&&512&c[0]&&(s=!0,d.value=i[9],bt(()=>s=!1)),n.$set(d)},i(i){o||(m(n.$$.fragment,i),o=!0)},o(i){$(n.$$.fragment,i),o=!1},d(i){i&&v(t),x(n)}}}function Oc(r){let t,e,n,s,o;function a(i){r[41](i)}let l={size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",$$slots:{label:[Dc]},$$scope:{ctx:r}};return r[10]!==void 0&&(l.value=r[10]),n=new ye({props:l}),Ct.push(()=>_t(n,"value",a)),n.$on("focus",r[19]),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),w(e,"class","input-field svelte-igdbzh"),w(t,"class","form-row svelte-igdbzh")},m(i,c){y(i,t,c),b(t,e),C(n,e,null),o=!0},p(i,c){const d={};131072&c[1]&&(d.$$scope={dirty:c,ctx:i}),!s&&1024&c[0]&&(s=!0,d.value=i[10],bt(()=>s=!1)),n.$set(d)},i(i){o||(m(n.$$.fragment,i),o=!0)},o(i){$(n.$$.fragment,i),o=!1},d(i){i&&v(t),x(n)}}}function Zc(r){let t;return{c(){t=I("Command")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Fc(r){let t,e;return t=new Y({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[Zc]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function jc(r){let t;return{c(){t=I("URL")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Dc(r){let t,e;return t=new Y({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[jc]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Uc(r){let t;return{c(){t=I("Code Snippet")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function $r(r){let t,e,n;function s(a){r[43](a)}let o={handleEnterEditMode:r[19]};return r[13]!==void 0&&(o.envVarEntries=r[13]),t=new Sc({props:o}),Ct.push(()=>_t(t,"envVarEntries",s)),{c(){_(t.$$.fragment)},m(a,l){C(t,a,l),n=!0},p(a,l){const i={};!e&&8192&l[0]&&(e=!0,i.envVarEntries=a[13],bt(()=>e=!1)),t.$set(i)},i(a){n||(m(t.$$.fragment,a),n=!0)},o(a){$(t.$$.fragment,a),n=!1},d(a){x(t,a)}}}function Vc(r){let t;return{c(){t=I(r[1])},m(e,n){y(e,t,n)},p(e,n){2&n[0]&&ct(t,e[1])},d(e){e&&v(t)}}}function qc(r){let t,e;return t=new Ho({props:{slot:"icon"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Bc(r){let t;return{c(){t=I("Cancel")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Hc(r){let t;return{c(){t=I("Save")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Gc(r){let t;return{c(){t=I("Add")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Jc(r){let t;return{c(){t=I("Add")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Wc(r){let t;return{c(){t=I("Import")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Kc(r){let t;function e(o,a){return o[0]==="addJson"?Wc:o[0]==="add"?Jc:o[0]==="addRemote"?Gc:o[0]==="edit"?Hc:void 0}let n=e(r),s=n&&n(r);return{c(){s&&s.c(),t=ft()},m(o,a){s&&s.m(o,a),y(o,t,a)},p(o,a){n!==(n=e(o))&&(s&&s.d(1),s=n&&n(o),s&&(s.c(),s.m(t.parentNode,t)))},d(o){o&&v(t),s&&s.d(o)}}}function gr(r){let t,e;return t=new Le({props:{size:1,variant:"ghost",$$slots:{default:[Qc]},$$scope:{ctx:r}}}),t.$on("click",r[35]),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};16384&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Yc(r){let t,e;return t=new Xn({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Xc(r){let t,e;return t=new ta({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Qc(r){let t,e,n,s;const o=[Xc,Yc],a=[];function l(i,c){return i[14]?0:1}return t=l(r),e=a[t]=o[t](r),{c(){e.c(),n=ft()},m(i,c){a[t].m(i,c),y(i,n,c),s=!0},p(i,c){let d=t;t=l(i),t!==d&&(q(),$(a[d],1,1,()=>{a[d]=null}),B(),e=a[t],e||(e=a[t]=o[t](i),e.c()),m(e,1),e.m(n.parentNode,n))},i(i){s||(m(e),s=!0)},o(i){$(e),s=!1},d(i){i&&v(n),a[t].d(i)}}}function td(r){let t;return{c(){t=k("div"),w(t,"class","c-dot svelte-igdbzh"),gt(t,"c-green",!r[6]),gt(t,"c-warning",!r[6]&&!!r[7]),gt(t,"c-red",!!r[6]),gt(t,"c-disabled",r[3].disabled)},m(e,n){y(e,t,n)},p(e,n){64&n[0]&&gt(t,"c-green",!e[6]),192&n[0]&&gt(t,"c-warning",!e[6]&&!!e[7]),64&n[0]&&gt(t,"c-red",!!e[6]),8&n[0]&&gt(t,"c-disabled",e[3].disabled)},d(e){e&&v(t)}}}function hr(r){let t,e,n,s=r[18].length+"";return{c(){t=I("("),e=I(s),n=I(") tools")},m(o,a){y(o,t,a),y(o,e,a),y(o,n,a)},p(o,a){262144&a[0]&&s!==(s=o[18].length+"")&&ct(e,s)},d(o){o&&(v(t),v(e),v(n))}}}function ed(r){let t,e,n,s=r[3].name+"",o=r[18].length>0&&hr(r);return{c(){t=I(s),e=T(),o&&o.c(),n=ft()},m(a,l){y(a,t,l),y(a,e,l),o&&o.m(a,l),y(a,n,l)},p(a,l){8&l[0]&&s!==(s=a[3].name+"")&&ct(t,s),a[18].length>0?o?o.p(a,l):(o=hr(a),o.c(),o.m(n.parentNode,n)):o&&(o.d(1),o=null)},d(a){a&&(v(t),v(e),v(n)),o&&o.d(a)}}}function nd(r){let t,e,n;return e=new Y({props:{size:1,weight:"medium",$$slots:{default:[ed]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),w(t,"class","server-name svelte-igdbzh")},m(s,o){y(s,t,o),C(e,t,null),n=!0},p(s,o){const a={};262152&o[0]|131072&o[1]&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(m(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&v(t),x(e)}}}function sd(r){let t,e=wn(r[3])+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){8&s[0]&&e!==(e=wn(n[3])+"")&&ct(t,e)},d(n){n&&v(t)}}}function rd(r){let t,e;return t=new Y({props:{color:"secondary",size:1,weight:"regular",$$slots:{default:[sd]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};8&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function od(r){let t,e,n,s,o,a,l,i,c,d=r[18].length>0&&gr(r);return n=new ee({props:{content:r[6]||r[7],$$slots:{default:[td]},$$scope:{ctx:r}}}),o=new ee({props:{content:r[3].name,side:"top",align:"start",$$slots:{default:[nd]},$$scope:{ctx:r}}}),i=new ee({props:{content:wn(r[3]),side:"top",align:"start",$$slots:{default:[rd]},$$scope:{ctx:r}}}),{c(){t=k("div"),d&&d.c(),e=T(),_(n.$$.fragment),s=T(),_(o.$$.fragment),a=T(),l=k("div"),_(i.$$.fragment),w(l,"class","command-text svelte-igdbzh"),w(t,"slot","header-left"),w(t,"class","l-header svelte-igdbzh")},m(u,f){y(u,t,f),d&&d.m(t,null),b(t,e),C(n,t,null),b(t,s),C(o,t,null),b(t,a),b(t,l),C(i,l,null),c=!0},p(u,f){u[18].length>0?d?(d.p(u,f),262144&f[0]&&m(d,1)):(d=gr(u),d.c(),m(d,1),d.m(t,e)):d&&(q(),$(d,1,1,()=>{d=null}),B());const p={};192&f[0]&&(p.content=u[6]||u[7]),200&f[0]|131072&f[1]&&(p.$$scope={dirty:f,ctx:u}),n.$set(p);const g={};8&f[0]&&(g.content=u[3].name),262152&f[0]|131072&f[1]&&(g.$$scope={dirty:f,ctx:u}),o.$set(g);const h={};8&f[0]&&(h.content=wn(u[3])),8&f[0]|131072&f[1]&&(h.$$scope={dirty:f,ctx:u}),i.$set(h)},i(u){c||(m(d),m(n.$$.fragment,u),m(o.$$.fragment,u),m(i.$$.fragment,u),c=!0)},o(u){$(d),$(n.$$.fragment,u),$(o.$$.fragment,u),$(i.$$.fragment,u),c=!1},d(u){u&&v(t),d&&d.d(),x(n),x(o),x(i)}}}function ad(r){let t,e;return t=new oa({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function id(r){let t,e;return t=new Le({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[ad]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function ld(r){let t;return{c(){t=I("Edit")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function cd(r){let t,e,n,s,o;return e=new aa({}),s=new Y({props:{size:1,weight:"medium",$$slots:{default:[ld]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),n=T(),_(s.$$.fragment),w(t,"class","status-controls-button svelte-igdbzh")},m(a,l){y(a,t,l),C(e,t,null),b(t,n),C(s,t,null),o=!0},p(a,l){const i={};131072&l[1]&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(m(e.$$.fragment,a),m(s.$$.fragment,a),o=!0)},o(a){$(e.$$.fragment,a),$(s.$$.fragment,a),o=!1},d(a){a&&v(t),x(e),x(s)}}}function dd(r){let t;return{c(){t=I("Copy JSON")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function ud(r){let t,e,n,s,o;return e=new la({}),s=new Y({props:{size:1,weight:"medium",$$slots:{default:[dd]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),n=T(),_(s.$$.fragment),w(t,"class","status-controls-button svelte-igdbzh")},m(a,l){y(a,t,l),C(e,t,null),b(t,n),C(s,t,null),o=!0},p(a,l){const i={};131072&l[1]&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(m(e.$$.fragment,a),m(s.$$.fragment,a),o=!0)},o(a){$(e.$$.fragment,a),$(s.$$.fragment,a),o=!1},d(a){a&&v(t),x(e),x(s)}}}function pd(r){let t;return{c(){t=I("Delete")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function md(r){let t,e,n,s,o;return e=new to({}),s=new Y({props:{size:1,weight:"medium",$$slots:{default:[pd]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),n=T(),_(s.$$.fragment),w(t,"class","status-controls-button svelte-igdbzh")},m(a,l){y(a,t,l),C(e,t,null),b(t,n),C(s,t,null),o=!0},p(a,l){const i={};131072&l[1]&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(m(e.$$.fragment,a),m(s.$$.fragment,a),o=!0)},o(a){$(e.$$.fragment,a),$(s.$$.fragment,a),o=!1},d(a){a&&v(t),x(e),x(s)}}}function fd(r){let t,e,n,s,o,a;return t=new wt.Item({props:{onSelect:r[19],$$slots:{default:[cd]},$$scope:{ctx:r}}}),n=new wt.Item({props:{onSelect:r[32],$$slots:{default:[ud]},$$scope:{ctx:r}}}),o=new wt.Item({props:{color:"error",onSelect:r[33],$$slots:{default:[md]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment),e=T(),_(n.$$.fragment),s=T(),_(o.$$.fragment)},m(l,i){C(t,l,i),y(l,e,i),C(n,l,i),y(l,s,i),C(o,l,i),a=!0},p(l,i){const c={};131072&i[1]&&(c.$$scope={dirty:i,ctx:l}),t.$set(c);const d={};32768&i[0]&&(d.onSelect=l[32]),131072&i[1]&&(d.$$scope={dirty:i,ctx:l}),n.$set(d);const u={};32792&i[0]&&(u.onSelect=l[33]),131072&i[1]&&(u.$$scope={dirty:i,ctx:l}),o.$set(u)},i(l){a||(m(t.$$.fragment,l),m(n.$$.fragment,l),m(o.$$.fragment,l),a=!0)},o(l){$(t.$$.fragment,l),$(n.$$.fragment,l),$(o.$$.fragment,l),a=!1},d(l){l&&(v(e),v(s)),x(t,l),x(n,l),x(o,l)}}}function $d(r){let t,e,n,s;return t=new wt.Trigger({props:{$$slots:{default:[id]},$$scope:{ctx:r}}}),n=new wt.Content({props:{side:"bottom",align:"end",$$slots:{default:[fd]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment),e=T(),_(n.$$.fragment)},m(o,a){C(t,o,a),y(o,e,a),C(n,o,a),s=!0},p(o,a){const l={};131072&a[1]&&(l.$$scope={dirty:a,ctx:o}),t.$set(l);const i={};32792&a[0]|131072&a[1]&&(i.$$scope={dirty:a,ctx:o}),n.$set(i)},i(o){s||(m(t.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){$(t.$$.fragment,o),$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(t,o),x(n,o)}}}function gd(r){let t,e,n,s,o,a,l=Ro(),i=l&&function(u){let f,p;return f=new Wn({props:{size:1,checked:!u[3].disabled}}),f.$on("change",u[31]),{c(){_(f.$$.fragment)},m(g,h){C(f,g,h),p=!0},p(g,h){const S={};8&h[0]&&(S.checked=!g[3].disabled),f.$set(S)},i(g){p||(m(f.$$.fragment,g),p=!0)},o(g){$(f.$$.fragment,g),p=!1},d(g){x(f,g)}}}(r);function c(u){r[34](u)}let d={$$slots:{default:[$d]},$$scope:{ctx:r}};return r[15]!==void 0&&(d.requestClose=r[15]),s=new wt.Root({props:d}),Ct.push(()=>_t(s,"requestClose",c)),{c(){t=k("div"),e=k("div"),i&&i.c(),n=T(),_(s.$$.fragment),w(e,"class","status-controls svelte-igdbzh"),w(t,"class","server-actions svelte-igdbzh"),w(t,"slot","header-right")},m(u,f){y(u,t,f),b(t,e),i&&i.m(e,null),b(e,n),C(s,e,null),a=!0},p(u,f){l&&i.p(u,f);const p={};32792&f[0]|131072&f[1]&&(p.$$scope={dirty:f,ctx:u}),!o&&32768&f[0]&&(o=!0,p.requestClose=u[15],bt(()=>o=!1)),s.$set(p)},i(u){a||(m(i),m(s.$$.fragment,u),a=!0)},o(u){$(i),$(s.$$.fragment,u),a=!1},d(u){u&&v(t),i&&i.d(),x(s)}}}function hd(r){let t,e;return t=new Kt({props:{slot:"header",$$slots:{"header-right":[gd],"header-left":[od]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};311544&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function vd(r){let t,e=(r[45].definition.mcp_tool_name||r[45].definition.name)+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){262144&s[0]&&e!==(e=(n[45].definition.mcp_tool_name||n[45].definition.name)+"")&&ct(t,e)},d(n){n&&v(t)}}}function vr(r){let t,e;return t=new Y({props:{size:1,color:"secondary",$$slots:{default:[yd]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};262144&s[0]|131072&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function yd(r){let t,e=r[45].definition.description+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){262144&s[0]&&e!==(e=n[45].definition.description+"")&&ct(t,e)},d(n){n&&v(t)}}}function wd(r){let t,e,n=r[45].definition.description&&vr(r);return{c(){n&&n.c(),t=ft()},m(s,o){n&&n.m(s,o),y(s,t,o),e=!0},p(s,o){s[45].definition.description?n?(n.p(s,o),262144&o[0]&&m(n,1)):(n=vr(s),n.c(),m(n,1),n.m(t.parentNode,t)):n&&(q(),$(n,1,1,()=>{n=null}),B())},i(s){e||(m(n),e=!0)},o(s){$(n),e=!1},d(s){s&&v(t),n&&n.d(s)}}}function yr(r){let t,e,n,s,o,a,l,i,c,d,u;return a=new Y({props:{size:1,weight:"medium",$$slots:{default:[vd]},$$scope:{ctx:r}}}),c=new ee({props:{content:r[45].definition.description,align:"start",$$slots:{default:[wd]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),n=k("div"),s=k("div"),o=T(),_(a.$$.fragment),l=T(),i=k("div"),_(c.$$.fragment),d=T(),w(s,"class","tool-status-dot svelte-igdbzh"),gt(s,"enabled",r[45].enabled),gt(s,"disabled",!r[45].enabled),w(n,"class","tool-status svelte-igdbzh"),w(i,"class","c-tool-description svelte-igdbzh"),w(e,"class","c-tool-info svelte-igdbzh"),w(t,"class","c-tool-item svelte-igdbzh")},m(f,p){y(f,t,p),b(t,e),b(e,n),b(n,s),b(n,o),C(a,n,null),b(e,l),b(e,i),C(c,i,null),b(t,d),u=!0},p(f,p){(!u||262144&p[0])&&gt(s,"enabled",f[45].enabled),(!u||262144&p[0])&&gt(s,"disabled",!f[45].enabled);const g={};262144&p[0]|131072&p[1]&&(g.$$scope={dirty:p,ctx:f}),a.$set(g);const h={};262144&p[0]&&(h.content=f[45].definition.description),262144&p[0]|131072&p[1]&&(h.$$scope={dirty:p,ctx:f}),c.$set(h)},i(f){u||(m(a.$$.fragment,f),m(c.$$.fragment,f),u=!0)},o(f){$(a.$$.fragment,f),$(c.$$.fragment,f),u=!1},d(f){f&&v(t),x(a),x(c)}}}function xd(r){let t,e,n=lt(r[18]),s=[];for(let a=0;a<n.length;a+=1)s[a]=yr(mr(r,n,a));const o=a=>$(s[a],1,1,()=>{s[a]=null});return{c(){t=k("div");for(let a=0;a<s.length;a+=1)s[a].c();w(t,"slot","footer")},m(a,l){y(a,t,l);for(let i=0;i<s.length;i+=1)s[i]&&s[i].m(t,null);e=!0},p(a,l){if(262144&l[0]){let i;for(n=lt(a[18]),i=0;i<n.length;i+=1){const c=mr(a,n,i);s[i]?(s[i].p(c,l),m(s[i],1)):(s[i]=yr(c),s[i].c(),m(s[i],1),s[i].m(t,null))}for(q(),i=n.length;i<s.length;i+=1)o(i);B()}},i(a){if(!e){for(let l=0;l<n.length;l+=1)m(s[l]);e=!0}},o(a){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)$(s[l]);e=!1},d(a){a&&v(t),me(s,a)}}}function Cd(r){let t,e,n,s;const o=[Mc,kc],a=[];function l(i,c){return i[0]==="view"&&i[3]?0:1}return t=l(r),e=a[t]=o[t](r),{c(){e.c(),n=ft()},m(i,c){a[t].m(i,c),y(i,n,c),s=!0},p(i,c){let d=t;t=l(i),t===d?a[t].p(i,c):(q(),$(a[d],1,1,()=>{a[d]=null}),B(),e=a[t],e?e.p(i,c):(e=a[t]=o[t](i),e.c()),m(e,1),e.m(n.parentNode,n))},i(i){s||(m(e),s=!0)},o(i){$(e),s=!1},d(i){i&&v(n),a[t].d(i)}}}function _d({key:r,value:t}){return r.trim()&&t.trim()}function bd(r,t,e){let n,s,o,a,l,{server:i=null}=t,{onDelete:c}=t,{onAdd:d}=t,{onSave:u}=t,{onEdit:f}=t,{onToggleDisableServer:p}=t,{onJSONImport:g}=t,{onCancel:h}=t,{disabledText:S}=t,{warningText:P}=t,{mode:N="view"}=t,{mcpServerError:M=""}=t,A=(i==null?void 0:i.name)??"",E=Dt(i)?"":Me(i)?i.command:"",z=Dt(i)?i.url:"",G=Me(i)?i.env??{}:{},tt="",O=Dt(i)?i.type:"http",F=[];V();let R=!0;function V(){e(13,F=Object.entries(G).map(([j,pt])=>({id:crypto.randomUUID(),key:j,value:pt})))}let st=()=>{},{busy:Z=!1}=t;function J(){if(i){const j=on.convertServerToJSON(i);navigator.clipboard.writeText(j)}}return r.$$set=j=>{"server"in j&&e(3,i=j.server),"onDelete"in j&&e(4,c=j.onDelete),"onAdd"in j&&e(23,d=j.onAdd),"onSave"in j&&e(24,u=j.onSave),"onEdit"in j&&e(25,f=j.onEdit),"onToggleDisableServer"in j&&e(5,p=j.onToggleDisableServer),"onJSONImport"in j&&e(26,g=j.onJSONImport),"onCancel"in j&&e(27,h=j.onCancel),"disabledText"in j&&e(6,S=j.disabledText),"warningText"in j&&e(7,P=j.warningText),"mode"in j&&e(0,N=j.mode),"mcpServerError"in j&&e(1,M=j.mcpServerError),"busy"in j&&e(2,Z=j.busy)},r.$$.update=()=>{8&r.$$.dirty[0]&&e(18,n=(i==null?void 0:i.tools)??[]),768&r.$$.dirty[0]&&A&&E&&e(1,M=""),1793&r.$$.dirty[0]&&e(30,s=!((N!=="add"||A.trim()&&E.trim())&&(N!=="addRemote"||A.trim()&&z.trim()))),2049&r.$$.dirty[0]&&e(29,o=N==="addJson"&&!tt.trim()),1610612737&r.$$.dirty[0]&&e(17,a=s||N==="view"||o),1&r.$$.dirty[0]&&e(16,l=(()=>{switch(N){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())},[N,M,Z,i,c,p,S,P,A,E,z,tt,O,F,R,st,l,a,n,function(){i&&N==="view"&&(e(0,N="edit"),f(i),st())},J,async function(){e(1,M=""),e(2,Z=!0);const j=F.filter(_d);G=Object.fromEntries(j.map(({key:pt,value:Lt})=>[pt.trim(),Lt.trim()])),V();try{if(N==="add"){const pt={type:"stdio",name:A.trim(),command:E.trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(G).length>0?G:void 0};await d(pt)}else if(N==="addRemote"){const pt={type:O,name:A.trim(),url:z.trim()};await d(pt)}else if(N==="addJson"){try{JSON.parse(tt)}catch(pt){const Lt=pt instanceof Error?pt.message:String(pt);throw new Tt(`Invalid JSON format: ${Lt}`)}await g(tt)}else if(N==="edit"&&i){if(Dt(i)){const pt={...i,type:O,name:A.trim(),url:z.trim()};await u(pt)}else if(Me(i)){const pt={...i,name:A.trim(),command:E.trim(),arguments:"",env:Object.keys(G).length>0?G:void 0};await u(pt)}}}catch(pt){e(1,M=pt instanceof Tt?pt.message:"Failed to save server"),console.warn(pt)}finally{e(2,Z=!1)}},function(){e(2,Z=!1),e(1,M=""),h==null||h(),e(11,tt=""),e(8,A=(i==null?void 0:i.name)??""),e(9,E=Dt(i)?"":Me(i)?i.command:""),e(10,z=Dt(i)?i.url:""),G=Me(i)&&i.env?{...i.env}:{},e(12,O=Dt(i)?i.type:"http"),V()},d,u,f,g,h,V,o,s,()=>{i&&p(i.id),st()},()=>{J(),st()},()=>{c(i.id),st()},function(j){st=j,e(15,st)},()=>e(14,R=!R),function(j){R=j,e(14,R)},function(j){tt=j,e(11,tt)},()=>e(12,O="http"),()=>e(12,O="sse"),function(j){A=j,e(8,A)},function(j){z=j,e(10,z)},function(j){E=j,e(9,E)},function(j){F=j,e(13,F)}]}class xo extends ot{constructor(t){super(),at(this,t,bd,Cd,rt,{server:3,onDelete:4,onAdd:23,onSave:24,onEdit:25,onToggleDisableServer:5,onJSONImport:26,onCancel:27,disabledText:6,warningText:7,mode:0,mcpServerError:1,setLocalEnvVarFormState:28,busy:2},null,[-1,-1])}get setLocalEnvVarFormState(){return this.$$.ctx[28]}}function wr(r,t,e){const n=r.slice();return n[18]=t[e],n}function xr(r,t,e){const n=r.slice();n[21]=t[e],n[24]=e;const s=n[21].type==="environmentVariable"&&n[21].envVarName?n[21].envVarName:n[21].correspondingArg||`input_${n[24]}`;return n[22]=s,n}function Sd(r){let t,e=r[18].label+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p:D,d(n){n&&v(t)}}}function kd(r){let t,e=r[18].description+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p:D,d(n){n&&v(t)}}}function Cr(r){let t,e,n,s,o,a,l,i=lt(r[18].userInput),c=[];for(let u=0;u<i.length;u+=1)c[u]=_r(xr(r,i,u));const d=u=>$(c[u],1,1,()=>{c[u]=null});return s=new vt({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[Td]},$$scope:{ctx:r}}}),s.$on("click",function(){return r[16](r[18])}),a=new vt({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[Nd]},$$scope:{ctx:r}}}),a.$on("click",r[8]),{c(){t=k("div");for(let u=0;u<c.length;u+=1)c[u].c();e=T(),n=k("div"),_(s.$$.fragment),o=T(),_(a.$$.fragment),w(n,"class","user-input-actions svelte-8tbe79"),w(t,"class","user-input-container svelte-8tbe79")},m(u,f){y(u,t,f);for(let p=0;p<c.length;p+=1)c[p]&&c[p].m(t,null);b(t,e),b(t,n),C(s,n,null),b(n,o),C(a,n,null),l=!0},p(u,f){if(r=u,440&f){let h;for(i=lt(r[18].userInput),h=0;h<i.length;h+=1){const S=xr(r,i,h);c[h]?(c[h].p(S,f),m(c[h],1)):(c[h]=_r(S),c[h].c(),m(c[h],1),c[h].m(t,e))}for(q(),h=i.length;h<c.length;h+=1)d(h);B()}const p={};33554432&f&&(p.$$scope={dirty:f,ctx:r}),s.$set(p);const g={};33554432&f&&(g.$$scope={dirty:f,ctx:r}),a.$set(g)},i(u){if(!l){for(let f=0;f<i.length;f+=1)m(c[f]);m(s.$$.fragment,u),m(a.$$.fragment,u),l=!0}},o(u){c=c.filter(Boolean);for(let f=0;f<c.length;f+=1)$(c[f]);$(s.$$.fragment,u),$(a.$$.fragment,u),l=!1},d(u){u&&v(t),me(c,u),x(s),x(a)}}}function Md(r){let t,e=r[21].label+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p:D,d(n){n&&v(t)}}}function Ad(r){let t,e=r[21].description+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p:D,d(n){n&&v(t)}}}function _r(r){let t,e,n,s,o,a,l,i;e=new Y({props:{size:1,weight:"medium",color:"neutral",$$slots:{default:[Md]},$$scope:{ctx:r}}});let c=r[21].description&&function(p){let g,h;return g=new Y({props:{size:1,color:"secondary",$$slots:{default:[Ad]},$$scope:{ctx:p}}}),{c(){_(g.$$.fragment)},m(S,P){C(g,S,P),h=!0},p(S,P){const N={};33554432&P&&(N.$$scope={dirty:P,ctx:S}),g.$set(N)},i(S){h||(m(g.$$.fragment,S),h=!0)},o(S){$(g.$$.fragment,S),h=!1},d(S){x(g,S)}}}(r);function d(p){r[13](p,r[22])}function u(p){r[14](p,r[22])}let f={placeholder:r[21].placeholder||"",size:1,variant:"surface"};return r[3][r[22]]!==void 0&&(f.value=r[3][r[22]]),r[4][r[22]]!==void 0&&(f.textInput=r[4][r[22]]),o=new ye({props:f}),Ct.push(()=>_t(o,"value",d)),Ct.push(()=>_t(o,"textInput",u)),o.$on("keydown",function(...p){return r[15](r[18],...p)}),{c(){t=k("div"),_(e.$$.fragment),n=T(),c&&c.c(),s=T(),_(o.$$.fragment),w(t,"class","user-input-field svelte-8tbe79")},m(p,g){y(p,t,g),C(e,t,null),b(t,n),c&&c.m(t,null),b(t,s),C(o,t,null),i=!0},p(p,g){r=p;const h={};33554432&g&&(h.$$scope={dirty:g,ctx:r}),e.$set(h),r[21].description&&c.p(r,g);const S={};!a&&40&g&&(a=!0,S.value=r[3][r[22]],bt(()=>a=!1)),!l&&48&g&&(l=!0,S.textInput=r[4][r[22]],bt(()=>l=!1)),o.$set(S)},i(p){i||(m(e.$$.fragment,p),m(c),m(o.$$.fragment,p),i=!0)},o(p){$(e.$$.fragment,p),$(c),$(o.$$.fragment,p),i=!1},d(p){p&&v(t),x(e),c&&c.d(),x(o)}}}function Td(r){let t;return{c(){t=I("Install")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Nd(r){let t;return{c(){t=I("Cancel")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Ed(r){let t,e,n,s,o,a;n=new Y({props:{size:1,weight:"medium",$$slots:{default:[Sd]},$$scope:{ctx:r}}});let l=r[18].description&&function(c){let d,u;return d=new Y({props:{size:1,color:"secondary",$$slots:{default:[kd]},$$scope:{ctx:c}}}),{c(){_(d.$$.fragment)},m(f,p){C(d,f,p),u=!0},p(f,p){const g={};33554432&p&&(g.$$scope={dirty:p,ctx:f}),d.$set(g)},i(f){u||(m(d.$$.fragment,f),u=!0)},o(f){$(d.$$.fragment,f),u=!1},d(f){x(d,f)}}}(r),i=r[2]===r[18].value&&r[18].userInput&&Cr(r);return{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),l&&l.c(),o=T(),i&&i.c(),w(e,"class","mcp-service-title svelte-8tbe79"),w(t,"slot","header-left"),w(t,"class","mcp-service-info svelte-8tbe79")},m(c,d){y(c,t,d),b(t,e),C(n,e,null),b(t,s),l&&l.m(t,null),b(t,o),i&&i.m(t,null),a=!0},p(c,d){const u={};33554432&d&&(u.$$scope={dirty:d,ctx:c}),n.$set(u),c[18].description&&l.p(c,d),c[2]===c[18].value&&c[18].userInput?i?(i.p(c,d),4&d&&m(i,1)):(i=Cr(c),i.c(),m(i,1),i.m(t,null)):i&&(q(),$(i,1,1,()=>{i=null}),B())},i(c){a||(m(n.$$.fragment,c),m(l),m(i),a=!0)},o(c){$(n.$$.fragment,c),$(l),$(i),a=!1},d(c){c&&v(t),x(n),l&&l.d(),i&&i.d()}}}function Id(r){let t,e;return t=new vt({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[Pd]},$$scope:{ctx:r}}}),t.$on("click",function(){return r[12](r[18])}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){r=n;const o={};33554432&s&&(o.$$scope={dirty:s,ctx:r}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Rd(r){let t,e,n;return e=new Jn.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Ld]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),w(t,"class","installed-indicator svelte-8tbe79")},m(s,o){y(s,t,o),C(e,t,null),n=!0},p(s,o){const a={};33554432&o&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(m(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&v(t),x(e)}}}function Pd(r){let t;return{c(){t=k("span"),t.textContent="+"},m(e,n){y(e,t,n)},p:D,d(e){e&&v(t)}}}function Ld(r){let t;return{c(){t=I("Installed")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function zd(r){let t,e,n,s,o;function a(...d){return r[11](r[18],...d)}const l=[Rd,Id],i=[];function c(d,u){return 1&u&&(e=null),e==null&&(e=!!d[0].some(a)),e?0:1}return n=c(r,-1),s=i[n]=l[n](r),{c(){t=k("div"),s.c(),w(t,"slot","header-right"),w(t,"class","mcp-service-actions svelte-8tbe79")},m(d,u){y(d,t,u),i[n].m(t,null),o=!0},p(d,u){let f=n;n=c(r=d,u),n===f?i[n].p(r,u):(q(),$(i[f],1,1,()=>{i[f]=null}),B(),s=i[n],s?s.p(r,u):(s=i[n]=l[n](r),s.c()),m(s,1),s.m(t,null))},i(d){o||(m(s),o=!0)},o(d){$(s),o=!1},d(d){d&&v(t),i[n].d()}}}function br(r){let t,e,n,s;return e=new Kt({props:{$$slots:{"header-right":[zd],"header-left":[Ed]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),n=T(),w(t,"class","mcp-service-item")},m(o,a){y(o,t,a),C(e,t,null),b(t,n),s=!0},p(o,a){const l={};33554461&a&&(l.$$scope={dirty:a,ctx:o}),e.$set(l)},i(o){s||(m(e.$$.fragment,o),s=!0)},o(o){$(e.$$.fragment,o),s=!1},d(o){o&&v(t),x(e)}}}function Od(r){let t,e,n,s=lt(r[5]),o=[];for(let l=0;l<s.length;l+=1)o[l]=br(wr(r,s,l));const a=l=>$(o[l],1,1,()=>{o[l]=null});return{c(){t=k("div"),e=k("div");for(let l=0;l<o.length;l+=1)o[l].c();w(e,"class","mcp-list-container svelte-8tbe79"),w(t,"class","mcp-install-content svelte-8tbe79")},m(l,i){y(l,t,i),b(t,e);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(e,null);n=!0},p(l,i){if(509&i){let c;for(s=lt(l[5]),c=0;c<s.length;c+=1){const d=wr(l,s,c);o[c]?(o[c].p(d,i),m(o[c],1)):(o[c]=br(d),o[c].c(),m(o[c],1),o[c].m(e,null))}for(q(),c=s.length;c<o.length;c+=1)a(c);B()}},i(l){if(!n){for(let i=0;i<s.length;i+=1)m(o[i]);n=!0}},o(l){o=o.filter(Boolean);for(let i=0;i<o.length;i+=1)$(o[i]);n=!1},d(l){l&&v(t),me(o,l)}}}function Zd(r){let t;return{c(){t=I("Easy MCP Installation")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Fd(r){let t,e,n,s,o;return e=new ca({}),s=new Y({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Zd]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),n=T(),_(s.$$.fragment),w(t,"slot","header-left"),w(t,"class","mcp-install-left svelte-8tbe79")},m(a,l){y(a,t,l),C(e,t,null),b(t,n),C(s,t,null),o=!0},p(a,l){const i={};33554432&l&&(i.$$scope={dirty:l,ctx:a}),s.$set(i)},i(a){o||(m(e.$$.fragment,a),m(s.$$.fragment,a),o=!0)},o(a){$(e.$$.fragment,a),$(s.$$.fragment,a),o=!1},d(a){a&&v(t),x(e),x(s)}}}function jd(r){let t,e,n;return e=new Kt({props:{$$slots:{"header-left":[Fd]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),w(t,"slot","header"),w(t,"class","mcp-install-header svelte-8tbe79")},m(s,o){y(s,t,o),C(e,t,null),n=!0},p(s,o){const a={};33554432&o&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(m(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&v(t),x(e)}}}function Dd(r){let t,e,n,s;function o(l){r[17](l)}let a={$$slots:{header:[jd],default:[Od]},$$scope:{ctx:r}};return r[1]!==void 0&&(a.collapsed=r[1]),e=new oo({props:a}),Ct.push(()=>_t(e,"collapsed",o)),{c(){t=k("div"),_(e.$$.fragment),w(t,"class","mcp-install-wrapper svelte-8tbe79")},m(l,i){y(l,t,i),C(e,t,null),s=!0},p(l,[i]){const c={};33554461&i&&(c.$$scope={dirty:i,ctx:l}),!n&&2&i&&(n=!0,c.collapsed=l[1],bt(()=>n=!1)),e.$set(c)},i(l){s||(m(e.$$.fragment,l),s=!0)},o(l){$(e.$$.fragment,l),s=!1},d(l){l&&v(t),x(e)}}}const Zn="easyMCPInstall.collapsed";function Ud(r,t,e){let{onMCPServerAdd:n}=t,{servers:s=[]}=t,o=!1,a=!1,l=null,i={},c={};function d(p){if(s.some(h=>h.name===p.label))return;if(p.userInput&&p.userInput.length>0)return e(3,i={}),p.userInput.forEach((h,S)=>{let P;P=h.type==="environmentVariable"&&h.envVarName?h.envVarName:h.correspondingArg?h.correspondingArg:`input_${S}`,e(3,i[P]=h.defaultValue||"",i)}),void e(2,l=p.value);const g={type:"stdio",name:p.label,command:p.command,arguments:"",useShellInterpolation:!0};n&&n(g)}function u(p){var P;if(!p.userInput)return;for(let N=0;N<p.userInput.length;N++){const M=p.userInput[N];let A;if(A=M.type==="environmentVariable"&&M.envVarName?M.envVarName:M.correspondingArg?M.correspondingArg:`input_${N}`,!((P=i[A])==null?void 0:P.trim())){const z=c[A];return void(z&&z.focus())}}let g=[p.command],h={};p.args&&g.push(...p.args);for(let N=0;N<p.userInput.length;N++){const M=p.userInput[N];let A;A=M.type==="environmentVariable"&&M.envVarName?M.envVarName:M.correspondingArg?M.correspondingArg:`input_${N}`;const E=i[A].trim(),z=`"${E}"`;if(M.type==="environmentVariable"&&M.envVarName)h[M.envVarName]=E;else if(M.correspondingArg){const G=g.indexOf(M.correspondingArg);G!==-1?g.splice(G+1,0,z):g.push(M.correspondingArg,z)}else g.push(z)}const S={type:"stdio",name:p.label,command:g.join(" "),arguments:"",useShellInterpolation:!0,env:Object.keys(h).length>0?h:void 0};n&&n(S),e(2,l=null),e(3,i={})}function f(){e(2,l=null),e(3,i={})}r.$$set=p=>{"onMCPServerAdd"in p&&e(9,n=p.onMCPServerAdd),"servers"in p&&e(0,s=p.servers)},r.$$.update=()=>{1026&r.$$.dirty&&typeof window<"u"&&a&&localStorage.setItem(Zn,JSON.stringify(o))};{const p=localStorage.getItem(Zn);if(p!==null)try{e(1,o=JSON.parse(p))}catch{localStorage.removeItem(Zn)}e(10,a=!0)}return[s,o,l,i,c,[{value:"redis",label:"Redis",description:"Real-time data platform for building fast apps",command:"uvx",args:["--from","git+https://github.com/redis/mcp-redis.git","redis-mcp-server","--url"],userInput:[{label:"Redis connection URL",description:"Enter your connection URL (redis://localhost:6379/0)",placeholder:"rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>",correspondingArg:"--url",type:"argument"}]},{value:"mongodb",label:"MongoDB",description:"Optimize database queries and performance.",command:"npx",args:["-y","mongodb-mcp-server","--connectionString"],userInput:[{label:"MongoDB Connection String",description:"Enter your MongoDB connection string",placeholder:"********************************:port/database",correspondingArg:"--connectionString",type:"argument"}]},{value:"circleci",label:"CircleCI",description:"Debug builds and improve CI/CD pipelines.",command:"npx",args:["-y","@circleci/mcp-server-circleci"],userInput:[{label:"CircleCI Token",description:"Enter your CircleCI token",placeholder:"YOUR_CIRCLE_CI_TOKEN",type:"environmentVariable",envVarName:"CIRCLECI_TOKEN"},{label:"Base URL",description:"Enter the base URL for your CircleCI instance",placeholder:"https://circleci.com",defaultValue:"https://circleci.com",type:"environmentVariable",envVarName:"CIRCLECI_BASE_URL"}]},{value:"context7",label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{value:"playwright",label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{value:"sequential-thinking",label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],d,u,f,n,a,(p,g)=>g.name===p.label,p=>d(p),function(p,g){r.$$.not_equal(i[g],p)&&(i[g]=p,e(3,i))},function(p,g){r.$$.not_equal(c[g],p)&&(c[g]=p,e(4,c))},(p,g)=>{g.key==="Enter"?u(p):g.key==="Escape"&&f()},p=>u(p),function(p){o=p,e(1,o)}]}class Vd extends ot{constructor(t){super(),at(this,t,Ud,Dd,rt,{onMCPServerAdd:9,servers:0})}}const qd={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},Bd={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},Hd=Po(),Gd=new class{constructor(r){it(this,"strings");let t={[Rn.vscode]:{},[Rn.jetbrains]:Bd,[Rn.web]:{}};this.strings={...qd,...t[r]}}get(r){return this.strings[r]}}(Hd.clientType);function Sr(r,t,e){const n=r.slice();return n[29]=t[e],n}function Jd(r){let t;return{c(){t=k("div"),t.textContent="MCP",w(t,"class","section-heading-text")},m(e,n){y(e,t,n)},p:D,d(e){e&&v(t)}}}function kr(r,t){let e,n,s;return n=new xo({props:{mode:t[1]===t[29].id?"edit":"view",server:t[29],onAdd:t[9],onSave:t[10],onDelete:t[12],onToggleDisableServer:t[13],onEdit:t[8],onCancel:t[6],onJSONImport:t[11],disabledText:t[4].errors.get(t[29].id),warningText:t[4].warnings.get(t[29].id)}}),{key:r,first:null,c(){e=ft(),_(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),C(n,o,a),s=!0},p(o,a){t=o;const l={};10&a[0]&&(l.mode=t[1]===t[29].id?"edit":"view"),8&a[0]&&(l.server=t[29]),24&a[0]&&(l.disabledText=t[4].errors.get(t[29].id)),24&a[0]&&(l.warningText=t[4].warnings.get(t[29].id)),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(n,o)}}}function Mr(r){let t,e;return t=new xo({props:{mode:r[2],onAdd:r[9],onSave:r[10],onDelete:r[12],onToggleDisableServer:r[13],onEdit:r[8],onCancel:r[6],onJSONImport:r[11]}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};4&s[0]&&(o.mode=n[2]),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Wd(r){let t;return{c(){t=I("Add MCP")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Kd(r){let t,e;return t=new sn({props:{slot:"iconLeft"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Yd(r){let t;return{c(){t=I("Add remote MCP")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Xd(r){let t,e;return t=new sn({props:{slot:"iconLeft"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Ar(r){let t,e;return t=new vt({props:{disabled:r[5],color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$slots:{iconLeft:[tu],default:[Qd]},$$scope:{ctx:r}}}),t.$on("click",r[24]),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};32&s[0]&&(o.disabled=n[5]),2&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Qd(r){let t;return{c(){t=I("Import from JSON")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function tu(r){let t,e;return t=new Yr({props:{slot:"iconLeft"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function eu(r){let t,e,n,s,o,a,l,i,c,d,u,f,p,g,h,S,P,N,M,A,E=[],z=new Map;n=new Y({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Jd]},$$scope:{ctx:r}}}),u=new Vd({props:{onMCPServerAdd:r[9],servers:r[3]}});let G=lt(r[3]);const tt=R=>R[29].id;for(let R=0;R<G.length;R+=1){let V=Sr(r,G,R),st=tt(V);z.set(st,E[R]=kr(st,V))}let O=(r[2]==="add"||r[2]==="addJson"||r[2]==="addRemote")&&Mr(r);S=new vt({props:{disabled:r[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[Kd],default:[Wd]},$$scope:{ctx:r}}}),S.$on("click",r[22]),N=new vt({props:{disabled:r[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[Xd],default:[Yd]},$$scope:{ctx:r}}}),N.$on("click",r[23]);let F=r[0]&&Ar(r);return{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),o=k("div"),a=I(`Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP `),l=k("a"),i=I("in the docs"),c=I("."),d=T(),_(u.$$.fragment),f=T();for(let R=0;R<E.length;R+=1)E[R].c();p=T(),O&&O.c(),g=T(),h=k("div"),_(S.$$.fragment),P=T(),_(N.$$.fragment),M=T(),F&&F.c(),w(e,"class","section-heading svelte-1vnq4q3"),w(l,"href",r[14]),w(o,"class","description-text svelte-1vnq4q3"),w(t,"class","mcp-servers svelte-1vnq4q3"),w(h,"class","add-mcp-button-container svelte-1vnq4q3")},m(R,V){y(R,t,V),b(t,e),C(n,e,null),b(t,s),b(t,o),b(o,a),b(o,l),b(l,i),b(o,c),b(t,d),C(u,t,null),b(t,f);for(let st=0;st<E.length;st+=1)E[st]&&E[st].m(t,null);y(R,p,V),O&&O.m(R,V),y(R,g,V),y(R,h,V),C(S,h,null),b(h,P),C(N,h,null),b(h,M),F&&F.m(h,null),A=!0},p(R,V){const st={};2&V[1]&&(st.$$scope={dirty:V,ctx:R}),n.$set(st);const Z={};8&V[0]&&(Z.servers=R[3]),u.$set(Z),16218&V[0]&&(G=lt(R[3]),q(),E=Ht(E,V,tt,1,R,G,z,t,Gt,kr,null,Sr),B()),R[2]==="add"||R[2]==="addJson"||R[2]==="addRemote"?O?(O.p(R,V),4&V[0]&&m(O,1)):(O=Mr(R),O.c(),m(O,1),O.m(g.parentNode,g)):O&&(q(),$(O,1,1,()=>{O=null}),B());const J={};32&V[0]&&(J.disabled=R[5]),2&V[1]&&(J.$$scope={dirty:V,ctx:R}),S.$set(J);const j={};32&V[0]&&(j.disabled=R[5]),2&V[1]&&(j.$$scope={dirty:V,ctx:R}),N.$set(j),R[0]?F?(F.p(R,V),1&V[0]&&m(F,1)):(F=Ar(R),F.c(),m(F,1),F.m(h,null)):F&&(q(),$(F,1,1,()=>{F=null}),B())},i(R){if(!A){m(n.$$.fragment,R),m(u.$$.fragment,R);for(let V=0;V<G.length;V+=1)m(E[V]);m(O),m(S.$$.fragment,R),m(N.$$.fragment,R),m(F),A=!0}},o(R){$(n.$$.fragment,R),$(u.$$.fragment,R);for(let V=0;V<E.length;V+=1)$(E[V]);$(O),$(S.$$.fragment,R),$(N.$$.fragment,R),$(F),A=!1},d(R){R&&(v(t),v(p),v(g),v(h)),x(n),x(u);for(let V=0;V<E.length;V+=1)E[V].d();O&&O.d(R),x(S),x(N),F&&F.d()}}}function nu(r,t,e){let n,s,o,{onMCPServerAdd:a}=t,{onMCPServerSave:l}=t,{onMCPServerDelete:i}=t,{onMCPServerToggleDisable:c}=t,{onCancel:d}=t,{onMCPServerJSONImport:u}=t,{isMCPImportEnabled:f=!0}=t,p=null,g=null,h=[];const S=Pe(Ue.key),P=ts(),N=S.getEnableNativeRemoteMcp(),M=P.getServers();function A(R){return async function(...V){const st=await R(...V);return e(2,g=null),e(1,p=null),st}}Et(r,M,R=>e(21,o=R));const E=A(a),z=A(l),G=A(u),tt=A(i),O=A(c),F=Gd.get("mcpDocsURL");return r.$$set=R=>{"onMCPServerAdd"in R&&e(15,a=R.onMCPServerAdd),"onMCPServerSave"in R&&e(16,l=R.onMCPServerSave),"onMCPServerDelete"in R&&e(17,i=R.onMCPServerDelete),"onMCPServerToggleDisable"in R&&e(18,c=R.onMCPServerToggleDisable),"onCancel"in R&&e(19,d=R.onCancel),"onMCPServerJSONImport"in R&&e(20,u=R.onMCPServerJSONImport),"isMCPImportEnabled"in R&&e(0,f=R.isMCPImportEnabled)},r.$$.update=()=>{6&r.$$.dirty[0]&&e(5,n=g==="add"||g==="addJson"||g==="addRemote"||p!==null),2097152&r.$$.dirty[0]&&e(3,h=N?Go(o):o),8&r.$$.dirty[0]&&e(4,s=on.parseServerValidationMessages(h))},[f,p,g,h,s,n,function(){e(1,p=null),e(2,g=null),d==null||d()},M,function(R){e(1,p=R.id)},E,z,G,tt,O,F,a,l,i,c,d,u,o,()=>{e(2,g="add")},()=>{e(2,g="addRemote")},()=>{e(2,g="addJson")}]}class su extends ot{constructor(t){super(),at(this,t,nu,eu,rt,{onMCPServerAdd:15,onMCPServerSave:16,onMCPServerDelete:17,onMCPServerToggleDisable:18,onCancel:19,onMCPServerJSONImport:20,isMCPImportEnabled:0},null,[-1,-1])}}function Tr(r,t,e){const n=r.slice();return n[12]=t[e],n}function ru(r){let t;return{c(){t=k("div"),t.textContent="Terminal",w(t,"class","section-heading-text")},m(e,n){y(e,t,n)},p:D,d(e){e&&v(t)}}}function ou(r){let t;return{c(){t=I("Shell:")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function au(r){let t;return{c(){t=I("Select a shell")},m(e,n){y(e,t,n)},p:D,d(e){e&&v(t)}}}function iu(r){let t;return{c(){t=I("No shells available")},m(e,n){y(e,t,n)},p:D,d(e){e&&v(t)}}}function lu(r){let t,e,n,s,o=r[5].friendlyName+"",a=r[5].supportString+"";return{c(){t=I(o),e=I(`
            (`),n=I(a),s=I(")")},m(l,i){y(l,t,i),y(l,e,i),y(l,n,i),y(l,s,i)},p(l,i){32&i&&o!==(o=l[5].friendlyName+"")&&ct(t,o),32&i&&a!==(a=l[5].supportString+"")&&ct(n,a)},d(l){l&&(v(t),v(e),v(n),v(s))}}}function cu(r){let t;function e(o,a){return o[5]&&o[1].length>0?lu:o[1].length===0?iu:au}let n=e(r),s=n(r);return{c(){s.c(),t=ft()},m(o,a){s.m(o,a),y(o,t,a)},p(o,a){n===(n=e(o))&&s?s.p(o,a):(s.d(1),s=n(o),s&&(s.c(),s.m(t.parentNode,t)))},d(o){o&&v(t),s.d(o)}}}function du(r){let t,e;return t=new Jo({props:{slot:"iconRight"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function uu(r){let t,e;return t=new vt({props:{size:1,variant:"outline",color:"neutral",disabled:r[1].length===0,$$slots:{iconRight:[du],default:[cu]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};2&s&&(o.disabled=n[1].length===0),32802&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function pu(r){let t,e;return t=new wt.Label({props:{$$slots:{default:[fu]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};32768&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function mu(r){let t,e,n=[],s=new Map,o=lt(r[1]);const a=l=>l[12].friendlyName;for(let l=0;l<o.length;l+=1){let i=Tr(r,o,l),c=a(i);s.set(c,n[l]=Nr(c,i))}return{c(){for(let l=0;l<n.length;l+=1)n[l].c();t=ft()},m(l,i){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(l,i);y(l,t,i),e=!0},p(l,i){30&i&&(o=lt(l[1]),q(),n=Ht(n,i,a,1,l,o,s,t.parentNode,Gt,Nr,t,Tr),B())},i(l){if(!e){for(let i=0;i<o.length;i+=1)m(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)$(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d(l)}}}function fu(r){let t;return{c(){t=I("No shells available")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function $u(r){let t,e,n,s,o=r[12].friendlyName+"",a=r[12].supportString+"";return{c(){t=I(o),e=I(`
              (`),n=I(a),s=I(`)
            `)},m(l,i){y(l,t,i),y(l,e,i),y(l,n,i),y(l,s,i)},p(l,i){2&i&&o!==(o=l[12].friendlyName+"")&&ct(t,o),2&i&&a!==(a=l[12].supportString+"")&&ct(n,a)},d(l){l&&(v(t),v(e),v(n),v(s))}}}function Nr(r,t){let e,n,s;function o(){return t[8](t[12])}return n=new wt.Item({props:{onSelect:o,highlight:t[2]===t[12].friendlyName,$$slots:{default:[$u]},$$scope:{ctx:t}}}),{key:r,first:null,c(){e=ft(),_(n.$$.fragment),this.first=e},m(a,l){y(a,e,l),C(n,a,l),s=!0},p(a,l){t=a;const i={};26&l&&(i.onSelect=o),6&l&&(i.highlight=t[2]===t[12].friendlyName),32770&l&&(i.$$scope={dirty:l,ctx:t}),n.$set(i)},i(a){s||(m(n.$$.fragment,a),s=!0)},o(a){$(n.$$.fragment,a),s=!1},d(a){a&&v(e),x(n,a)}}}function gu(r){let t,e,n,s;const o=[mu,pu],a=[];function l(i,c){return i[1].length>0?0:1}return t=l(r),e=a[t]=o[t](r),{c(){e.c(),n=ft()},m(i,c){a[t].m(i,c),y(i,n,c),s=!0},p(i,c){let d=t;t=l(i),t===d?a[t].p(i,c):(q(),$(a[d],1,1,()=>{a[d]=null}),B(),e=a[t],e?e.p(i,c):(e=a[t]=o[t](i),e.c()),m(e,1),e.m(n.parentNode,n))},i(i){s||(m(e),s=!0)},o(i){$(e),s=!1},d(i){i&&v(n),a[t].d(i)}}}function hu(r){let t,e,n,s;return t=new wt.Trigger({props:{$$slots:{default:[uu]},$$scope:{ctx:r}}}),n=new wt.Content({props:{side:"bottom",align:"start",$$slots:{default:[gu]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment),e=T(),_(n.$$.fragment)},m(o,a){C(t,o,a),y(o,e,a),C(n,o,a),s=!0},p(o,a){const l={};32802&a&&(l.$$scope={dirty:a,ctx:o}),t.$set(l);const i={};32798&a&&(i.$$scope={dirty:a,ctx:o}),n.$set(i)},i(o){s||(m(t.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){$(t.$$.fragment,o),$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(t,o),x(n,o)}}}function vu(r){let t;return{c(){t=I("Start-up script: Code to run wherever a new terminal is opened")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function yu(r){let t,e,n,s,o,a,l,i,c,d,u,f,p,g,h;function S(A){r[9](A)}e=new Y({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[ru]},$$scope:{ctx:r}}}),o=new Y({props:{size:1,$$slots:{default:[ou]},$$scope:{ctx:r}}});let P={$$slots:{default:[hu]},$$scope:{ctx:r}};function N(A){r[10](A)}r[4]!==void 0&&(P.requestClose=r[4]),l=new wt.Root({props:P}),Ct.push(()=>_t(l,"requestClose",S)),u=new Y({props:{size:1,$$slots:{default:[vu]},$$scope:{ctx:r}}});let M={placeholder:"Enter shell commands to run on terminal startup",resize:"vertical"};return r[0]!==void 0&&(M.value=r[0]),p=new ro({props:M}),Ct.push(()=>_t(p,"value",N)),p.$on("change",r[6]),{c(){t=k("div"),_(e.$$.fragment),n=T(),s=k("div"),_(o.$$.fragment),a=T(),_(l.$$.fragment),c=T(),d=k("div"),_(u.$$.fragment),f=T(),_(p.$$.fragment),w(s,"class","shell-selector svelte-dndd5n"),w(d,"class","startup-script-container svelte-dndd5n"),w(t,"class","terminal-settings svelte-dndd5n")},m(A,E){y(A,t,E),C(e,t,null),b(t,n),b(t,s),C(o,s,null),b(s,a),C(l,s,null),b(t,c),b(t,d),C(u,d,null),b(d,f),C(p,d,null),h=!0},p(A,[E]){const z={};32768&E&&(z.$$scope={dirty:E,ctx:A}),e.$set(z);const G={};32768&E&&(G.$$scope={dirty:E,ctx:A}),o.$set(G);const tt={};32830&E&&(tt.$$scope={dirty:E,ctx:A}),!i&&16&E&&(i=!0,tt.requestClose=A[4],bt(()=>i=!1)),l.$set(tt);const O={};32768&E&&(O.$$scope={dirty:E,ctx:A}),u.$set(O);const F={};!g&&1&E&&(g=!0,F.value=A[0],bt(()=>g=!1)),p.$set(F)},i(A){h||(m(e.$$.fragment,A),m(o.$$.fragment,A),m(l.$$.fragment,A),m(u.$$.fragment,A),m(p.$$.fragment,A),h=!0)},o(A){$(e.$$.fragment,A),$(o.$$.fragment,A),$(l.$$.fragment,A),$(u.$$.fragment,A),$(p.$$.fragment,A),h=!1},d(A){A&&v(t),x(e),x(o),x(l),x(u),x(p)}}}function wu(r,t,e){let n,s,{supportedShells:o=[]}=t,{selectedShell:a}=t,{startupScript:l}=t,{onShellSelect:i}=t,{onStartupScriptChange:c}=t;return r.$$set=d=>{"supportedShells"in d&&e(1,o=d.supportedShells),"selectedShell"in d&&e(2,a=d.selectedShell),"startupScript"in d&&e(0,l=d.startupScript),"onShellSelect"in d&&e(3,i=d.onShellSelect),"onStartupScriptChange"in d&&e(7,c=d.onStartupScriptChange)},r.$$.update=()=>{var d;4&r.$$.dirty&&e(5,n=a?(d=a,o.find(u=>u.friendlyName===d)):void 0)},[l,o,a,i,s,n,function(d){const u=d.target;c(u.value)},c,d=>{i(d.friendlyName),s()},function(d){s=d,e(4,s)},function(d){l=d,e(0,l)}]}class xu extends ot{constructor(t){super(),at(this,t,wu,yu,rt,{supportedShells:1,selectedShell:2,startupScript:0,onShellSelect:3,onStartupScriptChange:7})}}function Cu(r){let t;return{c(){t=k("div"),t.textContent="Sound Settings",w(t,"class","section-heading-text")},m(e,n){y(e,t,n)},p:D,d(e){e&&v(t)}}}function _u(r){let t;return{c(){t=I("Enable Sound Effects")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function bu(r){let t;return{c(){t=I("Play a sound when an agent completes a task")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Su(r){let t,e,n,s,o,a,l;return n=new Y({props:{size:2,weight:"medium",$$slots:{default:[_u]},$$scope:{ctx:r}}}),a=new Y({props:{size:1,weight:"medium",$$slots:{default:[bu]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),o=k("div"),_(a.$$.fragment),w(t,"class","c-sound-setting__info svelte-8awonv"),w(t,"slot","header-left")},m(i,c){y(i,t,c),b(t,e),C(n,e,null),b(t,s),b(t,o),C(a,o,null),l=!0},p(i,c){const d={};64&c&&(d.$$scope={dirty:c,ctx:i}),n.$set(d);const u={};64&c&&(u.$$scope={dirty:c,ctx:i}),a.$set(u)},i(i){l||(m(n.$$.fragment,i),m(a.$$.fragment,i),l=!0)},o(i){$(n.$$.fragment,i),$(a.$$.fragment,i),l=!1},d(i){i&&v(t),x(n),x(a)}}}function ku(r){let t,e,n;return e=new Wn({props:{size:1,checked:r[0]}}),e.$on("change",r[5]),{c(){t=k("div"),_(e.$$.fragment),w(t,"slot","header-right")},m(s,o){y(s,t,o),C(e,t,null),n=!0},p(s,o){const a={};1&o&&(a.checked=s[0]),e.$set(a)},i(s){n||(m(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&v(t),x(e)}}}function Er(r){let t,e;return t=new Kt({props:{$$slots:{"header-right":[Iu],"header-left":[Tu]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};65&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Mu(r){let t;return{c(){t=I("Test Sound")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Au(r){let t;return{c(){t=I("Play a sample of the agent completion sound")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Tu(r){let t,e,n,s,o,a,l;return n=new Y({props:{size:2,weight:"medium",$$slots:{default:[Mu]},$$scope:{ctx:r}}}),a=new Y({props:{size:1,weight:"medium",$$slots:{default:[Au]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),o=k("div"),_(a.$$.fragment),w(t,"class","c-sound-setting__info svelte-8awonv"),w(t,"slot","header-left")},m(i,c){y(i,t,c),b(t,e),C(n,e,null),b(t,s),b(t,o),C(a,o,null),l=!0},p(i,c){const d={};64&c&&(d.$$scope={dirty:c,ctx:i}),n.$set(d);const u={};64&c&&(u.$$scope={dirty:c,ctx:i}),a.$set(u)},i(i){l||(m(n.$$.fragment,i),m(a.$$.fragment,i),l=!0)},o(i){$(n.$$.fragment,i),$(a.$$.fragment,i),l=!1},d(i){i&&v(t),x(n),x(a)}}}function Nu(r){let t;return{c(){t=I("Play")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Eu(r){let t,e;return t=new ia({props:{size:1,defaultColor:"neutral",enabled:r[0],stickyColor:!1,disabled:!r[0],onClick:r[3],tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},$$slots:{default:[Nu]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};1&s&&(o.enabled=n[0]),1&s&&(o.disabled=!n[0]),64&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Iu(r){let t,e,n;return e=new ee({props:{content:r[0]?"":"Enable sound effects to test",triggerOn:[Kn.Hover],$$slots:{default:[Eu]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),w(t,"slot","header-right")},m(s,o){y(s,t,o),C(e,t,null),n=!0},p(s,o){const a={};1&o&&(a.content=s[0]?"":"Enable sound effects to test"),65&o&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(m(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&v(t),x(e)}}}function Ru(r){let t,e,n,s,o,a;t=new Y({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Cu]},$$scope:{ctx:r}}}),s=new Kt({props:{$$slots:{"header-right":[ku],"header-left":[Su]},$$scope:{ctx:r}}});let l=r[0]&&Er(r);return{c(){_(t.$$.fragment),e=T(),n=k("div"),_(s.$$.fragment),o=T(),l&&l.c(),w(n,"class","c-sound-settings svelte-8awonv")},m(i,c){C(t,i,c),y(i,e,c),y(i,n,c),C(s,n,null),b(n,o),l&&l.m(n,null),a=!0},p(i,[c]){const d={};64&c&&(d.$$scope={dirty:c,ctx:i}),t.$set(d);const u={};65&c&&(u.$$scope={dirty:c,ctx:i}),s.$set(u),i[0]?l?(l.p(i,c),1&c&&m(l,1)):(l=Er(i),l.c(),m(l,1),l.m(n,null)):l&&(q(),$(l,1,1,()=>{l=null}),B())},i(i){a||(m(t.$$.fragment,i),m(s.$$.fragment,i),m(l),a=!0)},o(i){$(t.$$.fragment,i),$(s.$$.fragment,i),$(l),a=!1},d(i){i&&(v(e),v(n)),x(t,i),x(s),l&&l.d()}}}function Pu(r,t,e){let n,s,o,a=D;r.$$.on_destroy.push(()=>a());const l=Pe(jn.key);return r.$$.update=()=>{16&r.$$.dirty&&e(0,s=o.enabled)},e(1,n=l.getCurrentSettings),a(),a=nn(n,i=>e(4,o=i)),[s,n,l,async function(){return await l.playAgentComplete(),"success"},o,()=>l.updateEnabled(!s)]}class Lu extends ot{constructor(t){super(),at(this,t,Pu,Ru,rt,{})}}function Ir(r){let t,e,n,s,o;return t=new Y({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[zu]},$$scope:{ctx:r}}}),s=new Kt({props:{$$slots:{"header-right":[Du],"header-left":[ju]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment),e=T(),n=k("div"),_(s.$$.fragment),w(n,"class","c-agent-settings svelte-mv39d5")},m(a,l){C(t,a,l),y(a,e,l),y(a,n,l),C(s,n,null),o=!0},p(a,l){const i={};128&l&&(i.$$scope={dirty:l,ctx:a}),t.$set(i);const c={};132&l&&(c.$$scope={dirty:l,ctx:a}),s.$set(c)},i(a){o||(m(t.$$.fragment,a),m(s.$$.fragment,a),o=!0)},o(a){$(t.$$.fragment,a),$(s.$$.fragment,a),o=!1},d(a){a&&(v(e),v(n)),x(t,a),x(s)}}}function zu(r){let t;return{c(){t=k("div"),t.textContent="Agent Settings",w(t,"class","section-heading-text")},m(e,n){y(e,t,n)},p:D,d(e){e&&v(t)}}}function Ou(r){let t;return{c(){t=I("Enable Swarm Mode")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Zu(r){let t;return{c(){t=I("Allow agents to coordinate and work together on complex tasks")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Fu(r){let t;return{c(){t=I(`Sub-agents run in isolated remote environments and communicate via git repositories.
            Each remote agent consumes credits from your account. Changes are reviewed before being
            applied to your local workspace.`)},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function ju(r){let t,e,n,s,o,a,l,i,c,d;return n=new Y({props:{size:2,weight:"medium",$$slots:{default:[Ou]},$$scope:{ctx:r}}}),a=new Y({props:{size:1,weight:"medium",$$slots:{default:[Zu]},$$scope:{ctx:r}}}),c=new Y({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Fu]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),o=k("div"),_(a.$$.fragment),l=T(),i=k("div"),_(c.$$.fragment),w(i,"class","c-agent-setting__education svelte-mv39d5"),w(t,"class","c-agent-setting__info svelte-mv39d5"),w(t,"slot","header-left")},m(u,f){y(u,t,f),b(t,e),C(n,e,null),b(t,s),b(t,o),C(a,o,null),b(t,l),b(t,i),C(c,i,null),d=!0},p(u,f){const p={};128&f&&(p.$$scope={dirty:f,ctx:u}),n.$set(p);const g={};128&f&&(g.$$scope={dirty:f,ctx:u}),a.$set(g);const h={};128&f&&(h.$$scope={dirty:f,ctx:u}),c.$set(h)},i(u){d||(m(n.$$.fragment,u),m(a.$$.fragment,u),m(c.$$.fragment,u),d=!0)},o(u){$(n.$$.fragment,u),$(a.$$.fragment,u),$(c.$$.fragment,u),d=!1},d(u){u&&v(t),x(n),x(a),x(c)}}}function Du(r){let t,e,n;return e=new Wn({props:{size:1,checked:r[2]}}),e.$on("change",r[6]),{c(){t=k("div"),_(e.$$.fragment),w(t,"slot","header-right")},m(s,o){y(s,t,o),C(e,t,null),n=!0},p(s,o){const a={};4&o&&(a.checked=s[2]),e.$set(a)},i(s){n||(m(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&v(t),x(e)}}}function Uu(r){let t,e,n=r[0]&&r[1]&&Ir(r);return{c(){n&&n.c(),t=ft()},m(s,o){n&&n.m(s,o),y(s,t,o),e=!0},p(s,[o]){s[0]&&s[1]?n?(n.p(s,o),3&o&&m(n,1)):(n=Ir(s),n.c(),m(n,1),n.m(t.parentNode,t)):n&&(q(),$(n,1,1,()=>{n=null}),B())},i(s){e||(m(n),e=!0)},o(s){$(n),e=!1},d(s){s&&v(t),n&&n.d(s)}}}function Vu(r,t,e){let n,s,o,a=D;r.$$.on_destroy.push(()=>a());let{isSwarmModeEnabled:l=!1}=t,{hasEverUsedRemoteAgent:i=!1}=t;const c=Pe(en.key);return r.$$set=d=>{"isSwarmModeEnabled"in d&&e(0,l=d.isSwarmModeEnabled),"hasEverUsedRemoteAgent"in d&&e(1,i=d.hasEverUsedRemoteAgent)},r.$$.update=()=>{32&r.$$.dirty&&e(2,s=o.enabled)},e(3,n=c.getCurrentSettings),a(),a=nn(n,d=>e(5,o=d)),[l,i,s,n,c,o,()=>c.updateEnabled(!s)]}class qu extends ot{constructor(t){super(),at(this,t,Vu,Uu,rt,{isSwarmModeEnabled:0,hasEverUsedRemoteAgent:1})}}function Rr(r){let t,e;return t=new su({props:{onMCPServerAdd:r[11],onMCPServerSave:r[12],onMCPServerDelete:r[13],onMCPServerToggleDisable:r[14],onMCPServerJSONImport:r[15],onCancel:r[16],isMCPImportEnabled:r[2]}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};2048&s&&(o.onMCPServerAdd=n[11]),4096&s&&(o.onMCPServerSave=n[12]),8192&s&&(o.onMCPServerDelete=n[13]),16384&s&&(o.onMCPServerToggleDisable=n[14]),32768&s&&(o.onMCPServerJSONImport=n[15]),65536&s&&(o.onCancel=n[16]),4&s&&(o.isMCPImportEnabled=n[2]),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Pr(r){let t,e;return t=new xu({props:{supportedShells:r[17],selectedShell:r[18],startupScript:r[19],onShellSelect:r[20],onStartupScriptChange:r[21]}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};131072&s&&(o.supportedShells=n[17]),262144&s&&(o.selectedShell=n[18]),524288&s&&(o.startupScript=n[19]),1048576&s&&(o.onShellSelect=n[20]),2097152&s&&(o.onStartupScriptChange=n[21]),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Lr(r){let t,e;return t=new Lu({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function zr(r){let t,e;return t=new qu({props:{isSwarmModeEnabled:r[6],hasEverUsedRemoteAgent:r[7]}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};64&s&&(o.isSwarmModeEnabled=n[6]),128&s&&(o.hasEverUsedRemoteAgent=n[7]),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Bu(r){let t,e,n,s,o,a,l;e=new hc({props:{title:"Services",tools:r[0],onAuthenticate:r[8],onRevokeAccess:r[9],onToolApprovalConfigChange:r[10]}});let i=r[1]&&Rr(r),c=r[3]&&Pr(r),d=r[4]&&Lr(),u=r[5]&&zr(r);return{c(){t=k("div"),_(e.$$.fragment),n=T(),i&&i.c(),s=T(),c&&c.c(),o=T(),d&&d.c(),a=T(),u&&u.c(),w(t,"class","c-settings-tools svelte-181yusq")},m(f,p){y(f,t,p),C(e,t,null),b(t,n),i&&i.m(t,null),b(t,s),c&&c.m(t,null),b(t,o),d&&d.m(t,null),b(t,a),u&&u.m(t,null),l=!0},p(f,[p]){const g={};1&p&&(g.tools=f[0]),256&p&&(g.onAuthenticate=f[8]),512&p&&(g.onRevokeAccess=f[9]),1024&p&&(g.onToolApprovalConfigChange=f[10]),e.$set(g),f[1]?i?(i.p(f,p),2&p&&m(i,1)):(i=Rr(f),i.c(),m(i,1),i.m(t,s)):i&&(q(),$(i,1,1,()=>{i=null}),B()),f[3]?c?(c.p(f,p),8&p&&m(c,1)):(c=Pr(f),c.c(),m(c,1),c.m(t,o)):c&&(q(),$(c,1,1,()=>{c=null}),B()),f[4]?d?16&p&&m(d,1):(d=Lr(),d.c(),m(d,1),d.m(t,a)):d&&(q(),$(d,1,1,()=>{d=null}),B()),f[5]?u?(u.p(f,p),32&p&&m(u,1)):(u=zr(f),u.c(),m(u,1),u.m(t,null)):u&&(q(),$(u,1,1,()=>{u=null}),B())},i(f){l||(m(e.$$.fragment,f),m(i),m(c),m(d),m(u),l=!0)},o(f){$(e.$$.fragment,f),$(i),$(c),$(d),$(u),l=!1},d(f){f&&v(t),x(e),i&&i.d(),c&&c.d(),d&&d.d(),u&&u.d()}}}function Hu(r,t,e){let{tools:n=[]}=t,{isMCPEnabled:s=!0}=t,{isMCPImportEnabled:o=!0}=t,{isTerminalEnabled:a=!0}=t,{isSoundCategoryEnabled:l=!1}=t,{isAgentCategoryEnabled:i=!1}=t,{isSwarmModeFeatureFlagEnabled:c=!1}=t,{hasEverUsedRemoteAgent:d=!1}=t,{onAuthenticate:u}=t,{onRevokeAccess:f}=t,{onToolApprovalConfigChange:p=()=>{}}=t,{onMCPServerAdd:g}=t,{onMCPServerSave:h}=t,{onMCPServerDelete:S}=t,{onMCPServerToggleDisable:P}=t,{onMCPServerJSONImport:N}=t,{onCancel:M}=t,{supportedShells:A=[]}=t,{selectedShell:E}=t,{startupScript:z}=t,{onShellSelect:G=()=>{}}=t,{onStartupScriptChange:tt=()=>{}}=t;return r.$$set=O=>{"tools"in O&&e(0,n=O.tools),"isMCPEnabled"in O&&e(1,s=O.isMCPEnabled),"isMCPImportEnabled"in O&&e(2,o=O.isMCPImportEnabled),"isTerminalEnabled"in O&&e(3,a=O.isTerminalEnabled),"isSoundCategoryEnabled"in O&&e(4,l=O.isSoundCategoryEnabled),"isAgentCategoryEnabled"in O&&e(5,i=O.isAgentCategoryEnabled),"isSwarmModeFeatureFlagEnabled"in O&&e(6,c=O.isSwarmModeFeatureFlagEnabled),"hasEverUsedRemoteAgent"in O&&e(7,d=O.hasEverUsedRemoteAgent),"onAuthenticate"in O&&e(8,u=O.onAuthenticate),"onRevokeAccess"in O&&e(9,f=O.onRevokeAccess),"onToolApprovalConfigChange"in O&&e(10,p=O.onToolApprovalConfigChange),"onMCPServerAdd"in O&&e(11,g=O.onMCPServerAdd),"onMCPServerSave"in O&&e(12,h=O.onMCPServerSave),"onMCPServerDelete"in O&&e(13,S=O.onMCPServerDelete),"onMCPServerToggleDisable"in O&&e(14,P=O.onMCPServerToggleDisable),"onMCPServerJSONImport"in O&&e(15,N=O.onMCPServerJSONImport),"onCancel"in O&&e(16,M=O.onCancel),"supportedShells"in O&&e(17,A=O.supportedShells),"selectedShell"in O&&e(18,E=O.selectedShell),"startupScript"in O&&e(19,z=O.startupScript),"onShellSelect"in O&&e(20,G=O.onShellSelect),"onStartupScriptChange"in O&&e(21,tt=O.onStartupScriptChange)},[n,s,o,a,l,i,c,d,u,f,p,g,h,S,P,N,M,A,E,z,G,tt]}class Gu extends ot{constructor(t){super(),at(this,t,Hu,Bu,rt,{tools:0,isMCPEnabled:1,isMCPImportEnabled:2,isTerminalEnabled:3,isSoundCategoryEnabled:4,isAgentCategoryEnabled:5,isSwarmModeFeatureFlagEnabled:6,hasEverUsedRemoteAgent:7,onAuthenticate:8,onRevokeAccess:9,onToolApprovalConfigChange:10,onMCPServerAdd:11,onMCPServerSave:12,onMCPServerDelete:13,onMCPServerToggleDisable:14,onMCPServerJSONImport:15,onCancel:16,supportedShells:17,selectedShell:18,startupScript:19,onShellSelect:20,onStartupScriptChange:21})}}function Ju(r){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=Mt(s,n[o]);return{c(){t=xt("svg"),e=new kn(!0),this.h()},l(o){t=_n(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=bn(t);e=Sn(a,!0),a.forEach(v),this.h()},h(){e.a=null,le(t,s)},m(o,a){Cn(o,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',t)},p(o,[a]){le(t,s=ve(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&a&&o[0]]))},i:D,o:D,d(o){o&&v(t)}}}function Wu(r,t,e){return r.$$set=n=>{e(0,t=Mt(Mt({},t),se(n)))},[t=se(t)]}class Ku extends ot{constructor(t){super(),at(this,t,Wu,Ju,rt,{})}}function Yu(r){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=Mt(s,n[o]);return{c(){t=xt("svg"),e=new kn(!0),this.h()},l(o){t=_n(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=bn(t);e=Sn(a,!0),a.forEach(v),this.h()},h(){e.a=null,le(t,s)},m(o,a){Cn(o,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',t)},p(o,[a]){le(t,s=ve(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&a&&o[0]]))},i:D,o:D,d(o){o&&v(t)}}}function Xu(r,t,e){return r.$$set=n=>{e(0,t=Mt(Mt({},t),se(n)))},[t=se(t)]}class Qu extends ot{constructor(t){super(),at(this,t,Xu,Yu,rt,{})}}function tp(r){let t,e,n,s;function o(l){r[6](l)}let a={placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:r[3]};return r[0]!==void 0&&(a.value=r[0]),e=new ua({props:a}),Ct.push(()=>_t(e,"value",o)),e.$on("focus",r[7]),{c(){t=k("div"),_(e.$$.fragment),w(t,"class","c-user-guidelines-category__input svelte-10borzo")},m(l,i){y(l,t,i),C(e,t,null),s=!0},p(l,[i]){const c={};!n&&1&i&&(n=!0,c.value=l[0],bt(()=>n=!1)),e.$set(c)},i(l){s||(m(e.$$.fragment,l),s=!0)},o(l){$(e.$$.fragment,l),s=!1},d(l){l&&v(t),x(e)}}}function ep(r,t,e){let n;const s=Gn();let{userGuidelines:o=""}=t,{userGuidelinesLengthLimit:a}=t,{updateUserGuideline:l=()=>!1}=t;const i=kt(void 0);function c(){const d=o.trim();if(n!==d){if(!l(d))throw a&&d.length>a?`The user guideline must be less than ${a} character long`:"An error occurred updating the user";ps(i,n=d,n)}}return Et(r,i,d=>e(8,n=d)),Wr(()=>{ps(i,n=o.trim(),n)}),Kr(()=>{c()}),r.$$set=d=>{"userGuidelines"in d&&e(0,o=d.userGuidelines),"userGuidelinesLengthLimit"in d&&e(4,a=d.userGuidelinesLengthLimit),"updateUserGuideline"in d&&e(5,l=d.updateUserGuideline)},[o,s,i,c,a,l,function(d){o=d,e(0,o)},d=>{s("focus",d)}]}class Co extends ot{constructor(t){super(),at(this,t,ep,tp,rt,{userGuidelines:0,userGuidelinesLengthLimit:4,updateUserGuideline:5})}}class np{constructor(t,e,n){it(this,"_showCreateRuleDialog",kt(!1));it(this,"_createRuleError",kt(""));it(this,"_extensionClient");this._host=t,this._msgBroker=e,this._rulesModel=n;const s=new eo;this._extensionClient=new no(t,e,s)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(t){if(t&&t.trim()){this._createRuleError.set("");try{const e=await this._rulesModel.createRule(t.trim());e&&e.path&&await this.openRule(e.path),this._extensionClient.reportAgentSessionEvent({eventName:zn.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:dn.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const n=`Failed to create rule "${t.trim()}"`;this._createRuleError.set(n)}}else this.hideCreateRuleDialog()}async openRule(t){try{const e=await this._rulesModel.getWorkspaceRoot();t===ms?this._extensionClient.openFile({repoRoot:e,pathName:ms}):this._extensionClient.openFile({repoRoot:e,pathName:`${ea}/${na}/${t}`})}catch(e){console.error("Failed to open rule:",e)}}async deleteRule(t){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(t)}catch(e){console.error("Failed to delete rule:",e)}}async selectFileToImport(){try{const t=await this._msgBroker.send({type:ht.triggerImportDialogRequest},1e5);if(t.data.selectedPaths&&t.data.selectedPaths.length>0){const e=await this._rulesModel.processSelectedPaths(t.data.selectedPaths);this._showImportNotification(e),this._reportSelectedImportMetrics(e)}}catch(t){console.error("Failed to import files:",t)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(t){const e=await this._rulesModel.processAutoImportSelection(t);return this._showImportNotification(e),this._reportAutoImportMetrics(e),e}_showImportNotification(t){let e;t.importedRulesCount===0?e=t.source?`No new rules imported from ${t.source}`:"No new rules imported":(e=`Successfully imported ${t.importedRulesCount} rule${t.importedRulesCount!==1?"s":""}`,t.duplicatesCount&&t.duplicatesCount>0&&(e+=` and skipped ${t.duplicatesCount} duplicate${t.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:e,type:t.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(t){const e=t.directoryOrFile==="directory"?dn.selectedDirectory:(t.directoryOrFile,dn.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:zn.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:e,numFiles:t.importedRulesCount,source:""}}})}_reportAutoImportMetrics(t){this._extensionClient.reportAgentSessionEvent({eventName:zn.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:dn.auto,numFiles:t.importedRulesCount,source:t.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}function sp(r){let t;return{c(){t=I("Enter a name for the new rule file (e.g., architecture.md):")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Or(r){let t,e;return t=new Mn({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[op],default:[rp]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};4098&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function rp(r){let t;return{c(){t=I(r[1])},m(e,n){y(e,t,n)},p(e,n){2&n&&ct(t,e[1])},d(e){e&&v(t)}}}function op(r){let t,e;return t=new Yn({props:{slot:"icon"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function ap(r){let t,e,n,s,o,a,l,i;function c(p){r[9](p)}function d(p){r[10](p)}t=new Y({props:{size:2,color:"secondary",$$slots:{default:[sp]},$$scope:{ctx:r}}});let u={placeholder:"rule-name.md",disabled:r[4]};r[3]!==void 0&&(u.value=r[3]),r[2]!==void 0&&(u.textInput=r[2]),n=new ye({props:u}),Ct.push(()=>_t(n,"value",c)),Ct.push(()=>_t(n,"textInput",d)),n.$on("keydown",r[8]);let f=r[1]&&Or(r);return{c(){_(t.$$.fragment),e=T(),_(n.$$.fragment),a=T(),f&&f.c(),l=ft()},m(p,g){C(t,p,g),y(p,e,g),C(n,p,g),y(p,a,g),f&&f.m(p,g),y(p,l,g),i=!0},p(p,g){const h={};4096&g&&(h.$$scope={dirty:g,ctx:p}),t.$set(h);const S={};16&g&&(S.disabled=p[4]),!s&&8&g&&(s=!0,S.value=p[3],bt(()=>s=!1)),!o&&4&g&&(o=!0,S.textInput=p[2],bt(()=>o=!1)),n.$set(S),p[1]?f?(f.p(p,g),2&g&&m(f,1)):(f=Or(p),f.c(),m(f,1),f.m(l.parentNode,l)):f&&(q(),$(f,1,1,()=>{f=null}),B())},i(p){i||(m(t.$$.fragment,p),m(n.$$.fragment,p),m(f),i=!0)},o(p){$(t.$$.fragment,p),$(n.$$.fragment,p),$(f),i=!1},d(p){p&&(v(e),v(a),v(l)),x(t,p),x(n,p),f&&f.d(p)}}}function ip(r){let t;return{c(){t=I("Cancel")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function lp(r){let t,e=r[4]?"Creating...":"Create";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){16&s&&e!==(e=n[4]?"Creating...":"Create")&&ct(t,e)},d(n){n&&v(t)}}}function cp(r){let t,e,n,s,o;return e=new vt({props:{variant:"solid",color:"neutral",disabled:r[4],$$slots:{default:[ip]},$$scope:{ctx:r}}}),e.$on("click",r[6]),s=new vt({props:{variant:"solid",color:"accent",disabled:!r[3].trim()||r[4],loading:r[4],$$slots:{default:[lp]},$$scope:{ctx:r}}}),s.$on("click",r[5]),{c(){t=k("div"),_(e.$$.fragment),n=T(),_(s.$$.fragment),w(t,"slot","footer")},m(a,l){y(a,t,l),C(e,t,null),b(t,n),C(s,t,null),o=!0},p(a,l){const i={};16&l&&(i.disabled=a[4]),4096&l&&(i.$$scope={dirty:l,ctx:a}),e.$set(i);const c={};24&l&&(c.disabled=!a[3].trim()||a[4]),16&l&&(c.loading=a[4]),4112&l&&(c.$$scope={dirty:l,ctx:a}),s.$set(c)},i(a){o||(m(e.$$.fragment,a),m(s.$$.fragment,a),o=!0)},o(a){$(e.$$.fragment,a),$(s.$$.fragment,a),o=!1},d(a){a&&v(t),x(e),x(s)}}}function dp(r){let t,e;return t=new ao({props:{show:r[0],title:"Create New Rule",ariaLabelledBy:"dialog-title",preventBackdropClose:r[4],preventEscapeClose:r[4],$$slots:{footer:[cp],body:[ap]},$$scope:{ctx:r}}}),t.$on("cancel",r[6]),t.$on("keydown",r[7]),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,[s]){const o={};1&s&&(o.show=n[0]),16&s&&(o.preventBackdropClose=n[4]),16&s&&(o.preventEscapeClose=n[4]),4126&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function up(r,t,e){const n=Gn();let s,{show:o=!1}=t,{errorMessage:a=""}=t,l="",i=!1;function c(){l.trim()&&!i&&(e(4,i=!0),n("create",l.trim()))}function d(){i||(n("cancel"),e(3,l=""))}return r.$$set=u=>{"show"in u&&e(0,o=u.show),"errorMessage"in u&&e(1,a=u.errorMessage)},r.$$.update=()=>{5&r.$$.dirty&&o&&s&&setTimeout(()=>s==null?void 0:s.focus(),100),3&r.$$.dirty&&(o&&!a||e(4,i=!1)),3&r.$$.dirty&&(o||a||e(3,l=""))},[o,a,s,l,i,c,d,function(u){i||u.detail.key==="Enter"&&(u.detail.preventDefault(),c())},function(u){i||(u.key==="Enter"?(u.preventDefault(),c()):u.key==="Escape"&&(u.preventDefault(),d()))},function(u){l=u,e(3,l),e(0,o),e(1,a)},function(u){s=u,e(2,s)}]}class pp extends ot{constructor(t){super(),at(this,t,up,dp,rt,{show:0,errorMessage:1})}}function Zr(r,t,e){const n=r.slice();return n[18]=t[e],n}function mp(r){let t,e,n,s,o,a,l,i,c;function d(h){r[15](h)}function u(h){r[16](h)}t=new Y({props:{size:2,color:"secondary",$$slots:{default:[$p]},$$scope:{ctx:r}}});let f={triggerOn:r[1].length===0?[]:void 0,$$slots:{default:[wp]},$$scope:{ctx:r}};r[7]!==void 0&&(f.requestClose=r[7]),r[6]!==void 0&&(f.focusedIndex=r[6]),n=new wt.Root({props:f}),Ct.push(()=>_t(n,"requestClose",d)),Ct.push(()=>_t(n,"focusedIndex",u));let p=r[3]&&Dr(r),g=r[4]&&Ur(r);return{c(){_(t.$$.fragment),e=T(),_(n.$$.fragment),a=T(),p&&p.c(),l=T(),g&&g.c(),i=ft()},m(h,S){C(t,h,S),y(h,e,S),C(n,h,S),y(h,a,S),p&&p.m(h,S),y(h,l,S),g&&g.m(h,S),y(h,i,S),c=!0},p(h,S){const P={};2097152&S&&(P.$$scope={dirty:S,ctx:h}),t.$set(P);const N={};2&S&&(N.triggerOn=h[1].length===0?[]:void 0),2097442&S&&(N.$$scope={dirty:S,ctx:h}),!s&&128&S&&(s=!0,N.requestClose=h[7],bt(()=>s=!1)),!o&&64&S&&(o=!0,N.focusedIndex=h[6],bt(()=>o=!1)),n.$set(N),h[3]?p?(p.p(h,S),8&S&&m(p,1)):(p=Dr(h),p.c(),m(p,1),p.m(l.parentNode,l)):p&&(q(),$(p,1,1,()=>{p=null}),B()),h[4]?g?(g.p(h,S),16&S&&m(g,1)):(g=Ur(h),g.c(),m(g,1),g.m(i.parentNode,i)):g&&(q(),$(g,1,1,()=>{g=null}),B())},i(h){c||(m(t.$$.fragment,h),m(n.$$.fragment,h),m(p),m(g),c=!0)},o(h){$(t.$$.fragment,h),$(n.$$.fragment,h),$(p),$(g),c=!1},d(h){h&&(v(e),v(a),v(l),v(i)),x(t,h),x(n,h),p&&p.d(h),g&&g.d(h)}}}function fp(r){let t;return{c(){t=k("input"),w(t,"type","text"),t.value="No existing rules found",t.readOnly=!0,w(t,"class","c-dropdown-input svelte-z1s6x7")},m(e,n){y(e,t,n)},p:D,i:D,o:D,d(e){e&&v(t)}}}function $p(r){let t;return{c(){t=I("Select existing rules to auto import to .augment/rules")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function gp(r){let t,e,n,s,o,a;return o=new Xn({props:{class:"c-dropdown-chevron"}}),{c(){t=k("div"),e=k("input"),s=T(),_(o.$$.fragment),w(e,"type","text"),e.value=n=r[5]?r[5].label:"Existing rules",e.readOnly=!0,w(e,"class","c-dropdown-input svelte-z1s6x7"),w(t,"class","c-dropdown-trigger svelte-z1s6x7")},m(l,i){y(l,t,i),b(t,e),b(t,s),C(o,t,null),a=!0},p(l,i){(!a||32&i&&n!==(n=l[5]?l[5].label:"Existing rules")&&e.value!==n)&&(e.value=n)},i(l){a||(m(o.$$.fragment,l),a=!0)},o(l){$(o.$$.fragment,l),a=!1},d(l){l&&v(t),x(o)}}}function hp(r){let t,e=r[18].label+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){2&s&&e!==(e=n[18].label+"")&&ct(t,e)},d(n){n&&v(t)}}}function Fr(r){var s;let t,e;function n(){return r[14](r[18])}return t=new wt.Item({props:{onSelect:n,highlight:((s=r[5])==null?void 0:s.label)===r[18].label,$$slots:{default:[hp]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(o,a){C(t,o,a),e=!0},p(o,a){var i;r=o;const l={};2&a&&(l.onSelect=n),34&a&&(l.highlight=((i=r[5])==null?void 0:i.label)===r[18].label),2097154&a&&(l.$$scope={dirty:a,ctx:r}),t.$set(l)},i(o){e||(m(t.$$.fragment,o),e=!0)},o(o){$(t.$$.fragment,o),e=!1},d(o){x(t,o)}}}function jr(r){let t,e,n,s;return t=new wt.Separator({}),n=new wt.Label({props:{$$slots:{default:[vp]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment),e=T(),_(n.$$.fragment)},m(o,a){C(t,o,a),y(o,e,a),C(n,o,a),s=!0},p(o,a){const l={};2097442&a&&(l.$$scope={dirty:a,ctx:o}),n.$set(l)},i(o){s||(m(t.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){$(t.$$.fragment,o),$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(t,o),x(n,o)}}}function vp(r){var n;let t,e=(r[8]!==void 0?r[1][r[8]].description:(n=r[5])==null?void 0:n.description)+"";return{c(){t=I(e)},m(s,o){y(s,t,o)},p(s,o){var a;290&o&&e!==(e=(s[8]!==void 0?s[1][s[8]].description:(a=s[5])==null?void 0:a.description)+"")&&ct(t,e)},d(s){s&&v(t)}}}function yp(r){let t,e,n,s=lt(r[1]),o=[];for(let i=0;i<s.length;i+=1)o[i]=Fr(Zr(r,s,i));const a=i=>$(o[i],1,1,()=>{o[i]=null});let l=(r[8]!==void 0||r[5])&&jr(r);return{c(){for(let i=0;i<o.length;i+=1)o[i].c();t=T(),l&&l.c(),e=ft()},m(i,c){for(let d=0;d<o.length;d+=1)o[d]&&o[d].m(i,c);y(i,t,c),l&&l.m(i,c),y(i,e,c),n=!0},p(i,c){if(546&c){let d;for(s=lt(i[1]),d=0;d<s.length;d+=1){const u=Zr(i,s,d);o[d]?(o[d].p(u,c),m(o[d],1)):(o[d]=Fr(u),o[d].c(),m(o[d],1),o[d].m(t.parentNode,t))}for(q(),d=s.length;d<o.length;d+=1)a(d);B()}i[8]!==void 0||i[5]?l?(l.p(i,c),288&c&&m(l,1)):(l=jr(i),l.c(),m(l,1),l.m(e.parentNode,e)):l&&(q(),$(l,1,1,()=>{l=null}),B())},i(i){if(!n){for(let c=0;c<s.length;c+=1)m(o[c]);m(l),n=!0}},o(i){o=o.filter(Boolean);for(let c=0;c<o.length;c+=1)$(o[c]);$(l),n=!1},d(i){i&&(v(t),v(e)),me(o,i),l&&l.d(i)}}}function wp(r){let t,e,n,s;return t=new wt.Trigger({props:{$$slots:{default:[gp]},$$scope:{ctx:r}}}),n=new wt.Content({props:{align:"start",side:"bottom",$$slots:{default:[yp]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment),e=T(),_(n.$$.fragment)},m(o,a){C(t,o,a),y(o,e,a),C(n,o,a),s=!0},p(o,a){const l={};2097184&a&&(l.$$scope={dirty:a,ctx:o}),t.$set(l);const i={};2097442&a&&(i.$$scope={dirty:a,ctx:o}),n.$set(i)},i(o){s||(m(t.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){$(t.$$.fragment,o),$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(t,o),x(n,o)}}}function Dr(r){let t,e;return t=new Mn({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Cp],default:[xp]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};2097160&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function xp(r){let t;return{c(){t=I(r[3])},m(e,n){y(e,t,n)},p(e,n){8&n&&ct(t,e[3])},d(e){e&&v(t)}}}function Cp(r){let t,e;return t=new Yn({props:{slot:"icon"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Ur(r){let t,e;return t=new Mn({props:{variant:"soft",color:"success",size:1,$$slots:{icon:[bp],default:[_p]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};2097168&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function _p(r){let t;return{c(){t=I(r[4])},m(e,n){y(e,t,n)},p(e,n){16&n&&ct(t,e[4])},d(e){e&&v(t)}}}function bp(r){let t,e;return t=new Wo({props:{slot:"icon"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Sp(r){let t,e,n,s;const o=[fp,mp],a=[];function l(i,c){return i[1].length===0?0:1}return e=l(r),n=a[e]=o[e](r),{c(){t=k("div"),n.c(),w(t,"slot","body"),w(t,"class","c-auto-import-rules-dialog svelte-z1s6x7")},m(i,c){y(i,t,c),a[e].m(t,null),s=!0},p(i,c){let d=e;e=l(i),e===d?a[e].p(i,c):(q(),$(a[d],1,1,()=>{a[d]=null}),B(),n=a[e],n?n.p(i,c):(n=a[e]=o[e](i),n.c()),m(n,1),n.m(t,null))},i(i){s||(m(n),s=!0)},o(i){$(n),s=!1},d(i){i&&v(t),a[e].d()}}}function kp(r){let t;return{c(){t=I("Cancel")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Vr(r){let t,e;return t=new vt({props:{color:"accent",variant:"solid",disabled:!r[5]||r[2],loading:r[2],$$slots:{default:[Mp]},$$scope:{ctx:r}}}),t.$on("click",r[10]),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};36&s&&(o.disabled=!n[5]||n[2]),4&s&&(o.loading=n[2]),2097156&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Mp(r){let t,e=r[2]?"Importing...":"Import ";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){4&s&&e!==(e=n[2]?"Importing...":"Import ")&&ct(t,e)},d(n){n&&v(t)}}}function Ap(r){let t,e,n,s;e=new vt({props:{variant:"solid",color:"neutral",disabled:r[2],$$slots:{default:[kp]},$$scope:{ctx:r}}}),e.$on("click",r[11]);let o=r[1].length>0&&Vr(r);return{c(){t=k("div"),_(e.$$.fragment),n=T(),o&&o.c(),w(t,"slot","footer")},m(a,l){y(a,t,l),C(e,t,null),b(t,n),o&&o.m(t,null),s=!0},p(a,l){const i={};4&l&&(i.disabled=a[2]),2097152&l&&(i.$$scope={dirty:l,ctx:a}),e.$set(i),a[1].length>0?o?(o.p(a,l),2&l&&m(o,1)):(o=Vr(a),o.c(),m(o,1),o.m(t,null)):o&&(q(),$(o,1,1,()=>{o=null}),B())},i(a){s||(m(e.$$.fragment,a),m(o),s=!0)},o(a){$(e.$$.fragment,a),$(o),s=!1},d(a){a&&v(t),x(e),o&&o.d()}}}function Tp(r){let t,e,n,s;return t=new ao({props:{show:r[0],title:"Auto Import Rules",ariaLabelledBy:"dialog-title",preventBackdropClose:r[2],preventEscapeClose:r[2],$$slots:{footer:[Ap],body:[Sp]},$$scope:{ctx:r}}}),t.$on("cancel",r[11]),{c(){_(t.$$.fragment)},m(o,a){C(t,o,a),e=!0,n||(s=It(window,"keydown",r[12]),n=!0)},p(o,[a]){const l={};1&a&&(l.show=o[0]),4&a&&(l.preventBackdropClose=o[2]),4&a&&(l.preventEscapeClose=o[2]),2097662&a&&(l.$$scope={dirty:a,ctx:o}),t.$set(l)},i(o){e||(m(t.$$.fragment,o),e=!0)},o(o){$(t.$$.fragment,o),e=!1},d(o){x(t,o),n=!1,s()}}}function Np(r,t,e){let n,s,o=D,a=()=>(o(),o=nn(i,M=>e(8,s=M)),i);r.$$.on_destroy.push(()=>o());const l=Gn();let i,{show:c=!1}=t,{options:d=[]}=t,{isLoading:u=!1}=t,{errorMessage:f=""}=t,{successMessage:p=""}=t,g=n;a();let h=()=>{};function S(M){e(5,g=M),h()}function P(){g&&!u&&l("select",g)}function N(){u||(l("cancel"),e(5,g=n))}return r.$$set=M=>{"show"in M&&e(0,c=M.show),"options"in M&&e(1,d=M.options),"isLoading"in M&&e(2,u=M.isLoading),"errorMessage"in M&&e(3,f=M.errorMessage),"successMessage"in M&&e(4,p=M.successMessage)},r.$$.update=()=>{2&r.$$.dirty&&e(13,n=d.length>0?d[0]:null),8193&r.$$.dirty&&c&&e(5,g=n)},[c,d,u,f,p,g,i,h,s,S,P,N,function(M){c&&!u&&(M.key==="Escape"?(M.preventDefault(),N()):M.key==="Enter"&&g&&(M.preventDefault(),P()))},n,M=>S(M),function(M){h=M,e(7,h)},function(M){i=M,a(e(6,i))}]}class Ep extends ot{constructor(t){super(),at(this,t,Np,Tp,rt,{show:0,options:1,isLoading:2,errorMessage:3,successMessage:4})}}function qr(r,t,e){const n=r.slice();return n[35]=t[e],n}function Br(r,t,e){const n=r.slice();return n[38]=t[e],n}function Ip(r){let t;return{c(){t=I("Rules")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Rp(r){let t;return{c(){t=I("Learn more")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Pp(r){let t,e,n=[],s=new Map,o=lt(r[10]);const a=l=>l[38].path;for(let l=0;l<o.length;l+=1){let i=Br(r,o,l),c=a(i);s.set(c,n[l]=Hr(c,i))}return{c(){for(let l=0;l<n.length;l+=1)n[l].c();t=ft()},m(l,i){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(l,i);y(l,t,i),e=!0},p(l,i){99328&i[0]&&(o=lt(l[10]),q(),n=Ht(n,i,a,1,l,o,s,t.parentNode,Gt,Hr,t,Br),B())},i(l){if(!e){for(let i=0;i<o.length;i+=1)m(n[i]);e=!0}},o(l){for(let i=0;i<n.length;i+=1)$(n[i]);e=!1},d(l){l&&v(t);for(let i=0;i<n.length;i+=1)n[i].d(l)}}}function Lp(r){let t,e,n;return e=new Y({props:{size:1,color:"neutral",$$slots:{default:[qp]},$$scope:{ctx:r}}}),{c(){t=k("div"),_(e.$$.fragment),w(t,"class","c-rules-list-empty svelte-mrq2l0")},m(s,o){y(s,t,o),C(e,t,null),n=!0},p(s,o){const a={};1024&o[1]&&(a.$$scope={dirty:o,ctx:s}),e.$set(a)},i(s){n||(m(e.$$.fragment,s),n=!0)},o(s){$(e.$$.fragment,s),n=!1},d(s){s&&v(t),x(e)}}}function zp(r){let t,e;return t=new Ko({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Op(r){let t,e;return t=new ee({props:{content:"No description found",$$slots:{default:[Zp]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Zp(r){let t,e;return t=new Yn({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Fp(r){let t,e=r[38].path+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){1024&s[0]&&e!==(e=n[38].path+"")&&ct(t,e)},d(n){n&&v(t)}}}function jp(r){let t,e,n,s,o,a,l,i,c;const d=[Op,zp],u=[];function f(p,g){return p[38].type!==Lo.AGENT_REQUESTED||p[38].description?1:0}return n=f(r),s=u[n]=d[n](r),l=new Y({props:{size:1,color:"neutral",$$slots:{default:[Fp]},$$scope:{ctx:r}}}),{c(){t=k("div"),e=k("div"),s.c(),o=T(),a=k("div"),_(l.$$.fragment),i=T(),w(e,"class","l-icon-wrapper svelte-mrq2l0"),w(a,"class","c-rule-item-path svelte-mrq2l0"),w(t,"class","c-rule-item-info svelte-mrq2l0"),w(t,"slot","header-left")},m(p,g){y(p,t,g),b(t,e),u[n].m(e,null),b(t,o),b(t,a),C(l,a,null),b(t,i),c=!0},p(p,g){let h=n;n=f(p),n!==h&&(q(),$(u[h],1,1,()=>{u[h]=null}),B(),s=u[n],s||(s=u[n]=d[n](p),s.c()),m(s,1),s.m(e,null));const S={};1024&g[0]|1024&g[1]&&(S.$$scope={dirty:g,ctx:p}),l.$set(S)},i(p){c||(m(s),m(l.$$.fragment,p),c=!0)},o(p){$(s),$(l.$$.fragment,p),c=!1},d(p){p&&v(t),u[n].d(),x(l)}}}function Dp(r){let t,e;return t=new Yo({props:{slot:"iconRight"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Up(r){let t,e;return t=new Xo({props:{slot:"iconRight"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Vp(r){let t,e,n,s,o,a,l,i,c,d;function u(...f){return r[26](r[38],...f)}return s=new ma({props:{rule:r[38],onSave:u}}),a=new vt({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Dp]},$$scope:{ctx:r}}}),a.$on("click",function(...f){return r[27](r[38],...f)}),i=new vt({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Up]},$$scope:{ctx:r}}}),i.$on("click",function(...f){return r[28](r[38],...f)}),{c(){t=k("div"),e=k("div"),n=k("div"),_(s.$$.fragment),o=T(),_(a.$$.fragment),l=T(),_(i.$$.fragment),c=T(),w(n,"class","c-rules-dropdown"),w(e,"class","status-controls svelte-mrq2l0"),w(t,"class","server-actions"),w(t,"slot","header-right")},m(f,p){y(f,t,p),b(t,e),b(e,n),C(s,n,null),b(e,o),C(a,e,null),b(e,l),C(i,e,null),b(t,c),d=!0},p(f,p){r=f;const g={};1024&p[0]&&(g.rule=r[38]),1024&p[0]&&(g.onSave=u),s.$set(g);const h={};1024&p[1]&&(h.$$scope={dirty:p,ctx:r}),a.$set(h);const S={};1024&p[1]&&(S.$$scope={dirty:p,ctx:r}),i.$set(S)},i(f){d||(m(s.$$.fragment,f),m(a.$$.fragment,f),m(i.$$.fragment,f),d=!0)},o(f){$(s.$$.fragment,f),$(a.$$.fragment,f),$(i.$$.fragment,f),d=!1},d(f){f&&v(t),x(s),x(a),x(i)}}}function Hr(r,t){let e,n,s;return n=new Kt({props:{isClickable:!0,$$slots:{"header-right":[Vp],"header-left":[jp]},$$scope:{ctx:t}}}),n.$on("click",function(){return t[29](t[38])}),{key:r,first:null,c(){e=ft(),_(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),C(n,o,a),s=!0},p(o,a){t=o;const l={};1024&a[0]|1024&a[1]&&(l.$$scope={dirty:a,ctx:t}),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(n,o)}}}function qp(r){let t;return{c(){t=I("No rules files found")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Bp(r){let t,e,n,s;return e=new sn({props:{slot:"iconLeft"}}),{c(){t=k("div"),_(e.$$.fragment),n=I(`
          Create new rule file`),w(t,"class","c-rules-actions-button-content svelte-mrq2l0")},m(o,a){y(o,t,a),C(e,t,null),b(t,n),s=!0},p:D,i(o){s||(m(e.$$.fragment,o),s=!0)},o(o){$(e.$$.fragment,o),s=!1},d(o){o&&v(t),x(e)}}}function Hp(r){let t,e,n,s,o;return e=new Yr({props:{slot:"iconLeft"}}),s=new Xn({props:{slot:"iconRight"}}),{c(){t=k("div"),_(e.$$.fragment),n=I(`
              Import rules
              `),_(s.$$.fragment),w(t,"class","c-rules-actions-button-content svelte-mrq2l0")},m(a,l){y(a,t,l),C(e,t,null),b(t,n),C(s,t,null),o=!0},p:D,i(a){o||(m(e.$$.fragment,a),m(s.$$.fragment,a),o=!0)},o(a){$(e.$$.fragment,a),$(s.$$.fragment,a),o=!1},d(a){a&&v(t),x(e),x(s)}}}function Gp(r){let t,e;return t=new vt({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[Hp]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};1024&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function Jp(r){let t,e=r[35].label+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p:D,d(n){n&&v(t)}}}function Gr(r,t){let e,n,s;return n=new wt.Item({props:{onSelect:function(){return t[31](t[35])},$$slots:{default:[Jp]},$$scope:{ctx:t}}}),{key:r,first:null,c(){e=ft(),_(n.$$.fragment),this.first=e},m(o,a){y(o,e,a),C(n,o,a),s=!0},p(o,a){t=o;const l={};1024&a[1]&&(l.$$scope={dirty:a,ctx:t}),n.$set(l)},i(o){s||(m(n.$$.fragment,o),s=!0)},o(o){$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(n,o)}}}function Jr(r){let t,e,n,s;return t=new wt.Separator({}),n=new wt.Label({props:{$$slots:{default:[Wp]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment),e=T(),_(n.$$.fragment)},m(o,a){C(t,o,a),y(o,e,a),C(n,o,a),s=!0},p(o,a){const l={};2048&a[0]|1024&a[1]&&(l.$$scope={dirty:a,ctx:o}),n.$set(l)},i(o){s||(m(t.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){$(t.$$.fragment,o),$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(t,o),x(n,o)}}}function Wp(r){let t,e=(r[11]!==void 0?r[20][r[11]].description:r[20][0])+"";return{c(){t=I(e)},m(n,s){y(n,t,s)},p(n,s){2048&s[0]&&e!==(e=(n[11]!==void 0?n[20][n[11]].description:n[20][0])+"")&&ct(t,e)},d(n){n&&v(t)}}}function Kp(r){let t,e,n,s=[],o=new Map,a=lt(r[20]);const l=c=>c[35].id;for(let c=0;c<a.length;c+=1){let d=qr(r,a,c),u=l(d);o.set(u,s[c]=Gr(u,d))}let i=r[11]!==void 0&&Jr(r);return{c(){for(let c=0;c<s.length;c+=1)s[c].c();t=T(),i&&i.c(),e=ft()},m(c,d){for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(c,d);y(c,t,d),i&&i.m(c,d),y(c,e,d),n=!0},p(c,d){3145728&d[0]&&(a=lt(c[20]),q(),s=Ht(s,d,l,1,c,a,o,t.parentNode,Gt,Gr,t,qr),B()),c[11]!==void 0?i?(i.p(c,d),2048&d[0]&&m(i,1)):(i=Jr(c),i.c(),m(i,1),i.m(e.parentNode,e)):i&&(q(),$(i,1,1,()=>{i=null}),B())},i(c){if(!n){for(let d=0;d<a.length;d+=1)m(s[d]);m(i),n=!0}},o(c){for(let d=0;d<s.length;d+=1)$(s[d]);$(i),n=!1},d(c){c&&(v(t),v(e));for(let d=0;d<s.length;d+=1)s[d].d(c);i&&i.d(c)}}}function Yp(r){let t,e,n,s;return t=new wt.Trigger({props:{$$slots:{default:[Gp]},$$scope:{ctx:r}}}),n=new wt.Content({props:{align:"start",side:"bottom",$$slots:{default:[Kp]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment),e=T(),_(n.$$.fragment)},m(o,a){C(t,o,a),y(o,e,a),C(n,o,a),s=!0},p(o,a){const l={};1024&a[1]&&(l.$$scope={dirty:a,ctx:o}),t.$set(l);const i={};2048&a[0]|1024&a[1]&&(i.$$scope={dirty:a,ctx:o}),n.$set(i)},i(o){s||(m(t.$$.fragment,o),m(n.$$.fragment,o),s=!0)},o(o){$(t.$$.fragment,o),$(n.$$.fragment,o),s=!1},d(o){o&&v(e),x(t,o),x(n,o)}}}function Xp(r){let t;return{c(){t=I("User Guidelines")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function Qp(r){let t;return{c(){t=I("Learn more")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function t1(r){let t,e,n,s,o,a,l,i,c,d,u,f,p,g,h,S,P,N,M,A,E,z,G,tt,O,F,R,V,st,Z,J,j,pt,Lt,_e,ze;n=new Y({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[Ip]},$$scope:{ctx:r}}}),i=new Y({props:{size:1,weight:"regular",$$slots:{default:[Rp]},$$scope:{ctx:r}}});const fe=[Lp,Pp],re=[];function es(K,dt){return K[10].length===0?0:1}function _o(K){r[32](K)}function bo(K){r[33](K)}u=es(r),f=re[u]=fe[u](r),h=new vt({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[Bp]},$$scope:{ctx:r}}}),h.$on("click",r[30]);let Nn={$$slots:{default:[Yp]},$$scope:{ctx:r}};return r[9]!==void 0&&(Nn.requestClose=r[9]),r[8]!==void 0&&(Nn.focusedIndex=r[8]),P=new wt.Root({props:Nn}),Ct.push(()=>_t(P,"requestClose",_o)),Ct.push(()=>_t(P,"focusedIndex",bo)),z=new Y({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[Xp]},$$scope:{ctx:r}}}),R=new Y({props:{size:1,weight:"regular",$$slots:{default:[Qp]},$$scope:{ctx:r}}}),st=new Co({props:{userGuidelines:r[0],userGuidelinesLengthLimit:r[1],updateUserGuideline:r[2]}}),J=new pp({props:{show:r[12],errorMessage:r[13]}}),J.$on("create",r[24]),J.$on("cancel",r[25]),pt=new Ep({props:{show:r[3],options:r[4],isLoading:r[5],errorMessage:r[6],successMessage:r[7]}}),pt.$on("select",r[22]),pt.$on("cancel",r[23]),{c(){t=k("div"),e=k("div"),_(n.$$.fragment),s=T(),o=k("div"),a=I(`Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) `),l=k("a"),_(i.$$.fragment),c=T(),d=k("div"),f.c(),p=T(),g=k("div"),_(h.$$.fragment),S=T(),_(P.$$.fragment),A=T(),E=k("div"),_(z.$$.fragment),G=T(),tt=k("div"),O=I(`User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. `),F=k("a"),_(R.$$.fragment),V=T(),_(st.$$.fragment),Z=T(),_(J.$$.fragment),j=T(),_(pt.$$.fragment),w(l,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),w(l,"target","_blank"),w(d,"class","c-rules-list svelte-mrq2l0"),w(g,"class","c-rules-actions-container svelte-mrq2l0"),w(e,"class","c-rules-section svelte-mrq2l0"),w(F,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),w(F,"target","_blank"),w(E,"class","c-user-guidelines-section svelte-mrq2l0"),w(t,"class","c-rules-category svelte-mrq2l0")},m(K,dt){y(K,t,dt),b(t,e),C(n,e,null),b(e,s),b(e,o),b(o,a),b(o,l),C(i,l,null),b(e,c),b(e,d),re[u].m(d,null),b(e,p),b(e,g),C(h,g,null),b(g,S),C(P,g,null),b(t,A),b(t,E),C(z,E,null),b(E,G),b(E,tt),b(tt,O),b(tt,F),C(R,F,null),b(E,V),C(st,E,null),y(K,Z,dt),C(J,K,dt),y(K,j,dt),C(pt,K,dt),Lt=!0,_e||(ze=It(window,"message",r[14].onMessageFromExtension),_e=!0)},p(K,dt){const ns={};1024&dt[1]&&(ns.$$scope={dirty:dt,ctx:K}),n.$set(ns);const ss={};1024&dt[1]&&(ss.$$scope={dirty:dt,ctx:K}),i.$set(ss);let En=u;u=es(K),u===En?re[u].p(K,dt):(q(),$(re[En],1,1,()=>{re[En]=null}),B(),f=re[u],f?f.p(K,dt):(f=re[u]=fe[u](K),f.c()),m(f,1),f.m(d,null));const rs={};1024&dt[1]&&(rs.$$scope={dirty:dt,ctx:K}),h.$set(rs);const an={};2048&dt[0]|1024&dt[1]&&(an.$$scope={dirty:dt,ctx:K}),!N&&512&dt[0]&&(N=!0,an.requestClose=K[9],bt(()=>N=!1)),!M&&256&dt[0]&&(M=!0,an.focusedIndex=K[8],bt(()=>M=!1)),P.$set(an);const os={};1024&dt[1]&&(os.$$scope={dirty:dt,ctx:K}),z.$set(os);const as={};1024&dt[1]&&(as.$$scope={dirty:dt,ctx:K}),R.$set(as);const ln={};1&dt[0]&&(ln.userGuidelines=K[0]),2&dt[0]&&(ln.userGuidelinesLengthLimit=K[1]),4&dt[0]&&(ln.updateUserGuideline=K[2]),st.$set(ln);const In={};4096&dt[0]&&(In.show=K[12]),8192&dt[0]&&(In.errorMessage=K[13]),J.$set(In);const be={};8&dt[0]&&(be.show=K[3]),16&dt[0]&&(be.options=K[4]),32&dt[0]&&(be.isLoading=K[5]),64&dt[0]&&(be.errorMessage=K[6]),128&dt[0]&&(be.successMessage=K[7]),pt.$set(be)},i(K){Lt||(m(n.$$.fragment,K),m(i.$$.fragment,K),m(f),m(h.$$.fragment,K),m(P.$$.fragment,K),m(z.$$.fragment,K),m(R.$$.fragment,K),m(st.$$.fragment,K),m(J.$$.fragment,K),m(pt.$$.fragment,K),Lt=!0)},o(K){$(n.$$.fragment,K),$(i.$$.fragment,K),$(f),$(h.$$.fragment,K),$(P.$$.fragment,K),$(z.$$.fragment,K),$(R.$$.fragment,K),$(st.$$.fragment,K),$(J.$$.fragment,K),$(pt.$$.fragment,K),Lt=!1},d(K){K&&(v(t),v(Z),v(j)),x(n),x(i),re[u].d(),x(h),x(P),x(z),x(R),x(st),x(J,K),x(pt,K),_e=!1,ze()}}}function e1(r,t,e){let n,s,o,a,l=D,i=()=>(l(),l=nn(G,F=>e(11,s=F)),G);r.$$.on_destroy.push(()=>l());let{userGuidelines:c=""}=t,{userGuidelinesLengthLimit:d}=t,{updateUserGuideline:u=()=>!1}=t;const f=new Xr(St),p=new pa(f),g=new np(St,f,p);f.registerConsumer(p);const h=p.getRulesFiles();Et(r,h,F=>e(10,n=F));const S=g.getShowCreateRuleDialog();Et(r,S,F=>e(12,o=F));const P=g.getCreateRuleError();Et(r,P,F=>e(13,a=F));let N=!1,M=[],A=!1,E="",z="",G;i();let tt=()=>{};async function O(F){try{F.id==="select_file_or_directory"?await g.selectFileToImport():F.id==="auto_import"&&await async function(){try{e(6,E=""),e(7,z="");const R=await g.getAutoImportOptions();e(4,M=R.data.options),e(3,N=!0)}catch(R){console.error("Failed to get auto-import options:",R),e(6,E="Failed to detect existing rules in workspace.")}}()}catch(R){console.error("Failed to handle import select:",R)}tt&&tt()}return Wr(()=>{p.requestRules()}),r.$$set=F=>{"userGuidelines"in F&&e(0,c=F.userGuidelines),"userGuidelinesLengthLimit"in F&&e(1,d=F.userGuidelinesLengthLimit),"updateUserGuideline"in F&&e(2,u=F.updateUserGuideline)},[c,d,u,N,M,A,E,z,G,tt,n,s,o,a,f,p,g,h,S,P,[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}],O,async function(F){const R=F.detail;try{e(5,A=!0),e(6,E="");const V=await g.processAutoImportSelection(R);let st=`Successfully imported ${V.importedRulesCount} rule${V.importedRulesCount!==1?"s":""} from ${R.label}`;V.duplicatesCount>0&&(st+=`, ${V.duplicatesCount} duplicate${V.duplicatesCount!==1?"s":""} skipped`),V.totalAttempted>V.importedRulesCount+V.duplicatesCount&&(st+=`, ${V.totalAttempted-V.importedRulesCount-V.duplicatesCount} failed`),e(7,z=st),setTimeout(()=>{e(3,N=!1),e(7,z="")},500)}catch(V){console.error("Failed to process auto-import selection:",V),e(6,E="Failed to import rules. Please try again.")}finally{e(5,A=!1)}},function(){e(3,N=!1),e(6,E=""),e(7,z="")},function(F){g.handleCreateRuleWithName(F.detail)},function(){g.hideCreateRuleDialog()},async(F,R,V)=>{await p.updateRuleContent({type:R,path:F.path,content:F.content,description:V})},(F,R)=>{R.stopPropagation(),g.openRule(F.path)},(F,R)=>{R.stopPropagation(),g.deleteRule(F.path)},F=>g.openRule(F.path),()=>g.createRule(),F=>O(F),function(F){tt=F,e(9,tt)},function(F){G=F,i(e(8,G))}]}class n1 extends ot{constructor(t){super(),at(this,t,e1,t1,rt,{userGuidelines:0,userGuidelinesLengthLimit:1,updateUserGuideline:2},null,[-1,-1])}}function s1(r){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],s={};for(let o=0;o<n.length;o+=1)s=Mt(s,n[o]);return{c(){t=xt("svg"),e=new kn(!0),this.h()},l(o){t=_n(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=bn(t);e=Sn(a,!0),a.forEach(v),this.h()},h(){e.a=null,le(t,s)},m(o,a){Cn(o,t,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',t)},p(o,[a]){le(t,s=ve(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&a&&o[0]]))},i:D,o:D,d(o){o&&v(t)}}}function r1(r,t,e){return r.$$set=n=>{e(0,t=Mt(Mt({},t),se(n)))},[t=se(t)]}class o1 extends ot{constructor(t){super(),at(this,t,r1,s1,rt,{})}}function a1(r){let t;return{c(){t=I("Sign Out")},m(e,n){y(e,t,n)},d(e){e&&v(t)}}}function i1(r){let t,e;return t=new o1({props:{slot:"iconLeft"}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function l1(r){let t,e;return t=new vt({props:{loading:r[0],variant:"soft","data-testid":"sign-out-button",$$slots:{iconLeft:[i1],default:[a1]},$$scope:{ctx:r}}}),t.$on("click",r[1]),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,[s]){const o={};1&s&&(o.loading=n[0]),8&s&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function c1(r,t,e){let{onSignOut:n}=t,s=!1;return r.$$set=o=>{"onSignOut"in o&&e(2,n=o.onSignOut)},[s,function(){n(),e(0,s=!0)},n]}class d1 extends ot{constructor(t){super(),at(this,t,c1,l1,rt,{onSignOut:2})}}function u1(r){let t,e;return t=new Gu({props:{tools:r[5],onAuthenticate:r[20],onRevokeAccess:r[21],onToolApprovalConfigChange:r[24],onMCPServerAdd:r[27],onMCPServerSave:r[28],onMCPServerDelete:r[29],onMCPServerToggleDisable:r[30],onMCPServerJSONImport:r[31],isMCPEnabled:r[6]&&r[2].mcpServerList,isMCPImportEnabled:r[6]&&r[2].mcpServerImport,supportedShells:r[7].supportedShells,selectedShell:r[7].selectedShell,startupScript:r[7].startupScript,onShellSelect:r[22],onStartupScriptChange:r[23],isTerminalEnabled:r[2].terminal,isSoundCategoryEnabled:!0,isAgentCategoryEnabled:r[6],isSwarmModeFeatureFlagEnabled:r[8],hasEverUsedRemoteAgent:r[9]}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};32&s[0]&&(o.tools=n[5]),68&s[0]&&(o.isMCPEnabled=n[6]&&n[2].mcpServerList),68&s[0]&&(o.isMCPImportEnabled=n[6]&&n[2].mcpServerImport),128&s[0]&&(o.supportedShells=n[7].supportedShells),128&s[0]&&(o.selectedShell=n[7].selectedShell),128&s[0]&&(o.startupScript=n[7].startupScript),4&s[0]&&(o.isTerminalEnabled=n[2].terminal),64&s[0]&&(o.isAgentCategoryEnabled=n[6]),256&s[0]&&(o.isSwarmModeFeatureFlagEnabled=n[8]),512&s[0]&&(o.hasEverUsedRemoteAgent=n[9]),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function p1(r){let t,e;return t=new d1({props:{onSignOut:r[25]}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function m1(r){let t,e,n,s;const o=[h1,g1],a=[];function l(i,c){return i[2].rules?0:1}return t=l(r),e=a[t]=o[t](r),{c(){e.c(),n=ft()},m(i,c){a[t].m(i,c),y(i,n,c),s=!0},p(i,c){let d=t;t=l(i),t===d?a[t].p(i,c):(q(),$(a[d],1,1,()=>{a[d]=null}),B(),e=a[t],e?e.p(i,c):(e=a[t]=o[t](i),e.c()),m(e,1),e.m(n.parentNode,n))},i(i){s||(m(e),s=!0)},o(i){$(e),s=!1},d(i){i&&v(n),a[t].d(i)}}}function f1(r){let t,e;return t=new sl({}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p:D,i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function $1(r){return{c:D,m:D,p:D,i:D,o:D,d:D}}function g1(r){let t,e;return t=new Co({props:{userGuidelines:r[4],userGuidelinesLengthLimit:r[3],updateUserGuideline:r[19]}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};16&s[0]&&(o.userGuidelines=n[4]),8&s[0]&&(o.userGuidelinesLengthLimit=n[3]),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function h1(r){let t,e;return t=new n1({props:{userGuidelines:r[4],userGuidelinesLengthLimit:r[3],updateUserGuideline:r[19]}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};16&s[0]&&(o.userGuidelines=n[4]),8&s[0]&&(o.userGuidelinesLengthLimit=n[3]),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function v1(r){let t,e,n,s,o;const a=[$1,f1,m1,p1,u1],l=[];function i(c,d){var u,f,p;return 256&d[1]&&(e=null),e==null&&(e=!$o(c[39])),e?0:((u=c[39])==null?void 0:u.id)==="context"?1:((f=c[39])==null?void 0:f.id)==="guidelines"?2:((p=c[39])==null?void 0:p.id)==="account"?3:4}return n=i(r,[-1,-1]),s=l[n]=a[n](r),{c(){t=k("span"),s.c(),w(t,"slot","content")},m(c,d){y(c,t,d),l[n].m(t,null),o=!0},p(c,d){let u=n;n=i(c,d),n===u?l[n].p(c,d):(q(),$(l[u],1,1,()=>{l[u]=null}),B(),s=l[n],s?s.p(c,d):(s=l[n]=a[n](c),s.c()),m(s,1),s.m(t,null))},i(c){o||(m(s),o=!0)},o(c){$(s),o=!1},d(c){c&&v(t),l[n].d()}}}function y1(r){let t,e;return t=new Nl({props:{items:r[1],mode:"tree",class:"c-settings-navigation",selectedId:r[0],$$slots:{content:[v1,({item:n})=>({39:n}),({item:n})=>[0,n?256:0]]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(n,s){C(t,n,s),e=!0},p(n,s){const o={};2&s[0]&&(o.items=n[1]),1&s[0]&&(o.selectedId=n[0]),1020&s[0]|768&s[1]&&(o.$$scope={dirty:s,ctx:n}),t.$set(o)},i(n){e||(m(t.$$.fragment,n),e=!0)},o(n){$(t.$$.fragment,n),e=!1},d(n){x(t,n)}}}function w1(r){let t,e,n,s;return t=new da.Root({props:{$$slots:{default:[y1]},$$scope:{ctx:r}}}),{c(){_(t.$$.fragment)},m(o,a){C(t,o,a),e=!0,n||(s=It(window,"message",r[11].onMessageFromExtension),n=!0)},p(o,a){const l={};1023&a[0]|512&a[1]&&(l.$$scope={dirty:a,ctx:o}),t.$set(l)},i(o){e||(m(t.$$.fragment,o),e=!0)},o(o){$(t.$$.fragment,o),e=!1},d(o){x(t,o),n=!1,s()}}}function x1(r,t,e){let n,s,o,a,l,i,c,d,u,f;const p=new Ue(St),g=new on(St),h=new Mi(St),S=new Xr(St),P=new eo,N=new no(St,S,P),M=new jn(S),A=new en(S);Oe(jn.key,M),Oe(en.key,A),Oe(Ue.key,p),function(Z){Oe(yo,Z)}(N),function(Z){Oe(wo,Z)}(g);const E=p.getSettingsComponentSupported();Et(r,E,Z=>e(2,a=Z));const z=p.getEnableAgentMode();Et(r,z,Z=>e(6,c=Z));const G=p.getEnableAgentSwarmMode();Et(r,G,Z=>e(8,u=Z));const tt=p.getHasEverUsedRemoteAgent();Et(r,tt,Z=>e(9,f=Z)),S.registerConsumer(p),S.registerConsumer(g),S.registerConsumer(h);const O=h.getTerminalSettings();let F;Et(r,O,Z=>e(7,d=Z));const R={handleMessageFromExtension:Z=>!(!Z.data||Z.data.type!==ht.navigateToSettingsSection)&&(Z.data.data&&typeof Z.data.data=="string"&&e(0,F=Z.data.data),!0)};S.registerConsumer(R);const V=p.getDisplayableTools();Et(r,V,Z=>e(5,i=Z));const st=p.getGuidelines();return Et(r,st,Z=>e(26,l=Z)),Kr(()=>{p.dispose(),M.dispose(),A.dispose()}),p.notifyLoaded(),St.postMessage({type:ht.getOrientationStatus}),St.postMessage({type:ht.settingsPanelLoaded}),r.$$.update=()=>{var Z,J,j;********&r.$$.dirty[0]&&e(4,n=(Z=l.userGuidelines)==null?void 0:Z.contents),********&r.$$.dirty[0]&&e(3,s=(J=l.userGuidelines)==null?void 0:J.lengthLimit),4&r.$$.dirty[0]&&e(1,o=[a.remoteTools?un("Tools","",Il,"section-tools"):void 0,a.userGuidelines&&!a.rules?un("User Guidelines","Guidelines for Augment Chat to follow.",Ku,"guidelines"):void 0,a.rules?un("Rules and User Guidelines","",Qu,"guidelines"):void 0,a.workspaceContext?{name:"Context",description:"",icon:Pl,id:"context"}:void 0,un("Account","Manage your Augment account settings.",Qo,"account")].filter(Boolean)),3&r.$$.dirty[0]&&o.length>1&&!F&&e(0,F=(j=o[0])==null?void 0:j.id)},[F,o,a,s,n,i,c,d,u,f,g,S,E,z,G,tt,O,V,st,function(Z){const J=Z.trim();return!(s&&J.length>s)&&(p.updateLocalUserGuidelines(J),St.postMessage({type:ht.updateUserGuidelines,data:Z}),!0)},function(Z){St.postMessage({type:ht.toolConfigStartOAuth,data:{authUrl:Z}}),p.startPolling()},async function(Z){await N.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${Z.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&St.postMessage({type:ht.toolConfigRevokeAccess,data:{toolId:Z.identifier}})},function(Z){h.updateSelectedShell(Z)},function(Z){h.updateStartupScript(Z)},function(Z,J){St.postMessage({type:ht.toolApprovalConfigSetRequest,data:{toolId:Z,approvalConfig:J}})},function(){St.postMessage({type:ht.signOut})},l,Z=>g.addServer(Z),Z=>g.updateServer(Z),Z=>g.deleteServer(Z),Z=>g.toggleDisabledServer(Z),Z=>g.importServersFromJSON(Z)]}class C1 extends ot{constructor(t){super(),at(this,t,x1,w1,rt,{},null,[-1,-1])}}(async function(){St&&St.initialize&&await St.initialize(),new C1({target:document.getElementById("app")})})();
