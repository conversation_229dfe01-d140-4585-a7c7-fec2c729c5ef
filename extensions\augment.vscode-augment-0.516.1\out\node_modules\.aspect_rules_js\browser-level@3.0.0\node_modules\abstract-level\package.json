{"name": "abstract-level", "version": "3.1.0", "description": "Abstract class for a lexicographically sorted key-value database", "license": "MIT", "main": "index.js", "types": "./index.d.ts", "scripts": {"test": "standard && hallmark && (nyc -s node test/self.js | tap-arc) && nyc report", "test-pessimistic": "node test/self.js | tap-arc -pv", "test-browsers": "airtap --coverage test/self.js", "test-electron": "airtap -p electron --coverage test/self.js | tap-arc", "coverage": "nyc report -r lcovonly"}, "files": ["abstract-chained-batch.js", "abstract-iterator.js", "abstract-level.js", "abstract-snapshot.js", "index.js", "index.d.ts", "lib", "test", "types", "CHANGELOG.md", "UPGRADING.md"], "dependencies": {"buffer": "^6.0.3", "is-buffer": "^2.0.5", "level-supports": "^6.2.0", "level-transcoder": "^1.0.1", "maybe-combine-errors": "^1.0.0", "module-error": "^1.0.1"}, "devDependencies": {"@babel/preset-env": "^7.26.0", "@types/node": "^22.10.2", "@voxpelli/tsconfig": "^15.1.0", "airtap": "^5.0.0", "airtap-electron": "^1.0.0", "airtap-playwright": "^1.0.1", "babelify": "^10.0.0", "electron": "^33.2.1", "hallmark": "^5.0.1", "nyc": "^17.1.0", "standard": "^17.1.2", "tap-arc": "^1.3.2", "tape": "^5.9.0", "typescript": "^5.7.2"}, "repository": {"type": "git", "url": "https://github.com/Level/abstract-level.git"}, "homepage": "https://github.com/Level/abstract-level", "keywords": ["abstract-level", "level", "leveldb"], "engines": {"node": ">=18"}}