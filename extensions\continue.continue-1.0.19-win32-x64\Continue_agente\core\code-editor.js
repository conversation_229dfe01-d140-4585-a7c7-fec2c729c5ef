/**
 * Code Editor - Editor de Código Avançado
 * Sistema completo para edição inteligente de código
 */

const vscode = require('vscode');
const FileManager = require('./file-manager');

class CodeEditor {
    constructor() {
        this.fileManager = new FileManager();
        this.isInitialized = false;
        this.editHistory = [];
        this.undoStack = [];
        this.redoStack = [];
    }

    async initialize() {
        if (this.isInitialized) return;

        console.log('🔧 Inicializando Editor de Código...');
        
        await this.fileManager.initialize();
        this.setupEditingRules();
        
        this.isInitialized = true;
        console.log('✅ Editor de Código inicializado');
    }

    setupEditingRules() {
        this.editingRules = {
            // Regras de segurança
            alwaysBackup: true,
            validateSyntax: true,
            checkImpact: true,
            
            // Regras de formatação
            autoFormat: true,
            preserveIndentation: true,
            normalizeLineEndings: true,
            
            // Regras de qualidade
            removeTrailingSpaces: true,
            ensureFinalNewline: true,
            validateImports: true
        };
    }

    // Método principal para edição de código (similar ao str_replace_editor)
    async editCode(filePath, edits, options = {}) {
        try {
            // Validar entrada
            if (!filePath || !edits || !Array.isArray(edits)) {
                throw new Error('Parâmetros inválidos para edição');
            }

            // Ler arquivo atual
            const fileResult = await this.fileManager.readFile(filePath);
            if (!fileResult.success) {
                throw new Error(`Erro ao ler arquivo: ${fileResult.error}`);
            }

            let content = fileResult.content;
            const originalContent = content;
            const appliedEdits = [];

            // Aplicar edições em ordem reversa (para manter números de linha)
            const sortedEdits = edits.sort((a, b) => {
                if (a.type === 'str_replace' && b.type === 'str_replace') {
                    return b.old_str_start_line_number - a.old_str_start_line_number;
                }
                if (a.type === 'insert' && b.type === 'insert') {
                    return b.insert_line - a.insert_line;
                }
                return 0;
            });

            for (const edit of sortedEdits) {
                const result = await this.applyEdit(content, edit, filePath);
                if (result.success) {
                    content = result.content;
                    appliedEdits.push({
                        edit,
                        result: result.description
                    });
                } else {
                    throw new Error(`Erro ao aplicar edição: ${result.error}`);
                }
            }

            // Aplicar regras de formatação se habilitadas
            if (this.editingRules.autoFormat) {
                content = await this.formatCode(content, filePath);
            }

            // Validar sintaxe se habilitado
            if (this.editingRules.validateSyntax) {
                const validation = await this.validateSyntax(content, filePath);
                if (!validation.isValid) {
                    throw new Error(`Erro de sintaxe: ${validation.errors.join(', ')}`);
                }
            }

            // Salvar arquivo
            const writeResult = await this.fileManager.writeFile(filePath, content);
            if (!writeResult.success) {
                throw new Error(`Erro ao salvar arquivo: ${writeResult.error}`);
            }

            // Registrar na história
            this.recordEdit({
                filePath,
                originalContent,
                newContent: content,
                edits: appliedEdits,
                timestamp: new Date().toISOString()
            });

            return {
                success: true,
                filePath,
                editsApplied: appliedEdits.length,
                changes: appliedEdits,
                originalLines: originalContent.split('\n').length,
                newLines: content.split('\n').length,
                sizeDiff: content.length - originalContent.length
            };

        } catch (error) {
            console.error('Erro na edição de código:', error);
            return {
                success: false,
                error: error.message,
                filePath
            };
        }
    }

    async applyEdit(content, edit, filePath) {
        try {
            switch (edit.type) {
                case 'str_replace':
                    return await this.applyStringReplace(content, edit);
                case 'insert':
                    return await this.applyInsert(content, edit);
                default:
                    throw new Error(`Tipo de edição não suportado: ${edit.type}`);
            }
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async applyStringReplace(content, edit) {
        const {
            old_str,
            new_str,
            old_str_start_line_number,
            old_str_end_line_number
        } = edit;

        const lines = content.split('\n');
        
        // Validar números de linha
        if (old_str_start_line_number < 1 || old_str_end_line_number > lines.length) {
            throw new Error('Números de linha inválidos');
        }

        // Extrair texto a ser substituído
        const startIndex = old_str_start_line_number - 1;
        const endIndex = old_str_end_line_number - 1;
        const targetLines = lines.slice(startIndex, endIndex + 1);
        const targetText = targetLines.join('\n');

        // Verificar se o texto corresponde
        if (targetText !== old_str) {
            throw new Error(`Texto não corresponde. Esperado:\n${old_str}\nEncontrado:\n${targetText}`);
        }

        // Aplicar substituição
        const newLines = new_str.split('\n');
        lines.splice(startIndex, endIndex - startIndex + 1, ...newLines);

        return {
            success: true,
            content: lines.join('\n'),
            description: `Substituído ${endIndex - startIndex + 1} linha(s) por ${newLines.length} linha(s)`
        };
    }

    async applyInsert(content, edit) {
        const { insert_line, new_str } = edit;
        
        const lines = content.split('\n');
        
        // Validar número de linha
        if (insert_line < 0 || insert_line > lines.length) {
            throw new Error('Número de linha inválido para inserção');
        }

        // Inserir novo conteúdo
        const newLines = new_str.split('\n');
        lines.splice(insert_line, 0, ...newLines);

        return {
            success: true,
            content: lines.join('\n'),
            description: `Inserido ${newLines.length} linha(s) na linha ${insert_line}`
        };
    }

    // Métodos de formatação
    async formatCode(content, filePath) {
        try {
            const language = this.detectLanguage(filePath);
            
            switch (language) {
                case 'javascript':
                case 'typescript':
                    return this.formatJavaScript(content);
                case 'python':
                    return this.formatPython(content);
                case 'json':
                    return this.formatJSON(content);
                default:
                    return this.formatGeneric(content);
            }
        } catch (error) {
            console.error('Erro na formatação:', error);
            return content; // Retornar conteúdo original se formatação falhar
        }
    }

    formatJavaScript(content) {
        // Formatação básica para JavaScript
        let formatted = content;
        
        // Normalizar espaços em branco
        formatted = formatted.replace(/\t/g, '    '); // Converter tabs para espaços
        
        // Remover espaços em branco no final das linhas
        if (this.editingRules.removeTrailingSpaces) {
            formatted = formatted.replace(/[ \t]+$/gm, '');
        }
        
        // Garantir nova linha no final
        if (this.editingRules.ensureFinalNewline && !formatted.endsWith('\n')) {
            formatted += '\n';
        }
        
        return formatted;
    }

    formatPython(content) {
        // Formatação básica para Python
        let formatted = content;
        
        // Normalizar indentação (4 espaços)
        const lines = formatted.split('\n');
        const formattedLines = lines.map(line => {
            if (line.trim() === '') return '';
            
            // Contar indentação atual
            const match = line.match(/^(\s*)/);
            const currentIndent = match ? match[1] : '';
            const indentLevel = Math.floor(currentIndent.length / 4);
            
            // Aplicar indentação consistente
            const content = line.trim();
            return '    '.repeat(indentLevel) + content;
        });
        
        formatted = formattedLines.join('\n');
        
        // Remover espaços em branco no final das linhas
        if (this.editingRules.removeTrailingSpaces) {
            formatted = formatted.replace(/[ \t]+$/gm, '');
        }
        
        // Garantir nova linha no final
        if (this.editingRules.ensureFinalNewline && !formatted.endsWith('\n')) {
            formatted += '\n';
        }
        
        return formatted;
    }

    formatJSON(content) {
        try {
            const parsed = JSON.parse(content);
            return JSON.stringify(parsed, null, 2) + '\n';
        } catch (error) {
            return content; // Retornar original se não for JSON válido
        }
    }

    formatGeneric(content) {
        let formatted = content;
        
        // Aplicar regras básicas
        if (this.editingRules.removeTrailingSpaces) {
            formatted = formatted.replace(/[ \t]+$/gm, '');
        }
        
        if (this.editingRules.ensureFinalNewline && !formatted.endsWith('\n')) {
            formatted += '\n';
        }
        
        return formatted;
    }

    // Validação de sintaxe
    async validateSyntax(content, filePath) {
        const language = this.detectLanguage(filePath);
        
        switch (language) {
            case 'javascript':
            case 'typescript':
                return this.validateJavaScript(content);
            case 'python':
                return this.validatePython(content);
            case 'json':
                return this.validateJSON(content);
            default:
                return { isValid: true, errors: [] };
        }
    }

    validateJavaScript(content) {
        const errors = [];
        
        try {
            // Verificações básicas
            const openBraces = (content.match(/{/g) || []).length;
            const closeBraces = (content.match(/}/g) || []).length;
            
            if (openBraces !== closeBraces) {
                errors.push('Chaves não balanceadas');
            }

            const openParens = (content.match(/\(/g) || []).length;
            const closeParens = (content.match(/\)/g) || []).length;
            
            if (openParens !== closeParens) {
                errors.push('Parênteses não balanceados');
            }

            const openBrackets = (content.match(/\[/g) || []).length;
            const closeBrackets = (content.match(/\]/g) || []).length;
            
            if (openBrackets !== closeBrackets) {
                errors.push('Colchetes não balanceados');
            }

        } catch (error) {
            errors.push('Erro na validação de sintaxe: ' + error.message);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    validatePython(content) {
        const errors = [];
        
        try {
            const lines = content.split('\n');
            
            // Verificar indentação
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (line.trim() === '') continue;
                
                const match = line.match(/^(\s*)/);
                const indent = match ? match[1] : '';
                
                // Verificar se indentação é múltiplo de 4
                if (indent.length % 4 !== 0) {
                    errors.push(`Linha ${i + 1}: Indentação inconsistente`);
                }
            }

        } catch (error) {
            errors.push('Erro na validação de sintaxe: ' + error.message);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    validateJSON(content) {
        try {
            JSON.parse(content);
            return { isValid: true, errors: [] };
        } catch (error) {
            return {
                isValid: false,
                errors: [`JSON inválido: ${error.message}`]
            };
        }
    }

    // Utilitários
    detectLanguage(filePath) {
        const ext = filePath.split('.').pop().toLowerCase();
        
        const languageMap = {
            'js': 'javascript',
            'jsx': 'javascript',
            'ts': 'typescript',
            'tsx': 'typescript',
            'py': 'python',
            'json': 'json',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'md': 'markdown',
            'yaml': 'yaml',
            'yml': 'yaml'
        };

        return languageMap[ext] || 'text';
    }

    recordEdit(editRecord) {
        this.editHistory.push(editRecord);
        
        // Manter apenas os últimos 50 registros
        if (this.editHistory.length > 50) {
            this.editHistory.shift();
        }

        // Limpar redo stack quando nova edição é feita
        this.redoStack = [];
        
        // Adicionar ao undo stack
        this.undoStack.push(editRecord);
        if (this.undoStack.length > 20) {
            this.undoStack.shift();
        }
    }

    // Sistema de undo/redo
    async undo() {
        if (this.undoStack.length === 0) {
            return { success: false, message: 'Nada para desfazer' };
        }

        const lastEdit = this.undoStack.pop();
        
        try {
            // Restaurar conteúdo original
            const writeResult = await this.fileManager.writeFile(
                lastEdit.filePath, 
                lastEdit.originalContent
            );
            
            if (writeResult.success) {
                this.redoStack.push(lastEdit);
                return {
                    success: true,
                    message: `Desfeita edição em ${lastEdit.filePath}`,
                    filePath: lastEdit.filePath
                };
            } else {
                // Restaurar no stack se falhou
                this.undoStack.push(lastEdit);
                return {
                    success: false,
                    message: `Erro ao desfazer: ${writeResult.error}`
                };
            }
        } catch (error) {
            // Restaurar no stack se falhou
            this.undoStack.push(lastEdit);
            return {
                success: false,
                message: `Erro ao desfazer: ${error.message}`
            };
        }
    }

    async redo() {
        if (this.redoStack.length === 0) {
            return { success: false, message: 'Nada para refazer' };
        }

        const editToRedo = this.redoStack.pop();
        
        try {
            // Aplicar novamente o conteúdo editado
            const writeResult = await this.fileManager.writeFile(
                editToRedo.filePath, 
                editToRedo.newContent
            );
            
            if (writeResult.success) {
                this.undoStack.push(editToRedo);
                return {
                    success: true,
                    message: `Refeita edição em ${editToRedo.filePath}`,
                    filePath: editToRedo.filePath
                };
            } else {
                // Restaurar no stack se falhou
                this.redoStack.push(editToRedo);
                return {
                    success: false,
                    message: `Erro ao refazer: ${writeResult.error}`
                };
            }
        } catch (error) {
            // Restaurar no stack se falhou
            this.redoStack.push(editToRedo);
            return {
                success: false,
                message: `Erro ao refazer: ${error.message}`
            };
        }
    }

    // Métodos de conveniência
    async createFile(filePath, content = '') {
        return await this.fileManager.writeFile(filePath, content);
    }

    async deleteFile(filePath) {
        return await this.fileManager.deleteFile(filePath);
    }

    async copyFile(sourcePath, destPath) {
        return await this.fileManager.copyFile(sourcePath, destPath);
    }

    async moveFile(sourcePath, destPath) {
        return await this.fileManager.moveFile(sourcePath, destPath);
    }

    async readFile(filePath) {
        return await this.fileManager.readFile(filePath);
    }

    // Estatísticas e informações
    getEditHistory() {
        return this.editHistory.slice(); // Retornar cópia
    }

    getUndoRedoStatus() {
        return {
            canUndo: this.undoStack.length > 0,
            canRedo: this.redoStack.length > 0,
            undoCount: this.undoStack.length,
            redoCount: this.redoStack.length
        };
    }

    clearHistory() {
        this.editHistory = [];
        this.undoStack = [];
        this.redoStack = [];
    }
}

module.exports = CodeEditor;
