{"name": "continue-agent", "version": "1.0.0", "description": "Continue Agent - Advanced Programming Assistant", "main": "core/agent-main.js", "scripts": {"start": "node core/agent-main.js", "integrate": "node integrate-agent.js", "status": "node integrate-agent.js --status", "test": "echo 'Continue Agent - No tests specified'"}, "keywords": ["continue", "agent", "programming", "ai", "assistant", "vscode"], "author": "Continue Agent", "license": "MIT", "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "local"}}