<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agents</title>
    <script nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <script type="module" crossorigin src="./assets/remote-agent-home-veUFSyin.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-VfHtkDdv.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BQpWKoxZ.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-BlRCK7lJ.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-Cm1y2LK7.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-DxXjuHCW.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/index-B528snJk.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-zf3VV9pT.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/index-PzkfeRvH.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-C8wcdhmo.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-CMpdst0l.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-jvmj3vIU.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-BgK0UWCq.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-DHqqkJ4i.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-Dx3cBvPf.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-D5Z9LOF7.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-FeoFGSYm.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-D-yOSWp9.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-B23ZKhTC.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-pxiddGnV.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-home-CuWF5Lfd.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-panel-base-DEzma35W.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
