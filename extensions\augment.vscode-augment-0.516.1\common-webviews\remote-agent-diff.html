<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agent Diff</title>
    <script nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <script type="module" crossorigin src="./assets/remote-agent-diff-DeyMjNt5.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-VfHtkDdv.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BQpWKoxZ.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-BlRCK7lJ.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-Cm1y2LK7.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-DxXjuHCW.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-DMR40nRt.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/index-PzkfeRvH.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/index-6WVCg-U8.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BsnNYDaF.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/SimpleMonaco-JNVBjzXL.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-DfKvZ1Ug.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-BcgZeyRI.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-C64qkVNR.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-CMpdst0l.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-CRJIYorH.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-YT2PSBkc.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-CXmnYUii.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/index-B528snJk.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-BXmH3Ek-.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-BgK0UWCq.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-UFj4_Gis.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-CvTiczpm.js" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-Dx3cBvPf.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-D5Z9LOF7.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-FeoFGSYm.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-DTcQ2vsq.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-DokFokeT.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-B23ZKhTC.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-zn72hvQy.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMsGeQ5J.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-diff-BRoiVkRf.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-panel-base-DEzma35W.css" nonce="nonce-GSIBvlh54C3sHXwyznpawA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
