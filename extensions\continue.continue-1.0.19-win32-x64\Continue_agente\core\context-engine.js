/**
 * Context Engine - Motor de Contexto Avançado
 * Similar ao motor de contexto do Augment Agent
 */

const vscode = require('vscode');
const path = require('path');
const fs = require('fs');

class ContextEngine {
    constructor() {
        this.codebaseIndex = new Map();
        this.symbolMap = new Map();
        this.dependencyGraph = new Map();
        this.fileCache = new Map();
        this.isInitialized = false;
    }

    async initialize() {
        if (this.isInitialized) return;

        console.log('🔧 Inicializando Motor de Contexto...');
        
        try {
            // Indexar codebase
            await this.indexCodebase();
            
            // Construir mapa de símbolos
            await this.buildSymbolMap();
            
            // Mapear dependências
            await this.mapDependencies();
            
            this.isInitialized = true;
            console.log('✅ Motor de Contexto inicializado');
            
        } catch (error) {
            console.error('❌ Erro na inicialização do Motor de Contexto:', error);
        }
    }

    async indexCodebase() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) return;

        for (const folder of workspaceFolders) {
            await this.indexDirectory(folder.uri.fsPath);
        }
    }

    async indexDirectory(dirPath) {
        try {
            const entries = fs.readdirSync(dirPath, { withFileTypes: true });
            
            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);
                
                // Ignorar diretórios e arquivos específicos
                if (this.shouldIgnore(entry.name)) continue;
                
                if (entry.isDirectory()) {
                    await this.indexDirectory(fullPath);
                } else if (entry.isFile() && this.isCodeFile(entry.name)) {
                    await this.indexFile(fullPath);
                }
            }
        } catch (error) {
            console.error(`Erro ao indexar diretório ${dirPath}:`, error);
        }
    }

    async indexFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const relativePath = vscode.workspace.asRelativePath(filePath);
            
            // Extrair informações do arquivo
            const fileInfo = {
                path: relativePath,
                fullPath: filePath,
                content: content,
                language: this.detectLanguage(filePath),
                symbols: this.extractSymbols(content, filePath),
                imports: this.extractImports(content, filePath),
                exports: this.extractExports(content, filePath),
                functions: this.extractFunctions(content, filePath),
                classes: this.extractClasses(content, filePath),
                lastModified: fs.statSync(filePath).mtime
            };
            
            this.codebaseIndex.set(relativePath, fileInfo);
            
        } catch (error) {
            console.error(`Erro ao indexar arquivo ${filePath}:`, error);
        }
    }

    shouldIgnore(name) {
        const ignorePatterns = [
            'node_modules', '.git', '.vscode', 'dist', 'build', 
            '.next', '.nuxt', 'coverage', '.nyc_output',
            '__pycache__', '.pytest_cache', 'venv', 'env'
        ];
        
        return ignorePatterns.some(pattern => name.includes(pattern)) ||
               name.startsWith('.') && !name.endsWith('.js') && !name.endsWith('.ts');
    }

    isCodeFile(filename) {
        const codeExtensions = [
            '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp', 
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.less', '.vue', '.svelte',
            '.json', '.yaml', '.yml', '.xml', '.md'
        ];
        
        return codeExtensions.some(ext => filename.endsWith(ext));
    }

    detectLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        const languageMap = {
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascriptreact',
            '.tsx': 'typescriptreact',
            '.py': 'python',
            '.java': 'java',
            '.c': 'c',
            '.cpp': 'cpp',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown'
        };
        
        return languageMap[ext] || 'plaintext';
    }

    extractSymbols(content, filePath) {
        const language = this.detectLanguage(filePath);
        const symbols = [];
        
        switch (language) {
            case 'javascript':
            case 'typescript':
                symbols.push(...this.extractJSSymbols(content));
                break;
            case 'python':
                symbols.push(...this.extractPythonSymbols(content));
                break;
            case 'java':
                symbols.push(...this.extractJavaSymbols(content));
                break;
            default:
                symbols.push(...this.extractGenericSymbols(content));
        }
        
        return symbols;
    }

    extractJSSymbols(content) {
        const symbols = [];
        
        // Funções
        const functionRegex = /(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s+)?(?:\([^)]*\)\s*=>|\([^)]*\)\s*{)|(\w+)\s*:\s*(?:async\s+)?(?:function|\([^)]*\)\s*=>))/g;
        let match;
        while ((match = functionRegex.exec(content)) !== null) {
            const name = match[1] || match[2] || match[3];
            if (name) {
                symbols.push({
                    name,
                    type: 'function',
                    line: content.substring(0, match.index).split('\n').length
                });
            }
        }
        
        // Classes
        const classRegex = /class\s+(\w+)/g;
        while ((match = classRegex.exec(content)) !== null) {
            symbols.push({
                name: match[1],
                type: 'class',
                line: content.substring(0, match.index).split('\n').length
            });
        }
        
        // Variáveis
        const varRegex = /(?:const|let|var)\s+(\w+)/g;
        while ((match = varRegex.exec(content)) !== null) {
            symbols.push({
                name: match[1],
                type: 'variable',
                line: content.substring(0, match.index).split('\n').length
            });
        }
        
        return symbols;
    }

    extractPythonSymbols(content) {
        const symbols = [];
        
        // Funções
        const functionRegex = /def\s+(\w+)/g;
        let match;
        while ((match = functionRegex.exec(content)) !== null) {
            symbols.push({
                name: match[1],
                type: 'function',
                line: content.substring(0, match.index).split('\n').length
            });
        }
        
        // Classes
        const classRegex = /class\s+(\w+)/g;
        while ((match = classRegex.exec(content)) !== null) {
            symbols.push({
                name: match[1],
                type: 'class',
                line: content.substring(0, match.index).split('\n').length
            });
        }
        
        return symbols;
    }

    extractJavaSymbols(content) {
        const symbols = [];
        
        // Classes
        const classRegex = /(?:public\s+)?class\s+(\w+)/g;
        let match;
        while ((match = classRegex.exec(content)) !== null) {
            symbols.push({
                name: match[1],
                type: 'class',
                line: content.substring(0, match.index).split('\n').length
            });
        }
        
        // Métodos
        const methodRegex = /(?:public|private|protected)?\s*(?:static\s+)?(?:\w+\s+)?(\w+)\s*\([^)]*\)\s*{/g;
        while ((match = methodRegex.exec(content)) !== null) {
            symbols.push({
                name: match[1],
                type: 'method',
                line: content.substring(0, match.index).split('\n').length
            });
        }
        
        return symbols;
    }

    extractGenericSymbols(content) {
        const symbols = [];
        
        // Identificadores genéricos
        const identifierRegex = /\b([A-Z][a-zA-Z0-9_]*)\b/g;
        let match;
        while ((match = identifierRegex.exec(content)) !== null) {
            symbols.push({
                name: match[1],
                type: 'identifier',
                line: content.substring(0, match.index).split('\n').length
            });
        }
        
        return symbols;
    }

    extractImports(content, filePath) {
        const language = this.detectLanguage(filePath);
        const imports = [];
        
        switch (language) {
            case 'javascript':
            case 'typescript':
                // import statements
                const importRegex = /import\s+(?:{[^}]+}|\w+|\*\s+as\s+\w+)\s+from\s+['"]([^'"]+)['"]/g;
                let match;
                while ((match = importRegex.exec(content)) !== null) {
                    imports.push(match[1]);
                }
                
                // require statements
                const requireRegex = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
                while ((match = requireRegex.exec(content)) !== null) {
                    imports.push(match[1]);
                }
                break;
                
            case 'python':
                const pythonImportRegex = /(?:from\s+(\S+)\s+import|import\s+(\S+))/g;
                while ((match = pythonImportRegex.exec(content)) !== null) {
                    imports.push(match[1] || match[2]);
                }
                break;
        }
        
        return imports;
    }

    extractExports(content, filePath) {
        const language = this.detectLanguage(filePath);
        const exports = [];
        
        if (language === 'javascript' || language === 'typescript') {
            // export statements
            const exportRegex = /export\s+(?:default\s+)?(?:class|function|const|let|var)?\s*(\w+)/g;
            let match;
            while ((match = exportRegex.exec(content)) !== null) {
                exports.push(match[1]);
            }
        }
        
        return exports;
    }

    extractFunctions(content, filePath) {
        return this.extractSymbols(content, filePath).filter(symbol => 
            symbol.type === 'function' || symbol.type === 'method'
        );
    }

    extractClasses(content, filePath) {
        return this.extractSymbols(content, filePath).filter(symbol => 
            symbol.type === 'class'
        );
    }

    async buildSymbolMap() {
        for (const [filePath, fileInfo] of this.codebaseIndex) {
            for (const symbol of fileInfo.symbols) {
                if (!this.symbolMap.has(symbol.name)) {
                    this.symbolMap.set(symbol.name, []);
                }
                
                this.symbolMap.get(symbol.name).push({
                    file: filePath,
                    type: symbol.type,
                    line: symbol.line
                });
            }
        }
    }

    async mapDependencies() {
        for (const [filePath, fileInfo] of this.codebaseIndex) {
            const dependencies = [];
            
            for (const importPath of fileInfo.imports) {
                const resolvedPath = this.resolveImportPath(importPath, filePath);
                if (resolvedPath && this.codebaseIndex.has(resolvedPath)) {
                    dependencies.push(resolvedPath);
                }
            }
            
            this.dependencyGraph.set(filePath, dependencies);
        }
    }

    resolveImportPath(importPath, currentFile) {
        // Resolver caminhos relativos e absolutos
        if (importPath.startsWith('./') || importPath.startsWith('../')) {
            const currentDir = path.dirname(currentFile);
            return path.normalize(path.join(currentDir, importPath));
        }
        
        // Para imports de módulos, tentar encontrar no codebase
        for (const [filePath] of this.codebaseIndex) {
            if (filePath.includes(importPath)) {
                return filePath;
            }
        }
        
        return null;
    }

    async getRelevantContext(uri, code) {
        const filePath = vscode.workspace.asRelativePath(uri);
        const fileInfo = this.codebaseIndex.get(filePath);
        
        const context = {
            currentFile: fileInfo,
            relatedFiles: [],
            symbols: [],
            dependencies: []
        };
        
        if (fileInfo) {
            // Obter arquivos relacionados
            const dependencies = this.dependencyGraph.get(filePath) || [];
            for (const depPath of dependencies) {
                const depInfo = this.codebaseIndex.get(depPath);
                if (depInfo) {
                    context.relatedFiles.push(depInfo);
                }
            }
            
            // Obter símbolos relevantes
            const codeSymbols = this.extractSymbols(code, uri.fsPath);
            for (const symbol of codeSymbols) {
                const symbolInfo = this.symbolMap.get(symbol.name) || [];
                context.symbols.push(...symbolInfo);
            }
        }
        
        return context;
    }

    async retrieveFromCodebase(query) {
        const results = [];
        const queryLower = query.toLowerCase();
        
        for (const [filePath, fileInfo] of this.codebaseIndex) {
            // Buscar no conteúdo do arquivo
            if (fileInfo.content.toLowerCase().includes(queryLower)) {
                const lines = fileInfo.content.split('\n');
                const matchingLines = [];
                
                lines.forEach((line, index) => {
                    if (line.toLowerCase().includes(queryLower)) {
                        matchingLines.push({
                            line: index + 1,
                            content: line.trim()
                        });
                    }
                });
                
                if (matchingLines.length > 0) {
                    results.push({
                        file: filePath,
                        matches: matchingLines.slice(0, 5), // Limitar a 5 matches por arquivo
                        code: matchingLines.map(m => `${m.line}: ${m.content}`).join('\n')
                    });
                }
            }
            
            // Buscar nos símbolos
            for (const symbol of fileInfo.symbols) {
                if (symbol.name.toLowerCase().includes(queryLower)) {
                    results.push({
                        file: filePath,
                        symbol: symbol.name,
                        type: symbol.type,
                        line: symbol.line,
                        code: `${symbol.type}: ${symbol.name} (linha ${symbol.line})`
                    });
                }
            }
        }
        
        return results.slice(0, 25); // Limitar a 25 resultados
    }
}

module.exports = ContextEngine;
